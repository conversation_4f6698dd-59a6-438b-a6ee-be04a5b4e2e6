{"name": "crow-ai", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "vite build", "build:server": "esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "preview": "vite preview", "deploy": "npm run build && wrangler pages deploy dist/public"}, "dependencies": {"@duckdb/duckdb-wasm": "^1.29.1-dev132.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.57.4", "@tanstack/react-query": "^5.60.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.17", "@types/dompurify": "^3.0.5", "@types/lodash": "^4.17.15", "@types/multer": "^2.0.0", "axios": "^1.8.1", "better-sqlite3": "^12.2.0", "chalk": "^4.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "cookie": "^1.0.2", "cors": "^2.8.5", "date-fns": "^3.6.0", "dompurify": "^3.2.4", "dotenv": "^16.4.7", "drizzle-orm": "^0.44.5", "drizzle-zod": "^0.7.0", "duckdb": "^1.3.2", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.2.4", "lodash": "^4.17.21", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "multer": "^2.0.2", "openai": "^4.85.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.13.3", "puppeteer": "^24.4.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "recharts": "^2.13.0", "sharp": "^0.34.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "use-debounce": "^10.0.4", "vaul": "^1.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "yargs": "^17.7.2", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.24.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.9"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}