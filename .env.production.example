# 🚀 Crow-AI Forum - Cloudflare Pages Production Environment Variables
# This file contains the exact environment variables needed for Cloudflare Pages deployment
# Set these in Cloudflare Pages Dashboard > Settings > Environment variables

# ================================
# 🌍 REQUIRED PRODUCTION VARIABLES
# ================================

# Node environment (REQUIRED)
NODE_ENV=production

# Supabase configuration (REQUIRED)
SUPABASE_URL=https://zfstxixesisigvqpycuw.supabase.co
SUPABASE_ANON_KEY=[GET_FROM_SUPABASE_DASHBOARD]
SUPABASE_SERVICE_ROLE_KEY=[GET_FROM_SUPABASE_DASHBOARD]

# Session security (REQUIRED)
# Generate with: openssl rand -base64 32
SESSION_SECRET=[GENERATE_SECURE_RANDOM_STRING]

# ================================
# 🔧 CLOUDFLARE PAGES SETUP INSTRUCTIONS
# ================================

# 1. Go to Cloudflare Pages Dashboard
# 2. Select your crow-ai-forum project
# 3. Go to Settings > Environment variables
# 4. Add each variable below as a "Production" environment variable

# ================================
# 📋 VARIABLES TO SET IN CLOUDFLARE DASHBOARD
# ================================

# Variable Name: NODE_ENV
# Value: production
# Environment: Production

# Variable Name: SUPABASE_URL
# Value: https://zfstxixesisigvqpycuw.supabase.co
# Environment: Production

# Variable Name: SUPABASE_ANON_KEY
# Value: [Copy from Supabase Dashboard > Settings > API > anon public key]
# Environment: Production

# Variable Name: SUPABASE_SERVICE_ROLE_KEY
# Value: [Copy from Supabase Dashboard > Settings > API > service_role secret key]
# Environment: Production
# ⚠️ CRITICAL: This is a secret key - handle with care

# Variable Name: SESSION_SECRET
# Value: [Generate with: openssl rand -base64 32]
# Environment: Production
# ⚠️ CRITICAL: Must be a strong, unique secret

# ================================
# 🤖 OPTIONAL AI VARIABLES
# ================================

# Variable Name: DEEPSEEK_API_KEY (Optional)
# Value: [Your DeepSeek API key from https://platform.deepseek.com/api_keys]
# Environment: Production

# Variable Name: OPENROUTER_API_KEY (Optional)
# Value: [Your OpenRouter API key from https://openrouter.ai/keys]
# Environment: Production

# ================================
# ✅ VERIFICATION CHECKLIST
# ================================

# After setting all variables in Cloudflare Pages:
# □ NODE_ENV is set to "production"
# □ SUPABASE_URL matches your Supabase project URL
# □ SUPABASE_ANON_KEY is the correct anon public key
# □ SUPABASE_SERVICE_ROLE_KEY is the correct service role secret
# □ SESSION_SECRET is a strong, unique random string
# □ All variables are set for "Production" environment
# □ No variables contain placeholder text like [GET_FROM_SUPABASE_DASHBOARD]

# ================================
# 🚀 DEPLOYMENT READY
# ================================

# Once all variables are set, you can deploy with:
# npm run deploy

# The application will be available at:
# https://crow-ai.net
