name = "crow-ai-forum"
compatibility_date = "2024-01-01"

[build]
command = "npm run build"
cwd = "."

[build.environment_variables]
NODE_ENV = "production"

# Environment variables for production
[env.production.vars]
NODE_ENV = "production"
SUPABASE_URL = "https://zfstxixesisigvqpycuw.supabase.co"

# Secrets (set via Cloudflare dashboard or wrangler secret)
# SUPABASE_ANON_KEY
# SUPABASE_SERVICE_ROLE_KEY
# SESSION_SECRET
# DEEPSEEK_API_KEY (optional)
# OPENROUTER_API_KEY (optional)

[[pages_build_output_dir]]
value = "dist/public"
