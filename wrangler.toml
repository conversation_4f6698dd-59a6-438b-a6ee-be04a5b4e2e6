name = "crow-ai-forum"
compatibility_date = "2024-09-18"
pages_build_output_dir = "dist"

[build]
command = "npm run build"

# Pages Functions routing
[[pages.rules]]
rule = "/*"
function = "api/[[path]]"

# Environment variables for production
[env.production.vars]
NODE_ENV = "production"
SUPABASE_URL = "https://zfstxixesisigvqpycuw.supabase.co"

# Secrets (set via Cloudflare dashboard or wrangler secret)
# SUPABASE_ANON_KEY - Required: Supabase anonymous key for client-side operations
# SUPABASE_SERVICE_ROLE_KEY - Required: Supabase service role key for server-side operations
# SESSION_SECRET - Required: Secure random string for session encryption
# DEEPSEEK_API_KEY - Optional: API key for DeepSeek AI integration
# OPENROUTER_API_KEY - Optional: API key for OpenRouter AI integration

# Note: All secrets must be set in Cloudflare Pages dashboard under Settings > Environment variables
# Generate SESSION_SECRET with: openssl rand -base64 32
