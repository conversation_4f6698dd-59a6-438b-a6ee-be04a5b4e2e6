import { integer, sqliteTable, text, real } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { z } from "zod";

// DuckDB doesn't have native enums like PostgreSQL, so we'll use check constraints or handle validation in code
// For now, we'll use text fields with validation in Zod schemas

// Export enums for backward compatibility with client-side code
export const threadCategoryEnum = {
  enumValues: ['AI_NEWS', 'GENERAL', 'GLOBAL_NEWS', 'TECHNICAL_SUPPORT', 'FUNNY'] as const
};

export const userEventTypeEnum = {
  enumValues: ['account_creation', 'login', 'password_change', 'suspension', 'ban',
              'unsuspension', 'unban', 'thread_create', 'admin_toggle_admin',
              'admin_suspend_user', 'admin_ban_user', 'admin_unsuspend_user',
              'admin_unban_user', 'admin_view_details', 'admin_delete_user'] as const
};

export const notificationTypeEnum = {
  enumValues: ['message', 'thread_reply', 'thread_like', 'mention', 'admin_action',
              'system', 'news', 'friend_request', 'friend_accept'] as const
};

export const friendRequestStatusEnum = {
  enumValues: ['pending', 'accepted', 'rejected'] as const
};

// Update users table with suspension, ban fields and public key for E2EE
export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  isAdmin: integer("is_admin", { mode: 'boolean' }).notNull().default(false),
  avatar: text("avatar"),
  bio: text("bio").default(""),
  // Public key for end-to-end encryption
  publicKey: text("public_key"),
  // Auto-encryption settings
  autoEncryptionEnabled: integer("auto_encryption_enabled", { mode: 'boolean' }).notNull().default(true),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now()),
  lastPasswordChange: real("last_password_change").notNull().$defaultFn(() => Date.now()),
  creationIp: text("creation_ip"),
  lastLoginIp: text("last_login_ip"),
  lastLoginAt: real("last_login_at"),
  suspendedUntil: real("suspended_until"),
  banReason: text("ban_reason"),
  bannedAt: real("banned_at")
});

// Update userEvents table to include admin-specific fields
export const userEvents = sqliteTable("user_events", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: text("type").notNull(), // Will validate in code: 'account_creation', 'login', etc.
  timestamp: real("timestamp").notNull().$defaultFn(() => Date.now()),
  details: text("details").notNull(),
  ip: text("ip"),
  reason: text("reason"),
  adminId: integer("admin_id").references(() => users.id, { onDelete: 'set null' }),
  metadata: text("metadata")
});

// Add chatRoomMessages table after the messages table
export const chatRoomMessages = sqliteTable("chat_room_messages", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  content: text("content").notNull(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  roomId: text("room_id").notNull(),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

export const messages = sqliteTable("messages", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  content: text("content").notNull(),  // Can store plain text or encrypted content
  nonce: text("nonce").default(""),    // Stores the nonce used for encryption (empty for unencrypted)
  isEncrypted: integer("is_encrypted", { mode: 'boolean' }).notNull().default(false),  // Flag to identify E2EE messages
  senderId: integer("sender_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  recipientId: integer("recipient_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

export const threads = sqliteTable("threads", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  title: text("title").notNull(),
  content: text("content").notNull(),
  authorId: integer("author_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  category: text("category").default('GENERAL'), // Will validate: 'AI_NEWS', 'GENERAL', 'GLOBAL_NEWS', 'TECHNICAL_SUPPORT', 'FUNNY'
  isDeleted: integer("is_deleted", { mode: 'boolean' }).notNull().default(false),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

export const replies = sqliteTable("replies", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  content: text("content").notNull(),
  threadId: integer("thread_id").notNull().references(() => threads.id, { onDelete: 'cascade' }),
  authorId: integer("author_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

export const votes = sqliteTable("votes", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  threadId: integer("thread_id").notNull().references(() => threads.id, { onDelete: 'cascade' }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  isUpvote: integer("is_upvote", { mode: 'boolean' }).notNull(),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

// AI chat sessions to group related messages
export const aiChatSessions = sqliteTable("ai_chat_sessions", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: text("title").notNull(),
  provider: text("provider").notNull().default('deepseek'), // 'deepseek' or 'openrouter'
  model: text("model").notNull().default('deepseek-chat'),
  totalTokens: integer("total_tokens").notNull().default(0),
  maxTokens: integer("max_tokens").notNull().default(64000),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now()),
  updatedAt: real("updated_at").notNull().$defaultFn(() => Date.now())
});

export const aiChats = sqliteTable("ai_chats", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  sessionId: integer("session_id").references(() => aiChatSessions.id, { onDelete: 'cascade' }),
  message: text("message").notNull(),
  response: text("response").notNull(),
  isDeepThinking: integer("is_deep_thinking", { mode: 'boolean' }).notNull().default(false),
  promptTokens: integer("prompt_tokens").notNull().default(0),
  completionTokens: integer("completion_tokens").notNull().default(0),
  totalTokens: integer("total_tokens").notNull().default(0),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

// Notifications table
export const notifications = sqliteTable("notifications", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: text("type").notNull(), // Will validate: 'message', 'thread_reply', 'thread_like', etc.
  title: text("title").notNull(),
  message: text("message").notNull(),
  isRead: integer("is_read", { mode: 'boolean' }).notNull().default(false),
  relatedId: integer("related_id"), // ID of related thread, message, etc.
  relatedType: text("related_type"), // 'thread', 'message', 'user', etc.
  actionUrl: text("action_url"), // URL to navigate when clicked
  senderId: integer("sender_id").references(() => users.id, { onDelete: 'set null' }),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now())
});

// News table for admin announcements
export const news = sqliteTable("news", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  title: text("title").notNull(),
  content: text("content").notNull(),
  authorId: integer("author_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  isPublished: integer("is_published", { mode: 'boolean' }).notNull().default(false),
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now()),
  publishedAt: real("published_at")
});

// Friend requests table
export const friendRequests = sqliteTable("friend_requests", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  requesterId: integer("requester_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  receiverId: integer("receiver_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  status: text("status").notNull().default('pending'), // Will validate: 'pending', 'accepted', 'rejected'
  createdAt: real("created_at").notNull().$defaultFn(() => Date.now()),
  updatedAt: real("updated_at").notNull().$defaultFn(() => Date.now())
});

// Define relations
export const usersRelations = relations(users, ({ many }) => ({
  threads: many(threads),
  replies: many(replies),
  votes: many(votes),
  aiChats: many(aiChats),
  messages: many(messages),
  chatRoomMessages: many(chatRoomMessages),
  events: many(userEvents),
  notifications: many(notifications),
  sentNotifications: many(notifications, { relationName: "sender" }),
  newsArticles: many(news),
  sentFriendRequests: many(friendRequests, { relationName: "requester" }),
  receivedFriendRequests: many(friendRequests, { relationName: "receiver" })
}));

export const threadsRelations = relations(threads, ({ one, many }) => ({
  author: one(users, {
    fields: [threads.authorId],
    references: [users.id]
  }),
  replies: many(replies),
  votes: many(votes)
}));

export const repliesRelations = relations(replies, ({ one }) => ({
  thread: one(threads, {
    fields: [replies.threadId],
    references: [threads.id]
  }),
  author: one(users, {
    fields: [replies.authorId],
    references: [users.id]
  })
}));

export const votesRelations = relations(votes, ({ one }) => ({
  thread: one(threads, {
    fields: [votes.threadId],
    references: [threads.id]
  }),
  user: one(users, {
    fields: [votes.userId],
    references: [users.id]
  })
}));

export const aiChatSessionsRelations = relations(aiChatSessions, ({ one, many }) => ({
  user: one(users, {
    fields: [aiChatSessions.userId],
    references: [users.id]
  }),
  chats: many(aiChats)
}));

export const aiChatsRelations = relations(aiChats, ({ one }) => ({
  user: one(users, {
    fields: [aiChats.userId],
    references: [users.id]
  }),
  session: one(aiChatSessions, {
    fields: [aiChats.sessionId],
    references: [aiChatSessions.id]
  })
}));

export const messagesRelations = relations(messages, ({ one }) => ({
  sender: one(users, { fields: [messages.senderId], references: [users.id] }),
  recipient: one(users, { fields: [messages.recipientId], references: [users.id] })
}));

// Add relations for chatRoomMessages
export const chatRoomMessagesRelations = relations(chatRoomMessages, ({ one }) => ({
  user: one(users, {
    fields: [chatRoomMessages.userId],
    references: [users.id]
  })
}));

// Add relations for userEvents
export const userEventsRelations = relations(userEvents, ({ one }) => ({
  user: one(users, {
    fields: [userEvents.userId],
    references: [users.id]
  })
}));

// Add relations for notifications
export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id]
  }),
  sender: one(users, {
    fields: [notifications.senderId],
    references: [users.id],
    relationName: "sender"
  })
}));

// Add relations for news
export const newsRelations = relations(news, ({ one }) => ({
  author: one(users, {
    fields: [news.authorId],
    references: [users.id]
  })
}));

// Add relations for friend requests
export const friendRequestsRelations = relations(friendRequests, ({ one }) => ({
  requester: one(users, {
    fields: [friendRequests.requesterId],
    references: [users.id],
    relationName: "requester"
  }),
  receiver: one(users, {
    fields: [friendRequests.receiverId],
    references: [users.id],
    relationName: "receiver"
  })
}));

// Update the user schema types
export const insertUserSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  publicKey: z.string().optional(),  // Public key for E2EE
  creationIp: z.string().optional(),
});

// List of words to filter out - only the most severe slurs
const FILTERED_WORDS = [
  'nigger', 'nigga', 'faggot', 'retard'
];

// Custom error class for content filtering
class ContentFilterError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ContentFilterError';
  }
}

// Content filter function that uses Zod's addIssue instead of throwing
const contentFilter = (value: string, ctx: z.RefinementCtx) => {
  const lowerCaseValue = value.toLowerCase();
  
  // Check for filtered words with word boundaries
  const matchedWord = FILTERED_WORDS.find(word => {
    const regex = new RegExp(`\\b${word}\\b|\\b${word}s\\b|\\b${word}ing\\b`, 'i');
    return regex.test(lowerCaseValue);
  });
  
  if (matchedWord) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again.",
      path: []
    });
    return;
  }
  
  return value;
};

export const insertThreadSchema = z.object({
  title: z.string()
    .min(1, "Title is required")
    .max(100, "Title must be 100 characters or less")
    .refine((val) => {
      const lowerCaseValue = val.toLowerCase();
      const matchedWord = FILTERED_WORDS.find(word => {
        const regex = new RegExp(`\\b${word}\\b|\\b${word}s\\b|\\b${word}ing\\b`, 'i');
        return regex.test(lowerCaseValue);
      });
      return !matchedWord;
    }, {
      message: "Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again."
    }),
  content: z.string()
    .min(1, "Content is required")
    .max(10000, "Content must be 10,000 characters or less")
    .refine((val) => {
      const lowerCaseValue = val.toLowerCase();
      const matchedWord = FILTERED_WORDS.find(word => {
        const regex = new RegExp(`\\b${word}\\b|\\b${word}s\\b|\\b${word}ing\\b`, 'i');
        return regex.test(lowerCaseValue);
      });
      return !matchedWord;
    }, {
      message: "Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again."
    }),
  category: z.enum(['AI_NEWS', 'GENERAL', 'GLOBAL_NEWS', 'TECHNICAL_SUPPORT', 'FUNNY'])
}).required();

export const insertReplySchema = z.object({
  content: z.string()
    .min(1, "Content is required")
    .max(5000, "Content must be 5,000 characters or less")
    .refine((val) => {
      const lowerCaseValue = val.toLowerCase();
      const matchedWord = FILTERED_WORDS.find(word => {
        const regex = new RegExp(`\\b${word}\\b|\\b${word}s\\b|\\b${word}ing\\b`, 'i');
        return regex.test(lowerCaseValue);
      });
      return !matchedWord;
    }, {
      message: "Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again."
    }),
});

export const insertVoteSchema = z.object({
  isUpvote: z.boolean()
});

export const insertAIChatSessionSchema = z.object({
  title: z.string().min(1, "Title is required").default("New Chat"),
  provider: z.string().default('deepseek'),
  model: z.string().default('deepseek-chat')
});

export const insertAIChatSchema = z.object({
  message: z.string().min(1, "Message is required"),
  sessionId: z.number().int().positive().optional(),
  isDeepThinking: z.boolean().default(false),
});

export const insertMessageSchema = z.object({
  senderId: z.number().int().positive(),
  recipientId: z.number().int().positive(),
  content: z.string()
    .min(1, "Content is required")
    .max(10000, "Message must be 10,000 characters or less"), // Increased limit for encrypted content
  nonce: z.string().optional().default(""),
  isEncrypted: z.boolean().default(false)
});

// Add new schema for chat room messages
export const insertChatRoomMessageSchema = z.object({
  content: z.string()
    .min(1, "Message content is required")
    .max(2000, "Message must be 2,000 characters or less")
    .transform(contentFilter),
  roomId: z.string().min(1, "Room ID is required")
});

// Notification schema
export const insertNotificationSchema = z.object({
  userId: z.number().int().positive(),
  type: z.enum(['message', 'thread_reply', 'thread_like', 'mention', 'admin_action', 'system', 'news', 'friend_request', 'friend_accept']),
  title: z.string().min(1, "Title is required").max(100, "Title must be 100 characters or less"),
  message: z.string().min(1, "Message is required").max(500, "Message must be 500 characters or less"),
  relatedId: z.number().int().positive().optional(),
  relatedType: z.string().optional(),
  actionUrl: z.string().optional(),
  senderId: z.number().int().positive().optional()
});

// News schema
export const insertNewsSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title must be 200 characters or less"),
  content: z.string().min(1, "Content is required").max(5000, "Content must be 5,000 characters or less"),
  isPublished: z.boolean().default(false)
});

// Friend request schema
export const insertFriendRequestSchema = z.object({
  receiverId: z.number().int().positive()
});

// Bio update schema
export const updateBioSchema = z.object({
  bio: z.string().max(500, "Bio must be 500 characters or less").optional()
});

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Thread = typeof threads.$inferSelect;
export type InsertThread = z.infer<typeof insertThreadSchema>;
export type Reply = typeof replies.$inferSelect;
export type InsertReply = z.infer<typeof insertReplySchema>;
export type Vote = typeof votes.$inferSelect;
export type InsertVote = z.infer<typeof insertVoteSchema>;
export type AIChat = typeof aiChats.$inferSelect;
export type InsertAIChat = z.infer<typeof insertAIChatSchema>;
export type AIChatSession = typeof aiChatSessions.$inferSelect;
export type InsertAIChatSession = z.infer<typeof insertAIChatSessionSchema>;
export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;
// Add new type exports at the bottom
export type ChatRoomMessage = typeof chatRoomMessages.$inferSelect;
export type InsertChatRoomMessage = z.infer<typeof insertChatRoomMessageSchema>;
export type UserEvent = typeof userEvents.$inferSelect;
export type Notification = typeof notifications.$inferSelect;
export type InsertNotification = z.infer<typeof insertNotificationSchema>;
export type News = typeof news.$inferSelect;
export type InsertNews = z.infer<typeof insertNewsSchema>;
export type FriendRequest = typeof friendRequests.$inferSelect;
export type InsertFriendRequest = z.infer<typeof insertFriendRequestSchema>;
export type UpdateBio = z.infer<typeof updateBioSchema>;