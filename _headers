# Security Headers for Crow-AI.net Production
# This file configures security headers for Cloudflare Pages deployment
# Based on production readiness requirements from Notion documentation

# Global security headers for all routes
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  X-XSS-Protection: 1; mode=block
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), vr=(), accelerometer=(), gyroscope=(), magnetometer=(), clipboard-read=(), clipboard-write=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# Content Security Policy for the main application
/app/*
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://zfstxixesisigvqpycuw.supabase.co wss://zfstxixesisigvqpycuw.supabase.co; frame-ancestors 'none'; font-src 'self' data:; object-src 'none'; base-uri 'self'; form-action 'self';

# API routes security
/api/*
  Content-Security-Policy: default-src 'none'; connect-src 'self' https://zfstxixesisigvqpycuw.supabase.co;
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY

# Hide preview URLs from search engines
https://:project.pages.dev/*
  X-Robots-Tag: noindex, nofollow, nosnippet, noarchive, noimageindex

# Cache static assets aggressively
/assets/*
  Cache-Control: public, max-age=31556952, immutable
  X-Content-Type-Options: nosniff

# Cache CSS files
/*.css
  Cache-Control: public, max-age=31556952
  X-Content-Type-Options: nosniff

# Cache JavaScript files
/*.js
  Cache-Control: public, max-age=31556952
  X-Content-Type-Options: nosniff

# Cache font files
/*.woff2
  Cache-Control: public, max-age=31556952
  X-Content-Type-Options: nosniff

/*.woff
  Cache-Control: public, max-age=31556952
  X-Content-Type-Options: nosniff

/*.ttf
  Cache-Control: public, max-age=31556952
  X-Content-Type-Options: nosniff

# Cache image files
/*.png
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

/*.jpg
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

/*.jpeg
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

/*.gif
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

/*.svg
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

/*.webp
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

/*.ico
  Cache-Control: public, max-age=31556952
  X-Content-Type-Options: nosniff

# Security for HTML files
/*.html
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin

# JSON API responses
/*.json
  Content-Type: application/json; charset=utf-8
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY

# Manifest and service worker files
/manifest.json
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff

/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  X-Content-Type-Options: nosniff

# Root level files
/robots.txt
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff

/sitemap.xml
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff
