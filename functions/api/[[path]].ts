// Cloudflare Pages Functions handler for API routes
import { registerRoutes } from '../../server/routes';
import express from 'express';

// Create Express app for API handling
const app = express();

// Register all routes
registerRoutes(app);

// Export handler for Cloudflare Pages Functions
export const onRequest: PagesFunction = async (context) => {
  const { request } = context;
  
  // Convert Cloudflare request to Express-compatible request
  const url = new URL(request.url);
  const path = url.pathname;
  
  // Handle API routes through Express app
  if (path.startsWith('/api/')) {
    return new Promise((resolve) => {
      const req = {
        method: request.method,
        url: path + url.search,
        headers: Object.fromEntries(request.headers.entries()),
        body: request.body,
      };
      
      const res = {
        status: (code: number) => ({ json: (data: any) => resolve(Response.json(data, { status: code })) }),
        json: (data: any) => resolve(Response.json(data)),
        send: (data: any) => resolve(new Response(data)),
        sendStatus: (code: number) => resolve(new Response('', { status: code })),
      };
      
      // Route through Express app
      app(req as any, res as any, () => {
        resolve(new Response('Not Found', { status: 404 }));
      });
    });
  }
  
  // For non-API routes, let Cloudflare Pages handle static files
  return new Response('Not Found', { status: 404 });
};
