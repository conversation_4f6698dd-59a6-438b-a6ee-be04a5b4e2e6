import { createClient } from '@supabase/supabase-js';

// Supabase configuration from environment variables
const supabaseUrl = process.env.SUPABASE_URL || 'https://zfstxixesisigvqpycuw.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgxNTMzNjMsImV4cCI6MjA3MzcyOTM2M30.xAQRT4uOVUXMr7cf6aGBdrgeXpM6y5nsM3pS7O-uxLg';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODE1MzM2MywiZXhwIjoyMDczNzI5MzYzfQ.YU4Z8FmivHEimg1hV4fpcdy5lTA3ikno2hSHl5jvVog';

// Client for public operations (with RLS)
export const supabase = createClient(supabaseUrl, supabase<PERSON><PERSON>Key, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
  },
});

// Admin client for server-side operations (bypasses RLS)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Database types based on our schema
export interface DatabaseUser {
  id: number;
  username: string;
  password: string;
  is_admin: boolean;
  avatar?: string;
  bio: string;
  public_key?: string;
  auto_encryption_enabled: boolean;
  created_at: string;
  last_password_change: string;
  suspended_until?: string;
  banned_at?: string;
  ban_reason?: string;
  last_login?: string;
  last_ip?: string;
  login_count: number;
  auth_id?: string;
}

export interface DatabaseThread {
  id: number;
  title: string;
  content: string;
  author_id: number;
  category: string;
  is_deleted: boolean;
  created_at: string;
}

export interface DatabaseReply {
  id: number;
  content: string;
  thread_id: number;
  author_id: number;
  is_deleted: boolean;
  created_at: string;
}

export interface DatabaseMessage {
  id: number;
  content: string;
  nonce: string;
  is_encrypted: boolean;
  sender_id: number;
  recipient_id: number;
  created_at: string;
}

export interface DatabaseAIChat {
  id: number;
  user_id: number;
  session_id?: number;
  message: string;
  response: string;
  tokens_used: number;
  created_at: string;
}

export interface DatabaseAIChatSession {
  id: number;
  user_id: number;
  title: string;
  provider: string;
  model: string;
  total_tokens: number;
  max_tokens: number;
  created_at: string;
  updated_at: string;
}

// Helper function to handle Supabase errors
export function handleSupabaseError(error: any, operation: string) {
  console.error(`Supabase ${operation} error:`, error);
  throw new Error(`Database ${operation} failed: ${error.message}`);
}

// Helper function to get user by auth ID
export async function getUserByAuthId(authId: string): Promise<DatabaseUser | null> {
  const { data, error } = await supabaseAdmin
    .from('users')
    .select('*')
    .eq('auth_id', authId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') return null; // No rows returned
    handleSupabaseError(error, 'getUserByAuthId');
  }

  return data;
}

// Helper function to create user with auth ID
export async function createUserWithAuthId(userData: Partial<DatabaseUser>): Promise<DatabaseUser> {
  const { data, error } = await supabaseAdmin
    .from('users')
    .insert(userData)
    .select()
    .single();

  if (error) {
    handleSupabaseError(error, 'createUserWithAuthId');
  }

  return data;
}
