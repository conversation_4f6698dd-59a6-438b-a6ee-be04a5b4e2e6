import { users, userEvents, type User, type InsertUser } from "@shared/schema";
import { threads, type Thread, type InsertThread } from "@shared/schema";
import { replies, type Reply, type InsertReply } from "@shared/schema";
import { votes, type Vote, type InsertVote } from "@shared/schema";
import { aiChats, aiChatSessions, type AIChat, type InsertAIChat, type AIChatSession, type InsertAIChatSession } from "@shared/schema";
import { messages, type Message, type InsertMessage } from "@shared/schema";
import { chatRoomMessages, type ChatRoomMessage, type InsertChatRoomMessage } from "@shared/schema";
import { notifications, type Notification, type InsertNotification } from "@shared/schema";
import { news, type News, type InsertNews } from "@shared/schema";
import { friendRequests, type FriendRequest, type InsertFriendRequest } from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, gt, inArray, isNull, lt, sql } from "drizzle-orm";
import session from "express-session";
import createMemoryStore from "memorystore";
import pgSession from 'connect-pg-simple';
import { pool } from "./db";
import { dbMonitor } from "./utils/db-monitor";
import { EventEmitter } from 'events';
import fs from 'fs';
import path from 'path';

const MemoryStore = createMemoryStore(session);
const PgStore = pgSession(session);

// File-based session store for better persistence
class FileSessionStore extends session.Store {
  private sessionsDir: string;

  constructor(options: { dir?: string } = {}) {
    super();
    this.sessionsDir = options.dir || path.join(process.cwd(), 'sessions');
    
    // Ensure sessions directory exists
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true });
    }
  }

  get(sid: string, callback: (err?: any, session?: any) => void): void {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sid}.json`);
      if (!fs.existsSync(sessionPath)) {
        return callback();
      }

      const data = fs.readFileSync(sessionPath, 'utf8');
      const session = JSON.parse(data);
      
      // Check if session has expired
      if (session.expires && new Date(session.expires) <= new Date()) {
        this.destroy(sid, callback);
        return;
      }
      
      callback(null, session);
    } catch (error) {
      callback(error);
    }
  }

  set(sid: string, session: any, callback?: (err?: any) => void): void {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sid}.json`);
      fs.writeFileSync(sessionPath, JSON.stringify(session, null, 2));
      if (callback) callback();
    } catch (error) {
      if (callback) callback(error);
    }
  }

  destroy(sid: string, callback?: (err?: any) => void): void {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sid}.json`);
      if (fs.existsSync(sessionPath)) {
        fs.unlinkSync(sessionPath);
      }
      if (callback) callback();
    } catch (error) {
      if (callback) callback(error);
    }
  }

  touch(sid: string, session: any, callback?: (err?: any) => void): void {
    this.set(sid, session, callback);
  }
}

// Helper functions for date conversion between SQLite numbers and Date objects
const toDate = (timestamp: number | null): Date | null => {
  return timestamp ? new Date(timestamp) : null;
};

const toTimestamp = (date: Date | null): number | null => {
  return date ? date.getTime() : null;
};

const nowTimestamp = (): number => Date.now();

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser & { creationIp?: string }): Promise<User>;
  getThreads(): Promise<(Thread & { authorName: string; voteCount: number; replyCount: number })[]>;
  getUserThreads(userId: number): Promise<(Thread & { authorName: string; voteCount: number })[]>;
  createThread(thread: InsertThread & { authorId: number }): Promise<Thread>;
  deleteThread(threadId: number, userId: number): Promise<boolean>;
  getRepliesByThreadId(threadId: number): Promise<(Reply & { authorName: string })[]>;
  createReply(reply: InsertReply & { threadId: number; authorId: number }): Promise<Reply>;
  getThreadVotes(threadId: number): Promise<Vote[]>;
  getUserVote(threadId: number, userId: number): Promise<Vote | undefined>;
  createOrUpdateVote(vote: InsertVote & { threadId: number; userId: number }): Promise<Vote>;
  // AI Chat Sessions
  createAIChatSession(session: InsertAIChatSession & { userId: number }): Promise<AIChatSession>;
  getAIChatSessionsByUserId(userId: number): Promise<AIChatSession[]>;
  getAIChatSessionById(sessionId: number): Promise<AIChatSession | undefined>;
  updateAIChatSessionTitle(sessionId: number, title: string): Promise<void>;
  deleteAIChatSession(sessionId: number): Promise<void>;
  
  // AI Chats
  createAIChat(chat: InsertAIChat & { userId: number; response: string }): Promise<AIChat>;
  getAIChatsByUserId(userId: number): Promise<AIChat[]>;
  getAIChatsBySessionId(sessionId: number): Promise<AIChat[]>;
  getUsers(): Promise<User[]>;
  getThreadById(threadId: number): Promise<(Thread & { authorName: string }) | undefined>;
  getMessagesBetweenUsers(userId1: number, userId2: number): Promise<any[]>;
  createMessage(data: { 
    senderId: number, 
    recipientId: number, 
    content: string,
    nonce: string,
    isEncrypted?: boolean
  }): Promise<any>;
  getAllMessages(userId: number): Promise<any[]>;
  updateUserPublicKey(userId: number, publicKey: string): Promise<void>;
  getUserPublicKey(userId: number): Promise<string | undefined>;
  sessionStore: session.Store;
  createChatRoomMessage(data: InsertChatRoomMessage & { userId: number }): Promise<ChatRoomMessage>;
  getChatRoomMessages(roomId: string): Promise<(ChatRoomMessage & { username: string })[]>;
  updateUserPassword(userId: number, hashedPassword: string): Promise<void>;
  updateUserAvatar(userId: number, avatar: string): Promise<void>;
  deleteReply(replyId: number, userId: number): Promise<boolean>;
  deleteUser(userId: number): Promise<void>;
  getDbMetrics(): any;
  clearDbMetrics(): void;
  updateUserAdminStatus(userId: number, isAdmin: boolean, adminId: number): Promise<void>;
  getUserWithDetails(userId: number): Promise<User | undefined>;
  updateUserLoginInfo(userId: number, ip: string): Promise<void>;
  suspendUser(userId: number, until: Date, reason: string, adminId: number): Promise<void>;
  banUser(userId: number, reason: string, adminId: number): Promise<void>;
  unsuspendUser(userId: number, adminId: number): Promise<void>;
  unbanUser(userId: number, adminId: number): Promise<void>;
  isUserSuspended(userId: number): Promise<boolean>;
  isUserBanned(userId: number): Promise<boolean>;
  getUserTimeline(userId: number): Promise<any[]>;
  getUserEvents(): Promise<any[]>;
  createUserEvent(data: {
    userId: number;
    type: 'account_creation' | 'login' | 'password_change' | 'suspension' | 'ban' | 
          'unsuspension' | 'unban' | 'thread_create' | 'admin_toggle_admin' | 
          'admin_suspend_user' | 'admin_ban_user' | 'admin_unsuspend_user' | 
          'admin_unban_user' | 'admin_view_details' | 'admin_delete_user';
    details: string;
    ip?: string;
    reason?: string;
    adminId?: number;
    metadata?: string;
    timestamp?: Date;
  }): Promise<void>;

  logAdminAction(data: {
    adminId: number;
    targetUserId: number;
    actionType: 'admin_toggle_admin' | 'admin_suspend_user' | 'admin_ban_user' | 
                'admin_unsuspend_user' | 'admin_unban_user' | 'admin_view_details' | 
                'admin_delete_user';
    details: string;
    reason?: string;
    metadata?: string;
  }): Promise<void>;

  updateUserEncryptionSetting(userId: number, enabled: boolean): Promise<void>;
  
  // Notifications
  createNotification(notification: InsertNotification): Promise<Notification>;
  getUserNotifications(userId: number, limit?: number): Promise<(Notification & { senderName?: string })[]>;
  getUnreadNotificationCount(userId: number): Promise<number>;
  markNotificationAsRead(notificationId: number, userId: number): Promise<boolean>;
  markAllNotificationsAsRead(userId: number): Promise<void>;
  deleteNotification(notificationId: number, userId: number): Promise<boolean>;
  
  // News
  createNews(news: InsertNews & { authorId: number }): Promise<News>;
  getNews(): Promise<(News & { authorName: string })[]>;
  getNewsById(id: number): Promise<(News & { authorName: string }) | undefined>;
  updateNews(id: number, news: Partial<InsertNews>): Promise<boolean>;
  deleteNews(id: number): Promise<boolean>;
  
  // Profile-specific methods
  getUserStats(userId: number): Promise<{
    threadsCreated: number;
    repliesPosted: number;
    messagesReceived: number;
    messagesSent: number;
    lastLoginAt: string | null;
    joinedAt: string;
  }>;
  getUserActivity(userId: number): Promise<{
    id: number;
    type: string;
    description: string;
    timestamp: string;
    ip?: string;
  }[]>;
  getUserSecurityInfo(userId: number): Promise<{
    lastPasswordChange: string;
    publicKey: string;
    autoEncryptionEnabled: boolean;
  }>;

  // Friend requests
  sendFriendRequest(requesterId: number, receiverId: number): Promise<FriendRequest>;
  getFriendRequests(userId: number): Promise<(FriendRequest & { requesterName: string; receiverName: string })[]>;
  respondToFriendRequest(requestId: number, userId: number, accept: boolean): Promise<boolean>;
  getFriends(userId: number): Promise<User[]>;
  checkFriendship(userId1: number, userId2: number): Promise<{ isFriend: boolean; requestStatus?: string }>;
  
  // Bio updates
  updateUserBio(userId: number, bio: string): Promise<void>;
  
  // Public profile
  getPublicProfile(userId: number): Promise<{
    id: number;
    username: string;
    bio: string;
    avatar: string;
    joinedAt: string;
    threadsCreated: number;
    repliesPosted: number;
    isAdmin: boolean;
  } | null>;
}

export class DatabaseStorage implements IStorage {
  public sessionStore: session.Store;

  constructor() {
    // Use FileSessionStore for better session persistence across browser restarts
    this.sessionStore = new FileSessionStore({
      dir: path.join(process.cwd(), 'sessions')
    });
    
    // Add error event listener to handle session store errors
    if (this.sessionStore instanceof EventEmitter) {
      this.sessionStore.on('error', (error: unknown) => {
        console.error('Session store error:', error);
      });
    }
    
    console.log('Session store initialized with FileSessionStore for better persistence');
  }

  async getUser(id: number): Promise<User | undefined> {
    return dbMonitor.trackQuery('getUser', async () => {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      return user;
    });
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return dbMonitor.trackQuery('getUserByUsername', async () => {
      const [user] = await db.select().from(users).where(eq(users.username, username));
      return user;
    });
  }

  async createUser(insertUser: InsertUser & { creationIp?: string }): Promise<User> {
    return dbMonitor.trackQuery('createUser', async () => {
      const [user] = await db.insert(users)
        .values({
          ...insertUser,
          creationIp: insertUser.creationIp
        })
        .returning();

    await this.createUserEvent({
      userId: user.id,
      type: 'account_creation',
      details: 'Account created',
      ip: insertUser.creationIp,
      timestamp: toDate(user.createdAt) || new Date()
    });

      return user;
    });
  }

  async getThreads(): Promise<(Thread & { authorName: string; voteCount: number; replyCount: number })[]> {
    return dbMonitor.trackQuery('getThreads', async () => {
      const result = await db.query.threads.findMany({
        where: eq(threads.isDeleted, false),
        with: {
          author: true,
          replies: true,
        },
        orderBy: desc(threads.createdAt),
      });
      
      // Get threadIds to fetch votes for all threads in a single query
      const threadIds = result.map(thread => thread.id);
      
      // Get all votes for these threads in a single query
      const allVotes = await db
        .select()
        .from(votes)
        .where(inArray(votes.threadId, threadIds));
      
      // Group votes by threadId for easier access
      const votesByThreadId = allVotes.reduce((acc, vote) => {
        if (!acc[vote.threadId]) {
          acc[vote.threadId] = [];
        }
        acc[vote.threadId].push(vote);
        return acc;
      }, {} as Record<number, Vote[]>);
      
      // Map threads with their respective votes
      const threadsWithVotes = result.map(thread => {
        const threadVotes = votesByThreadId[thread.id] || [];
        const voteCount = threadVotes.reduce((acc, vote) => acc + (vote.isUpvote ? 1 : -1), 0);
        
        return {
          ...thread,
          authorName: thread.author.username,
          voteCount,
          replyCount: thread.replies.length
        };
      });

      return threadsWithVotes;
    });
  }

  async getUserThreads(userId: number): Promise<(Thread & { authorName: string; voteCount: number })[]> {
    return dbMonitor.trackQuery('getUserThreads', async () => {
      const result = await db.query.threads.findMany({
        where: and(
          eq(threads.authorId, userId),
          eq(threads.isDeleted, false)
        ),
        with: {
          author: true,
        },
        orderBy: desc(threads.createdAt),
      });
      
      // Get threadIds to fetch votes for all threads in a single query
      const threadIds = result.map(thread => thread.id);
      
      // Get all votes for these threads in a single query
      const allVotes = threadIds.length > 0 ? 
        await db.select()
          .from(votes)
          .where(inArray(votes.threadId, threadIds)) : 
        [];
      
      // Group votes by threadId for easier access
      const votesByThreadId = allVotes.reduce((acc, vote) => {
        if (!acc[vote.threadId]) {
          acc[vote.threadId] = [];
        }
        acc[vote.threadId].push(vote);
        return acc;
      }, {} as Record<number, Vote[]>);
      
      // Map threads with their respective votes
      const threadsWithVotes = result.map(thread => {
        const threadVotes = votesByThreadId[thread.id] || [];
        const voteCount = threadVotes.reduce((acc, vote) => acc + (vote.isUpvote ? 1 : -1), 0);
        
        return {
          ...thread,
          authorName: thread.author.username,
          voteCount
        };
      });

      return threadsWithVotes;
    });
  }

  async createThread(data: InsertThread & { authorId: number }): Promise<Thread> {
    return dbMonitor.trackQuery('createThread', async () => {
      try {
        const [thread] = await db.insert(threads).values(data).returning();
        
        // Create event for thread creation
        try {
          await this.createUserEvent({
            userId: data.authorId,
            type: 'thread_create', // Use the correct enum value
            details: `Created thread: ${data.title}`
          });
        } catch (eventError) {
          console.error('Failed to create user event for thread:', eventError);
          // Don't fail the whole thread creation if just the event fails
        }
        
        return thread;
      } catch (error) {
        console.error('Error in createThread:', error);
        throw error;
      }
    });
  }

  async deleteThread(threadId: number, userId: number): Promise<boolean> {
    return dbMonitor.trackQuery('deleteThread', async () => {
      // First check if user is admin
      const adminCheck = await db
        .select({ isAdmin: users.isAdmin })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      const isUserAdmin = adminCheck[0]?.isAdmin || false;

      if (isUserAdmin) {
        // Admin can delete any thread - just check if thread exists and isn't already deleted
        const threadCheck = await db
          .select({ id: threads.id })
          .from(threads)
          .where(and(eq(threads.id, threadId), eq(threads.isDeleted, false)))
          .limit(1);
        
        if (threadCheck.length === 0) return false;
      } else {
        // Regular user can only delete their own threads
        const threadCheck = await db
          .select({ id: threads.id })
          .from(threads)
          .where(and(
            eq(threads.id, threadId), 
            eq(threads.authorId, userId),
            eq(threads.isDeleted, false)
          ))
          .limit(1);
        
        if (threadCheck.length === 0) return false;
      }

      // Mark thread as deleted
      await db
        .update(threads)
        .set({ isDeleted: true })
        .where(eq(threads.id, threadId));

      return true;
    });
  }

  async deleteReply(replyId: number, userId: number): Promise<boolean> {
    return dbMonitor.trackQuery('deleteReply', async () => {
      const [reply] = await db
        .select()
        .from(replies)
        .where(and(eq(replies.id, replyId), eq(replies.authorId, userId)));

      if (!reply) return false;

      await db
        .delete(replies)
        .where(eq(replies.id, replyId));

      return true;
    });
  }

  async getRepliesByThreadId(threadId: number): Promise<(Reply & { authorName: string })[]> {
    return dbMonitor.trackQuery('getRepliesByThreadId', async () => {
      const result = await db.query.replies.findMany({
        where: eq(replies.threadId, threadId),
        with: {
          author: true,
        },
        orderBy: replies.createdAt,
      });

      return result.map(reply => ({
        ...reply,
        authorName: reply.author.username
      }));
    });
  }

  async createReply(data: InsertReply & { threadId: number; authorId: number }): Promise<Reply> {
    return dbMonitor.trackQuery('createReply', async () => {
      const [reply] = await db.insert(replies).values(data).returning();
      return reply;
    });
  }

  async getThreadVotes(threadId: number): Promise<Vote[]> {
    return dbMonitor.trackQuery('getThreadVotes', async () => {
      return db
        .select()
        .from(votes)
        .where(eq(votes.threadId, threadId));
    });
  }

  async getUserVote(threadId: number, userId: number): Promise<Vote | undefined> {
    return dbMonitor.trackQuery('getUserVote', async () => {
      const [vote] = await db
        .select()
        .from(votes)
        .where(and(eq(votes.threadId, threadId), eq(votes.userId, userId)));
      return vote;
    });
  }

  async createOrUpdateVote(data: InsertVote & { threadId: number; userId: number }): Promise<Vote> {
    return dbMonitor.trackQuery('createOrUpdateVote', async () => {
      const existingVote = await this.getUserVote(data.threadId, data.userId);

      if (existingVote) {
        const [vote] = await db
          .update(votes)
          .set({ isUpvote: data.isUpvote })
          .where(eq(votes.id, existingVote.id))
          .returning();
        return vote;
      }

      const [vote] = await db.insert(votes).values(data).returning();
      return vote;
    });
  }

  async deleteUser(userId: number): Promise<void> {
    return dbMonitor.trackQuery('deleteUser', async () => {
      await db.transaction(async (tx) => {
        await tx.delete(votes).where(eq(votes.userId, userId));
        await tx.delete(replies).where(eq(replies.authorId, userId));
        await tx.delete(threads).where(eq(threads.authorId, userId));
        await tx.delete(messages).where(or(
          eq(messages.senderId, userId),
          eq(messages.recipientId, userId)
        ));
        await tx.delete(chatRoomMessages).where(eq(chatRoomMessages.userId, userId));
        await tx.delete(aiChats).where(eq(aiChats.userId, userId));
        await tx.delete(users).where(eq(users.id, userId));
      });
    });
  }

  // AI Chat Session methods
  async createAIChatSession(data: InsertAIChatSession & { userId: number }): Promise<AIChatSession> {
    return dbMonitor.trackQuery('createAIChatSession', async () => {
      const [session] = await db.insert(aiChatSessions).values({
        ...data,
        updatedAt: nowTimestamp()
      }).returning();
      return session;
    });
  }

  async getAIChatSessionsByUserId(userId: number): Promise<AIChatSession[]> {
    return dbMonitor.trackQuery('getAIChatSessionsByUserId', async () => {
      return db
        .select()
        .from(aiChatSessions)
        .where(eq(aiChatSessions.userId, userId))
        .orderBy(desc(aiChatSessions.updatedAt));
    });
  }

  async getAIChatSessionById(sessionId: number): Promise<AIChatSession | undefined> {
    return dbMonitor.trackQuery('getAIChatSessionById', async () => {
      const [session] = await db
        .select()
        .from(aiChatSessions)
        .where(eq(aiChatSessions.id, sessionId));
      return session;
    });
  }

  async updateAIChatSessionTitle(sessionId: number, title: string): Promise<void> {
    return dbMonitor.trackQuery('updateAIChatSessionTitle', async () => {
      await db
        .update(aiChatSessions)
        .set({
          title,
          updatedAt: nowTimestamp()
        })
        .where(eq(aiChatSessions.id, sessionId));
    });
  }

  async deleteAIChatSession(sessionId: number): Promise<void> {
    return dbMonitor.trackQuery('deleteAIChatSession', async () => {
      await db.transaction(async (tx) => {
        // First delete all chats in the session
        await tx
          .delete(aiChats)
          .where(eq(aiChats.sessionId, sessionId));
        
        // Then delete the session itself
        await tx
          .delete(aiChatSessions)
          .where(eq(aiChatSessions.id, sessionId));
      });
    });
  }

  // AI Chat methods
  async createAIChat(data: InsertAIChat & { userId: number; response: string; promptTokens?: number; completionTokens?: number; totalTokens?: number }): Promise<AIChat> {
    return dbMonitor.trackQuery('createAIChat', async () => {
      // Update the session's updatedAt timestamp and token count
      if (data.sessionId) {
        await db
          .update(aiChatSessions)
          .set({
            updatedAt: nowTimestamp(),
            totalTokens: sql`${aiChatSessions.totalTokens} + ${data.totalTokens || 0}`
          })
          .where(eq(aiChatSessions.id, data.sessionId));
      }
      
      const [chat] = await db.insert(aiChats).values(data).returning();
      return chat;
    });
  }
  
  async updateAIChatSessionTokens(sessionId: number, tokenUsage: { promptTokens: number; completionTokens: number; totalTokens: number }): Promise<void> {
    return dbMonitor.trackQuery('updateAIChatSessionTokens', async () => {
      await db
        .update(aiChatSessions)
        .set({
          totalTokens: sql`${aiChatSessions.totalTokens} + ${tokenUsage.totalTokens}`,
          updatedAt: nowTimestamp()
        })
        .where(eq(aiChatSessions.id, sessionId));
    });
  }
  
  async getAIChatSessionTokenUsage(sessionId: number): Promise<{ totalTokens: number; maxTokens: number; provider: string } | null> {
    return dbMonitor.trackQuery('getAIChatSessionTokenUsage', async () => {
      const [session] = await db
        .select({
          totalTokens: aiChatSessions.totalTokens,
          maxTokens: aiChatSessions.maxTokens,
          provider: aiChatSessions.provider
        })
        .from(aiChatSessions)
        .where(eq(aiChatSessions.id, sessionId));
      return session || null;
    });
  }

  async getAIChatsByUserId(userId: number): Promise<AIChat[]> {
    return dbMonitor.trackQuery('getAIChatsByUserId', async () => {
      return db
        .select()
        .from(aiChats)
        .where(eq(aiChats.userId, userId))
        .orderBy(desc(aiChats.createdAt));
    });
  }
  
  async getAIChatsBySessionId(sessionId: number): Promise<AIChat[]> {
    return dbMonitor.trackQuery('getAIChatsBySessionId', async () => {
      return db
        .select()
        .from(aiChats)
        .where(eq(aiChats.sessionId, sessionId))
        .orderBy(aiChats.createdAt);
    });
  }

  async getUsers(): Promise<User[]> {
    return dbMonitor.trackQuery('getUsers', async () => {
      // Use .from(users) instead of select to get all fields to satisfy the type
      return db.select()
      .from(users)
      .where(
        and(
          isNull(users.bannedAt),
          or(
            isNull(users.suspendedUntil),
            lt(users.suspendedUntil, nowTimestamp())
          )
        )
      );
    });
  }

  async getThreadById(threadId: number): Promise<(Thread & { authorName: string }) | undefined> {
    return dbMonitor.trackQuery('getThreadById', async () => {
      const result = await db.query.threads.findFirst({
        where: eq(threads.id, threadId),
        with: {
          author: true
        }
      });

      if (!result) return undefined;

      return {
        ...result,
        authorName: result.author.username
      };
    });
  }

  async getMessagesBetweenUsers(userId1: number, userId2: number): Promise<any[]> {
    return dbMonitor.trackQuery('getMessagesBetweenUsers', async () => {
      const msgs = await db.query.messages.findMany({
        where: or(
          and(eq(messages.senderId, userId1), eq(messages.recipientId, userId2)),
          and(eq(messages.senderId, userId2), eq(messages.recipientId, userId1))
        ),
        with: {
          sender: true
        },
        orderBy: messages.createdAt
      });

      return msgs.map(msg => ({
        ...msg,
        senderName: msg.sender.username
      }));
    });
  }

  async createMessage(data: { 
    senderId: number, 
    recipientId: number, 
    content: string,
    nonce: string,
    isEncrypted?: boolean
  }): Promise<any> {
    return dbMonitor.trackQuery('createMessage', async () => {
      // Set default value for isEncrypted if not provided
      const messageData = {
        ...data,
        isEncrypted: data.isEncrypted !== undefined ? data.isEncrypted : true
      };

      const [message] = await db.insert(messages)
        .values(messageData)
        .returning();

      const sender = await this.getUser(data.senderId);
      return {
        ...message,
        senderName: sender?.username || 'Unknown User'
      };
    });
  }

  async getAllMessages(userId: number): Promise<any[]> {
    return dbMonitor.trackQuery('getAllMessages', async () => {
      // First get all messages
      const messagesResult = await db
        .select()
        .from(messages)
        .where(or(
          eq(messages.senderId, userId),
          eq(messages.recipientId, userId)
        ))
        .orderBy(desc(messages.createdAt))
        .limit(100);
      
      if (messagesResult.length === 0) {
        return [];
      }

      // Extract unique user IDs from the messages
      const uniqueUserIds = new Set<number>();
      messagesResult.forEach(msg => {
        uniqueUserIds.add(msg.senderId);
        uniqueUserIds.add(msg.recipientId);
      });

      // Fetch user information separately
      const userIds = Array.from(uniqueUserIds);
      const usersResult = await db
        .select()
        .from(users)
        .where(inArray(users.id, userIds));

      // Create a map of user IDs to usernames for quick lookup
      const usernameMap = usersResult.reduce((map, user) => {
        map[user.id] = user.username;
        return map;
      }, {} as Record<number, string>);

      // Process the results to add senderName and recipientName
      return messagesResult.map(msg => ({
        ...msg,
        senderName: usernameMap[msg.senderId] || 'Unknown User',
        recipientName: usernameMap[msg.recipientId] || 'Unknown User'
      }));
    });
  }

  async createChatRoomMessage(data: InsertChatRoomMessage & { userId: number }): Promise<ChatRoomMessage> {
    return dbMonitor.trackQuery('createChatRoomMessage', async () => {
      const [message] = await db
        .insert(chatRoomMessages)
        .values([{
          userId: data.userId,
          content: data.content || '',
          roomId: data.roomId
        }])
        .returning();
      return message;
    });
  }

  async getChatRoomMessages(roomId: string): Promise<(ChatRoomMessage & { username: string })[]> {
    return dbMonitor.trackQuery('getChatRoomMessages', async () => {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const messages = await db.query.chatRoomMessages.findMany({
        where: and(
          eq(chatRoomMessages.roomId, roomId),
          gt(chatRoomMessages.createdAt, oneDayAgo.getTime())
        ),
        with: {
          user: true
        },
        orderBy: chatRoomMessages.createdAt
      });

      return messages.map(msg => ({
        ...msg,
        username: msg.user.username
      }));
    });
  }

  async updateUserPassword(userId: number, hashedPassword: string): Promise<void> {
    return dbMonitor.trackQuery('updateUserPassword', async () => {
      await db.update(users)
        .set({
          password: hashedPassword,
          lastPasswordChange: nowTimestamp()
        })
        .where(eq(users.id, userId));
    });
  }

  async updateUserAvatar(userId: number, avatar: string): Promise<void> {
    return dbMonitor.trackQuery('updateUserAvatar', async () => {
      await db.update(users)
        .set({ avatar })
        .where(eq(users.id, userId));
    });
  }
  
  async updateUserPublicKey(userId: number, publicKey: string): Promise<void> {
    return dbMonitor.trackQuery('updateUserPublicKey', async () => {
      await db.update(users)
        .set({ publicKey })
        .where(eq(users.id, userId));
    });
  }
  
  async getUserPublicKey(userId: number): Promise<string | undefined> {
    return dbMonitor.trackQuery('getUserPublicKey', async () => {
      const [user] = await db
        .select({ publicKey: users.publicKey })
        .from(users)
        .where(eq(users.id, userId));
      return user?.publicKey || undefined;
    });
  }

  async getUserWithDetails(userId: number): Promise<User | undefined> {
    return dbMonitor.trackQuery('getUserWithDetails', async () => {
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (user && user.suspendedUntil) {
        console.log(`[Storage] User ${userId} suspension details: ${user.suspendedUntil}`);
      }
      return user;
    });
  }

  async updateUserLoginInfo(userId: number, ip: string): Promise<void> {
    return dbMonitor.trackQuery('updateUserLoginInfo', async () => {
      const now = nowTimestamp();
      await db.update(users)
        .set({
          lastLoginIp: ip,
          lastLoginAt: now
        })
        .where(eq(users.id, userId));
        
      await this.createUserEvent({
        userId,
        type: 'login',
        details: 'User logged in',
        ip,
        timestamp: toDate(now) || new Date()
      });
    });
  }

  async updateUserAdminStatus(userId: number, isAdmin: boolean, adminId: number): Promise<void> {
    return dbMonitor.trackQuery('updateUserAdminStatus', async () => {
      await db.update(users)
        .set({ isAdmin })
        .where(eq(users.id, userId));

      await this.logAdminAction({
        adminId,
        targetUserId: userId,
        actionType: 'admin_toggle_admin',
        details: `Admin status ${isAdmin ? 'granted' : 'revoked'}`,
        metadata: JSON.stringify({ isAdmin })
      });
    });
  }

  async suspendUser(userId: number, until: Date, reason: string, adminId: number): Promise<void> {
    return dbMonitor.trackQuery('suspendUser', async () => {
      await db.update(users)
        .set({
          suspendedUntil: until.getTime(),
          banReason: reason
        })
        .where(eq(users.id, userId));

      await this.logAdminAction({
        adminId,
        targetUserId: userId,
        actionType: 'admin_suspend_user',
        details: 'Account suspended',
        reason,
        metadata: JSON.stringify({ until })
      });
    });
  }

  async banUser(userId: number, reason: string, adminId: number): Promise<void> {
    return dbMonitor.trackQuery('banUser', async () => {
      await db.update(users)
        .set({
          bannedAt: nowTimestamp(),
          banReason: reason
        })
        .where(eq(users.id, userId));

      await this.logAdminAction({
        adminId,
        targetUserId: userId,
        actionType: 'admin_ban_user',
        details: 'Account banned',
        reason
      });
    });
  }

  async unsuspendUser(userId: number, adminId: number): Promise<void> {
    return dbMonitor.trackQuery('unsuspendUser', async () => {
      await db.update(users)
        .set({
          suspendedUntil: null,
          banReason: null
        })
        .where(eq(users.id, userId));

      await this.logAdminAction({
        adminId,
        targetUserId: userId,
        actionType: 'admin_unsuspend_user',
        details: 'Account unsuspended'
      });
    });
  }

  async unbanUser(userId: number, adminId: number): Promise<void> {
    return dbMonitor.trackQuery('unbanUser', async () => {
      await db.update(users)
        .set({
          bannedAt: null,
          banReason: null
        })
        .where(eq(users.id, userId));

      await this.logAdminAction({
        adminId,
        targetUserId: userId,
        actionType: 'admin_unban_user',
        details: 'Account unbanned'
      });
    });
  }

  async isUserSuspended(userId: number): Promise<boolean> {
    return dbMonitor.trackQuery('isUserSuspended', async () => {
      const user = await this.getUser(userId);
      if (!user || !user.suspendedUntil) {
        return false;
      }
      const suspendedUntil = new Date(user.suspendedUntil);
      const now = new Date();
      console.log(`[Storage] Checking suspension for user ${userId}: suspended until ${suspendedUntil.toISOString()}, current time ${now.toISOString()}`);
      return suspendedUntil > now;
    });
  }

  async isUserBanned(userId: number): Promise<boolean> {
    return dbMonitor.trackQuery('isUserBanned', async () => {
      const user = await this.getUser(userId);
      return !!user?.bannedAt;
    });
  }

  async getUserTimeline(userId: number): Promise<any[]> {
    return dbMonitor.trackQuery('getUserTimeline', async () => {
      try {
        // Fetch both user data and events in parallel to reduce query time
        const [user, events] = await Promise.all([
          this.getUser(userId),
          db.select()
            .from(userEvents)
            .where(eq(userEvents.userId, userId))
            .orderBy(desc(userEvents.timestamp))
        ]);
        
        if (!user) {
          console.log(`[Storage] No user found for userId: ${userId}`);
          return [];
        }

        console.log(`[Storage] Found ${events.length} timeline events for user ${userId}`);

        // Add user's basic events if no events exist
        if (events.length === 0) {
          // Prepare events to be created
          const eventsToCreate = [];
          
          // Create account creation event
          eventsToCreate.push({
            userId: user.id,
            type: 'account_creation' as const,
            details: 'Account created',
            timestamp: toDate(user.createdAt) || new Date(),
            ip: user.creationIp || undefined
          });

        // Create login event if user has logged in
        if (user.lastLoginAt) {
          eventsToCreate.push({
            userId: user.id,
            type: 'login' as const,
            details: 'Last login',
            timestamp: toDate(user.lastLoginAt) || new Date(),
            ip: user.lastLoginIp || undefined
          });
        }
          
          // Create all events in parallel
          await Promise.all(
            eventsToCreate.map(event => this.createUserEvent(event))
          );

          // Fetch events again after creating initial ones
          return await db.select()
            .from(userEvents)
            .where(eq(userEvents.userId, userId))
            .orderBy(desc(userEvents.timestamp));
        }

        return events;
      } catch (error: any) {
        console.error(`[Storage] Error in getUserTimeline:`, error);
        throw new Error(`Failed to fetch timeline: ${error.message}`);
      }
    });
  }

  async createUserEvent(data: {
    userId: number;
    type: 'account_creation' | 'login' | 'password_change' | 'suspension' | 'ban' | 
          'unsuspension' | 'unban' | 'thread_create' | 'admin_toggle_admin' | 
          'admin_suspend_user' | 'admin_ban_user' | 'admin_unsuspend_user' | 
          'admin_unban_user' | 'admin_view_details' | 'admin_delete_user';
    details: string;
    ip?: string;
    reason?: string;
    adminId?: number;
    metadata?: string;
    timestamp?: Date;
  }): Promise<void> {
    return dbMonitor.trackQuery('createUserEvent', async () => {
      try {
        await db.insert(userEvents).values({
          ...data,
          timestamp: data.timestamp ? data.timestamp.getTime() : nowTimestamp()
        });
        console.log(`[Storage] Created user event: ${data.type} for user ${data.userId}`);
      } catch (error: any) {
        console.error(`[Storage] Error creating user event:`, error);
        throw error;
      }
    });
  }

  async logAdminAction(data: {
    adminId: number;
    targetUserId: number;
    actionType: 'admin_toggle_admin' | 'admin_suspend_user' | 'admin_ban_user' | 
                'admin_unsuspend_user' | 'admin_unban_user' | 'admin_view_details' | 
                'admin_delete_user';
    details: string;
    reason?: string;
    metadata?: string;
  }): Promise<void> {
    // Create a structured metadata object that includes targetUserId
    const metadataObj = {
      targetUserId: data.targetUserId,
      ...(data.metadata ? JSON.parse(data.metadata) : {})
    };
    
    // Store this event with the admin as the userId and target in metadata
    return this.createUserEvent({
      userId: data.adminId, // This identifies WHO performed the action
      type: data.actionType,
      details: data.details,
      adminId: data.adminId,
      reason: data.reason,
      metadata: JSON.stringify(metadataObj),
      timestamp: new Date()
    });
  }

  getDbMetrics() {
    return dbMonitor.getMetrics();
  }

  clearDbMetrics() {
    dbMonitor.clearMetrics();
  }

  async getUserEvents(): Promise<any[]> {
    return dbMonitor.trackQuery('getUserEvents', async () => {
      // Get all user events
      const events = await db.select({
        id: userEvents.id,
        type: userEvents.type,
        userId: userEvents.userId,
        adminId: userEvents.adminId,
        timestamp: userEvents.timestamp,
        details: userEvents.details,
        reason: userEvents.reason,
        metadata: userEvents.metadata,
      })
      .from(userEvents)
      .orderBy(desc(userEvents.timestamp));
      
      // If no events, return empty array
      if (!events.length) {
        return [];
      }
      
      // Create sets of all user and admin IDs
      const userIds = new Set<number>();
      events.forEach(event => {
        // Add user_id to the set
        if (event.userId) userIds.add(event.userId);
        
        // Add admin ID to the set if available
        if (event.adminId) userIds.add(event.adminId);
        
        // Extract target user ID from metadata for admin actions
        if (event.type.toString().startsWith('admin_') && event.metadata) {
          try {
            const metadata = JSON.parse(event.metadata);
            if (metadata.targetUserId) {
              userIds.add(metadata.targetUserId);
            }
          } catch (e) {
            console.log('Error parsing metadata JSON:', e);
          }
        }
      });
      
      // Convert Set to Array
      const userIdArray = Array.from(userIds);
      
      // Create a query to fetch all users at once
      const userList = await db.select({
        id: users.id,
        username: users.username,
      })
      .from(users)
      .where(
        userIdArray.length > 0 ? 
          inArray(users.id, userIdArray) : 
          eq(users.id, 0)
      );
      
      // Create a map of IDs to usernames
      const usernameMap = new Map<number, string>();
      userList.forEach(user => {
        usernameMap.set(user.id, user.username);
      });
      
      // Format the logs with user information
      return events.map(event => {
        let targetUserId = event.userId;
        const adminId = event.adminId;
        
        // For admin actions, extract target user ID from metadata
        if (event.type.toString().startsWith('admin_') && event.metadata) {
          try {
            const metadata = JSON.parse(event.metadata);
            if (metadata.targetUserId) {
              targetUserId = metadata.targetUserId;
            }
          } catch (e) {
            console.log('Error parsing metadata JSON:', e);
          }
        }
        
        return {
          id: event.id,
          type: event.type,
          timestamp: event.timestamp,
          details: event.details,
          reason: event.reason,
          metadata: event.metadata,
          // Set username from targetUserId (either from metadata or user_id field)
          username: targetUserId ? (usernameMap.get(targetUserId) || 'Unknown User') : 'Unknown User',
          // Admin username from admin_id field
          adminUsername: adminId ? (usernameMap.get(adminId) || 'System') : 'System'
        };
      });
    });
  }

  async updateUserEncryptionSetting(userId: number, enabled: boolean): Promise<void> {
    return dbMonitor.trackQuery('updateUserEncryptionSetting', async () => {
      await db
        .update(users)
        .set({ autoEncryptionEnabled: enabled })
        .where(eq(users.id, userId));
      
      console.log(`[Storage] Updated encryption setting for user ${userId}: ${enabled}`);
    });
  }

  // Notification methods
  async createNotification(notification: InsertNotification): Promise<Notification> {
    return dbMonitor.trackQuery('createNotification', async () => {
      const [result] = await db.insert(notifications).values(notification).returning();
      return result;
    });
  }

  async getUserNotifications(userId: number, limit: number = 20): Promise<(Notification & { senderName?: string })[]> {
    return dbMonitor.trackQuery('getUserNotifications', async () => {
      const result = await db.query.notifications.findMany({
        where: eq(notifications.userId, userId),
        with: {
          sender: true
        },
        orderBy: desc(notifications.createdAt),
        limit
      });

      return result.map(notification => ({
        ...notification,
        senderName: notification.sender?.username
      }));
    });
  }

  async getUnreadNotificationCount(userId: number): Promise<number> {
    return dbMonitor.trackQuery('getUnreadNotificationCount', async () => {
      const result = await db
        .select({ count: sql`count(*)` })
        .from(notifications)
        .where(and(
          eq(notifications.userId, userId),
          eq(notifications.isRead, false)
        ));
      
      return parseInt(String(result[0]?.count || '0'));
    });
  }

  async markNotificationAsRead(notificationId: number, userId: number): Promise<boolean> {
    return dbMonitor.trackQuery('markNotificationAsRead', async () => {
      const result = await db
        .update(notifications)
        .set({ isRead: true })
        .where(and(
          eq(notifications.id, notificationId),
          eq(notifications.userId, userId)
        ))
        .returning();
      
      return result.length > 0;
    });
  }

  async markAllNotificationsAsRead(userId: number): Promise<void> {
    return dbMonitor.trackQuery('markAllNotificationsAsRead', async () => {
      await db
        .update(notifications)
        .set({ isRead: true })
        .where(and(
          eq(notifications.userId, userId),
          eq(notifications.isRead, false)
        ));
    });
  }

  async deleteNotification(notificationId: number, userId: number): Promise<boolean> {
    return dbMonitor.trackQuery('deleteNotification', async () => {
      console.log(`Storage: Deleting notification ${notificationId} for user ${userId}`);
      
      // First check if the notification exists
      const existing = await db
        .select()
        .from(notifications)
        .where(and(
          eq(notifications.id, notificationId),
          eq(notifications.userId, userId)
        ));
      
      console.log(`Found existing notifications:`, existing);
      
      if (existing.length === 0) {
        console.log(`No notification found with id ${notificationId} for user ${userId}`);
        return false;
      }
      
      const result = await db
        .delete(notifications)
        .where(and(
          eq(notifications.id, notificationId),
          eq(notifications.userId, userId)
        ))
        .returning();
      
      console.log(`Delete result:`, result);
      return result.length > 0;
    });
  }

  // News methods
  async createNews(newsData: InsertNews & { authorId: number }): Promise<News> {
    return dbMonitor.trackQuery('createNews', async () => {
      const [result] = await db.insert(news).values({
        ...newsData,
        publishedAt: newsData.isPublished ? nowTimestamp() : null
      }).returning();
      return result;
    });
  }

  async getNews(): Promise<(News & { authorName: string })[]> {
    return dbMonitor.trackQuery('getNews', async () => {
      const result = await db.query.news.findMany({
        with: {
          author: true
        },
        orderBy: desc(news.createdAt)
      });

      return result.map(newsItem => ({
        ...newsItem,
        authorName: newsItem.author.username
      }));
    });
  }

  async getNewsById(id: number): Promise<(News & { authorName: string }) | undefined> {
    return dbMonitor.trackQuery('getNewsById', async () => {
      const result = await db.query.news.findFirst({
        where: eq(news.id, id),
        with: {
          author: true
        }
      });

      if (!result) return undefined;

      return {
        ...result,
        authorName: result.author.username
      };
    });
  }

  async updateNews(id: number, newsData: Partial<InsertNews>): Promise<boolean> {
    return dbMonitor.trackQuery('updateNews', async () => {
      const updateData: any = { ...newsData };
      
      // Set publishedAt if publishing for the first time
      if (newsData.isPublished) {
        const existing = await db.select({ publishedAt: news.publishedAt }).from(news).where(eq(news.id, id));
        if (existing[0] && !existing[0].publishedAt) {
          updateData.publishedAt = nowTimestamp();
        }
      }

      const result = await db
        .update(news)
        .set(updateData)
        .where(eq(news.id, id))
        .returning();
      
      return result.length > 0;
    });
  }

  async deleteNews(id: number): Promise<boolean> {
    return dbMonitor.trackQuery('deleteNews', async () => {
      const result = await db
        .delete(news)
        .where(eq(news.id, id))
        .returning();
      
      return result.length > 0;
    });
  }

  // Profile-specific methods
  async getUserStats(userId: number): Promise<{
    threadsCreated: number;
    repliesPosted: number;
    messagesReceived: number;
    messagesSent: number;
    lastLoginAt: string | null;
    joinedAt: string;
  }> {
    return dbMonitor.trackQuery('getUserStats', async () => {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Count threads created by user
      const threadsCreated = await db
        .select({ count: sql<number>`count(*)` })
        .from(threads)
        .where(eq(threads.authorId, userId));

      // Count replies posted by user
      const repliesPosted = await db
        .select({ count: sql<number>`count(*)` })
        .from(replies)
        .where(eq(replies.authorId, userId));

      // Count messages sent by user
      const messagesSent = await db
        .select({ count: sql<number>`count(*)` })
        .from(messages)
        .where(eq(messages.senderId, userId));

      // Count messages received by user
      const messagesReceived = await db
        .select({ count: sql<number>`count(*)` })
        .from(messages)
        .where(eq(messages.recipientId, userId));

      return {
        threadsCreated: threadsCreated[0]?.count || 0,
        repliesPosted: repliesPosted[0]?.count || 0,
        messagesSent: messagesSent[0]?.count || 0,
        messagesReceived: messagesReceived[0]?.count || 0,
        lastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt).toISOString() : null,
        joinedAt: new Date(user.createdAt).toISOString(),
      };
    });
  }

  async getUserActivity(userId: number): Promise<{
    id: number;
    type: string;
    description: string;
    timestamp: string;
    ip?: string;
  }[]> {
    return dbMonitor.trackQuery('getUserActivity', async () => {
      const events = await db
        .select({
          id: userEvents.id,
          type: userEvents.type,
          details: userEvents.details,
          timestamp: userEvents.timestamp,
          ip: userEvents.ip,
        })
        .from(userEvents)
        .where(eq(userEvents.userId, userId))
        .orderBy(desc(userEvents.timestamp))
        .limit(50); // Limit to last 50 activities

      return events.map(event => ({
        id: event.id,
        type: event.type,
        description: event.details,
        timestamp: new Date(event.timestamp).toISOString(),
        ip: event.ip || undefined,
      }));
    });
  }

  async getUserSecurityInfo(userId: number): Promise<{
    lastPasswordChange: string;
    publicKey: string;
    autoEncryptionEnabled: boolean;
  }> {
    return dbMonitor.trackQuery('getUserSecurityInfo', async () => {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      return {
        lastPasswordChange: new Date(user.lastPasswordChange).toISOString(),
        publicKey: user.publicKey || '',
        autoEncryptionEnabled: user.autoEncryptionEnabled,
      };
    });
  }

  // Friend request methods
  async sendFriendRequest(requesterId: number, receiverId: number): Promise<FriendRequest> {
    return dbMonitor.trackQuery('sendFriendRequest', async () => {
      // Check if users are not the same
      if (requesterId === receiverId) {
        throw new Error('Cannot send friend request to yourself');
      }

      // Check if request already exists
      const existingRequest = await db.query.friendRequests.findFirst({
        where: or(
          and(eq(friendRequests.requesterId, requesterId), eq(friendRequests.receiverId, receiverId)),
          and(eq(friendRequests.requesterId, receiverId), eq(friendRequests.receiverId, requesterId))
        )
      });

      if (existingRequest) {
        throw new Error('Friend request already exists');
      }

      const [result] = await db
        .insert(friendRequests)
        .values({
          requesterId,
          receiverId,
          status: 'pending'
        })
        .returning();

      return result;
    });
  }

  async getFriendRequests(userId: number): Promise<(FriendRequest & { requesterName: string; receiverName: string })[]> {
    return dbMonitor.trackQuery('getFriendRequests', async () => {
      const result = await db.query.friendRequests.findMany({
        where: or(
          eq(friendRequests.requesterId, userId),
          eq(friendRequests.receiverId, userId)
        ),
        with: {
          requester: true,
          receiver: true
        },
        orderBy: desc(friendRequests.createdAt)
      });

      return result.map(request => ({
        ...request,
        requesterName: request.requester.username,
        receiverName: request.receiver.username
      }));
    });
  }

  async respondToFriendRequest(requestId: number, userId: number, accept: boolean): Promise<boolean> {
    return dbMonitor.trackQuery('respondToFriendRequest', async () => {
      const request = await db.query.friendRequests.findFirst({
        where: and(
          eq(friendRequests.id, requestId),
          eq(friendRequests.receiverId, userId),
          eq(friendRequests.status, 'pending')
        )
      });

      if (!request) {
        return false;
      }

      const result = await db
        .update(friendRequests)
        .set({
          status: accept ? 'accepted' : 'rejected',
          updatedAt: nowTimestamp()
        })
        .where(eq(friendRequests.id, requestId))
        .returning();

      return result.length > 0;
    });
  }

  async getFriends(userId: number): Promise<User[]> {
    return dbMonitor.trackQuery('getFriends', async () => {
      const acceptedRequests = await db.query.friendRequests.findMany({
        where: and(
          or(
            eq(friendRequests.requesterId, userId),
            eq(friendRequests.receiverId, userId)
          ),
          eq(friendRequests.status, 'accepted')
        ),
        with: {
          requester: true,
          receiver: true
        }
      });

      const friends = acceptedRequests.map(request => 
        request.requesterId === userId ? request.receiver : request.requester
      );

      return friends;
    });
  }

  async checkFriendship(userId1: number, userId2: number): Promise<{ isFriend: boolean; requestStatus?: string }> {
    return dbMonitor.trackQuery('checkFriendship', async () => {
      const request = await db.query.friendRequests.findFirst({
        where: or(
          and(eq(friendRequests.requesterId, userId1), eq(friendRequests.receiverId, userId2)),
          and(eq(friendRequests.requesterId, userId2), eq(friendRequests.receiverId, userId1))
        )
      });

      if (!request) {
        return { isFriend: false };
      }

      return {
        isFriend: request.status === 'accepted',
        requestStatus: request.status
      };
    });
  }

  async updateUserBio(userId: number, bio: string): Promise<void> {
    return dbMonitor.trackQuery('updateUserBio', async () => {
      await db
        .update(users)
        .set({ bio })
        .where(eq(users.id, userId));
    });
  }

  async getPublicProfile(userId: number): Promise<{
    id: number;
    username: string;
    bio: string;
    avatar: string;
    joinedAt: string;
    threadsCreated: number;
    repliesPosted: number;
    isAdmin: boolean;
  } | null> {
    return dbMonitor.trackQuery('getPublicProfile', async () => {
      const user = await this.getUser(userId);
      if (!user) {
        return null;
      }

      // Get stats
      const threadsCreated = await db
        .select({ count: sql<number>`count(*)` })
        .from(threads)
        .where(eq(threads.authorId, userId));

      const repliesPosted = await db
        .select({ count: sql<number>`count(*)` })
        .from(replies)
        .where(eq(replies.authorId, userId));

      return {
        id: user.id,
        username: user.username,
        bio: user.bio || '',
        avatar: user.avatar || '',
        joinedAt: new Date(user.createdAt).toISOString(),
        threadsCreated: threadsCreated[0]?.count || 0,
        repliesPosted: repliesPosted[0]?.count || 0,
        isAdmin: user.isAdmin
      };
    });
  }
}

export const storage = new DatabaseStorage();