import { Router } from 'express';
import { storage } from './storage';
import { 
  getUserKeys, 
  generateUserKeyPair, 
  cleanupExpiredKeys,
  startKeyRotationService,
  autoEncryptMessage,
  autoDecryptMessage 
} from './autoEncryption';

const router = Router();

// Admin middleware - check if user is admin
const requireAdmin = (req: any, res: any, next: any) => {
  if (!req.user?.isAdmin) {
    return res.status(403).json({ message: "Admin access required" });
  }
  next();
};

// Get encryption statistics for dashboard
router.get('/admin/encryption/stats', requireAdmin, async (req, res) => {
  try {
    const users = await storage.getUsers();
    const totalUsers = users.length;
    const encryptionEnabled = users.filter(u => u.autoEncryptionEnabled).length;
    const encryptionDisabled = totalUsers - encryptionEnabled;

    // Get message statistics
    const allMessages = await storage.getAllMessages((req.user as any).id);
    const messagesEncrypted = allMessages.filter(m => m.isEncrypted).length;
    const messagesPlaintext = allMessages.length - messagesEncrypted;

    // Calculate key statistics
    const activeKeys = users.filter(u => u.autoEncryptionEnabled);
    const totalKeys = activeKeys.length;
    
    // Count keys expiring soon (within 3 days)
    const expiringSoon = activeKeys.filter(user => {
      const userKeys = getUserKeys(user.id);
      const now = new Date();
      const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
      return userKeys.expiresAt <= threeDaysFromNow;
    }).length;

    res.json({
      totalUsers,
      encryptionEnabled,
      encryptionDisabled,
      totalKeys,
      expiringSoon,
      messagesEncrypted,
      messagesPlaintext
    });
  } catch (error) {
    console.error('Error getting encryption stats:', error);
    res.status(500).json({ message: "Failed to get encryption statistics" });
  }
});

// Get user key information for dashboard
router.get('/admin/encryption/users', requireAdmin, async (req, res) => {
  try {
    const users = await storage.getUsers();
    
    const userKeyInfo = users.map(user => {
      const userKeys = user.autoEncryptionEnabled ? getUserKeys(user.id) : null;
      const now = new Date();
      
      let daysUntilExpiry = undefined;
      if (userKeys) {
        const timeDiff = userKeys.expiresAt.getTime() - now.getTime();
        daysUntilExpiry = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      }

      return {
        userId: user.id,
        username: user.username,
        hasKeys: !!userKeys,
        keyCreatedAt: userKeys?.createdAt.toISOString(),
        keyExpiresAt: userKeys?.expiresAt.toISOString(),
        encryptionEnabled: user.autoEncryptionEnabled,
        daysUntilExpiry: (daysUntilExpiry ?? 0) > 0 ? (daysUntilExpiry ?? 0) : 0
      };
    });

    res.json(userKeyInfo);
  } catch (error) {
    console.error('Error getting user key info:', error);
    res.status(500).json({ message: "Failed to get user key information" });
  }
});

// Rotate all encryption keys
router.post('/admin/encryption/rotate-all', requireAdmin, async (req, res) => {
  try {
    const users = await storage.getUsers();
    const encryptedUsers = users.filter(u => u.autoEncryptionEnabled);
    
    let rotatedCount = 0;
    for (const user of encryptedUsers) {
      try {
        generateUserKeyPair(user.id);
        rotatedCount++;
      } catch (error) {
        console.error(`Failed to rotate keys for user ${user.id}:`, error);
      }
    }

    res.json({
      message: `Successfully rotated keys for ${rotatedCount}/${encryptedUsers.length} users`,
      rotatedCount,
      totalUsers: encryptedUsers.length
    });
  } catch (error) {
    console.error('Error rotating all keys:', error);
    res.status(500).json({ message: "Failed to rotate encryption keys" });
  }
});

// Rotate encryption keys for specific user
router.post('/admin/encryption/rotate-user/:userId', requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (!user.autoEncryptionEnabled) {
      return res.status(400).json({ message: "User does not have encryption enabled" });
    }

    const newKeys = generateUserKeyPair(userId);
    
    res.json({
      message: `Successfully rotated encryption keys for ${user.username}`,
      keyExpiresAt: newKeys.expiresAt.toISOString()
    });
  } catch (error) {
    console.error('Error rotating user key:', error);
    res.status(500).json({ message: "Failed to rotate user encryption key" });
  }
});

// Toggle auto-rotation service
router.patch('/admin/encryption/auto-rotation', requireAdmin, async (req, res) => {
  try {
    const { enabled } = req.body;
    
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({ message: "Enabled must be a boolean" });
    }

    // Note: In a real implementation, you'd store this setting in the database
    // For now, we'll just respond with the setting
    if (enabled) {
      startKeyRotationService();
    }

    res.json({
      message: enabled ? "Auto-rotation enabled" : "Auto-rotation disabled",
      autoRotationEnabled: enabled
    });
  } catch (error) {
    console.error('Error toggling auto-rotation:', error);
    res.status(500).json({ message: "Failed to toggle auto-rotation" });
  }
});

// User endpoints for encryption toggle
router.patch('/user/auto-encryption', async (req, res) => {
  if (!req.user) return res.sendStatus(401);
  
  try {
    const { enabled } = req.body;
    
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({ message: "Enabled must be a boolean" });
    }

    // Update user's encryption preference in database
    await storage.updateUserEncryptionSetting(req.user.id, enabled);

    // If enabling encryption, generate initial keys
    if (enabled) {
      generateUserKeyPair(req.user.id);
    }

    res.json({
      message: enabled ? "Auto-encryption enabled" : "Auto-encryption disabled",
      autoEncryptionEnabled: enabled
    });
  } catch (error) {
    console.error('Error updating user encryption setting:', error);
    res.status(500).json({ message: "Failed to update encryption setting" });
  }
});

// Get current user's encryption status
router.get('/user/encryption-status', async (req, res) => {
  if (!req.user) {
    console.log('Encryption status: No user session found');
    return res.sendStatus(401);
  }
  
  console.log(`Encryption status: Fetching for user ID ${req.user.id}`);
  
  try {
    const user = await storage.getUser(req.user.id);
    if (!user) {
      console.log(`Encryption status: User not found in database for ID ${req.user.id}`);
      return res.status(404).json({ message: "User not found" });
    }

    console.log(`Encryption status: Found user ${user.username}, encryption enabled: ${user.autoEncryptionEnabled}`);

    let keyInfo = null;
    if (user.autoEncryptionEnabled) {
      try {
        const userKeys = getUserKeys(user.id);
        const now = new Date();
        const daysUntilExpiry = Math.ceil((userKeys.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        keyInfo = {
          hasKeys: true,
          createdAt: userKeys.createdAt.toISOString(),
          expiresAt: userKeys.expiresAt.toISOString(),
          daysUntilExpiry: Math.max(0, daysUntilExpiry)
        };
      } catch (keyError) {
        console.error('Error getting user keys:', keyError);
        // Don't fail the whole request if key retrieval fails
        keyInfo = {
          hasKeys: false,
          error: 'Unable to retrieve key information'
        };
      }
    }

    const response = {
      encryptionEnabled: user.autoEncryptionEnabled || false,
      keyInfo
    };
    
    console.log('Encryption status response:', response);
    res.json(response);
  } catch (error) {
    console.error('Error getting user encryption status:', error);
    res.status(500).json({ message: "Failed to get encryption status" });
  }
});

export default router;