import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Readable } from 'stream';
import { aiLogger } from './utils/logger';

interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

interface ChatResponse {
  content: string;
  tokenUsage: TokenUsage;
}

export interface Attachment {
  fileName: string;
  fileType: string;
  content: string;
}

type AIProvider = 'deepseek' | 'openrouter';

interface ProviderConfig {
  baseURL: string;
  defaultModel: string;
  maxTokens: number;
  headers?: Record<string, string>;
}

interface AIClientConfig {
  provider: AIProvider;
  apiKey: string;
  model?: string;
  maxRetries?: number;
  timeout?: number;
  baseDelay?: number;
  maxDelay?: number;
  siteUrl?: string;
  siteName?: string;
}

class AIClient {
  private static instances: Map<string, AIClient> = new Map();
  private readonly axiosInstance: AxiosInstance;
  private readonly config: Required<Omit<AIClientConfig, 'siteUrl' | 'siteName'>> & { siteUrl?: string; siteName?: string; };
  private readonly providerConfig: ProviderConfig;
  
  private static readonly PROVIDER_CONFIGS: Record<AIProvider, ProviderConfig> = {
    deepseek: {
      baseURL: 'https://api.deepseek.com',
      defaultModel: 'deepseek-chat',
      maxTokens: 64000
    },
    openrouter: {
      baseURL: 'https://openrouter.ai/api/v1',
      defaultModel: 'openai/gpt-3.5-turbo',
      maxTokens: 4096
    }
  };

  private readonly SYSTEM_PROMPT = `You are a professional AI assistant. Your responses should be clear, concise, and informative. You should:
- Provide accurate and detailed information
- Help users explore topics thoroughly
- Encourage critical thinking
- Share reliable sources when relevant
- Maintain a professional and supportive tone
- Keep responses focused and clear`;

  private constructor(config: AIClientConfig) {
    this.providerConfig = AIClient.PROVIDER_CONFIGS[config.provider];
    
    this.config = {
      provider: config.provider,
      apiKey: config.apiKey,
      model: config.model ?? this.providerConfig.defaultModel,
      maxRetries: config.maxRetries ?? 3,
      timeout: config.timeout ?? 30000,
      baseDelay: config.baseDelay ?? 1000,
      maxDelay: config.maxDelay ?? 10000,
      siteUrl: config.siteUrl,
      siteName: config.siteName
    };

    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    
    // Add OpenRouter specific headers
    if (config.provider === 'openrouter') {
      // OpenRouter recommends these headers for app attribution
      // Note: The header should be 'Referer' not 'HTTP-Referer'
      headers['Referer'] = config.siteUrl || process.env.SITE_URL || 'https://crow-ai.cloud';
      headers['X-Title'] = config.siteName || 'Crow-AI Community Forum';
      
      // Log headers for debugging
      aiLogger.debug('OpenRouter headers configured', {
        referer: headers['Referer'],
        title: headers['X-Title'],
        hasApiKey: !!this.config.apiKey,
        apiKeyLength: this.config.apiKey?.length
      }, 'openrouter-headers');
    }

    this.axiosInstance = axios.create({
      baseURL: this.providerConfig.baseURL,
      timeout: this.config.timeout,
      headers
    });

    this.axiosInstance.interceptors.response.use(
      response => response,
      this.handleRequestError.bind(this)
    );
  }

  public static getInstance(config: AIClientConfig): AIClient {
    const key = `${config.provider}-${config.model || 'default'}`;
    if (!AIClient.instances.has(key)) {
      AIClient.instances.set(key, new AIClient(config));
    }
    return AIClient.instances.get(key)!;
  }
  
  public getMaxTokens(): number {
    return this.providerConfig.maxTokens;
  }
  
  public getModel(): string {
    return this.config.model;
  }
  
  public getProvider(): AIProvider {
    return this.config.provider;
  }

  private async delay(retryCount: number): Promise<void> {
    const jitter = Math.random() * 200;
    const backoffDelay = Math.min(
      this.config.baseDelay * Math.pow(2, retryCount),
      this.config.maxDelay
    );
    await new Promise(resolve => setTimeout(resolve, backoffDelay + jitter));
  }

  private async handleRequestError(error: unknown): Promise<never> {
    // Ensure we're working with an AxiosError
    const axiosError = error as AxiosError;
    
    // Check specifically for client abort or canceled request
    if (axios.isCancel(axiosError)) {
      aiLogger.info('Request was canceled by the client', {
        url: axiosError.config?.url,
        method: axiosError.config?.method
      }, 'request-cancelled');
      
      // Throw a clean error for cancellations
      throw new Error('Request was cancelled by the client');
    }
    
    // Handle connection aborted errors specifically
    const errorMessage = (axiosError as any).message || '';
    const errorCode = (axiosError as any).code || '';
    
    if (errorCode === 'ECONNABORTED' || 
        errorMessage.includes('aborted') || 
        errorMessage.includes('canceled')) {
      
      aiLogger.warn('Connection aborted', {
        code: errorCode,
        message: errorMessage,
        url: (axiosError as any).config?.url,
        method: (axiosError as any).config?.method
      }, 'connection-aborted');
      
      throw new Error('Connection was aborted');
    }
    
    // Safely build error data for logging
    const errorData: Record<string, any> = {
      message: errorMessage,
      code: errorCode
    };
    
    // Only access response properties if they exist
    if ((axiosError as any).response) {
      errorData.status = (axiosError as any).response.status;
      errorData.data = (axiosError as any).response.data;
    }
    
    // Only access config properties if they exist
    if ((axiosError as any).config) {
      errorData.config = {
        url: (axiosError as any).config.url,
        method: (axiosError as any).config.method,
        headers: this.sanitizeHeaders((axiosError as any).config.headers),
        timeout: (axiosError as any).config.timeout
      };
    }
    
    // Log detailed error information
    aiLogger.error('AI API request failed', errorData, 'api');
    
    // For OpenRouter 401 errors, log more details
    if ((axiosError as any).response?.status === 401 && errorData.config?.url?.includes('openrouter')) {
      const responseData = (axiosError as any).response?.data;
      const errorMessage = responseData?.error?.message || responseData?.message || responseData?.error || 'Unknown error';
      
      // Log the complete response for debugging
      console.error('[OpenRouter 401 Response]:', JSON.stringify(responseData, null, 2));
      
      aiLogger.error('OpenRouter authentication failed', {
        errorMessage,
        fullResponse: JSON.stringify(responseData),
        responseType: typeof responseData,
        headers: this.sanitizeHeaders((axiosError as any).config?.headers),
        requestedModel: (axiosError as any).config?.data ? JSON.parse((axiosError as any).config?.data)?.model : 'unknown',
        suggestions: [
          'Verify API key is correct',
          'Check if API key has proper format (should start with "sk-or-")',
          'Ensure API key has not expired',
          'Verify the model you are trying to use is accessible with your API key'
        ]
      }, 'openrouter-auth');
    }

    const errorMessages: Record<number, string> = {
      400: 'The request format was invalid. Please try rephrasing your message.',
      401: 'API key authentication failed. Please check if the API key is valid.',
      402: 'API usage limit reached. Please try again in a few minutes.',
      422: 'The request parameters were invalid. Please try a different message.',
      429: 'Rate limit exceeded. Please wait a moment and try again.',
      500: 'The AI service is having issues. Please try again in a moment.',
      503: 'The AI service is temporarily down. Please try again shortly.',
      504: 'The request timed out. Please try again.'
    };

    const status = (axiosError as any).response?.status;
    let userErrorMessage = 'An unexpected error occurred. Please try again.';
    let errorContext = 'unknown';

    if (errorCode === 'ECONNABORTED') {
      userErrorMessage = 'The request took too long to complete. Please try again.';
      errorContext = 'timeout';
    } else if (errorCode === 'ECONNREFUSED' || errorCode === 'ENOTFOUND') {
      userErrorMessage = 'Unable to reach the AI service. Please check your connection and try again.';
      errorContext = 'connection';
    } else if (status && errorMessages[status]) {
      userErrorMessage = errorMessages[status];
      errorContext = `http-${status}`;
    }
    
    // Log user-friendly error message
    aiLogger.warn(`Returning error to user: ${userErrorMessage}`, { context: errorContext }, 'api-response');
    
    throw new Error(userErrorMessage);
  }
  
  // Helper method to remove sensitive data from headers before logging
  private sanitizeHeaders(headers: any): any {
    if (!headers) return {};
    const sanitized = { ...headers };
    
    // Remove Authorization header or replace with asterisks
    if (sanitized.Authorization || sanitized.authorization) {
      sanitized.Authorization = sanitized.Authorization ? 'Bearer ***' : undefined;
      sanitized.authorization = sanitized.authorization ? 'Bearer ***' : undefined;
    }
    
    return sanitized;
  }

  private shouldRetry(error: AxiosError, retryCount: number): boolean {
    const retryableStatuses = [408, 429, 500, 502, 503, 504];
    const retryableCodes = [
      'ECONNRESET',
      'ETIMEDOUT',
      'ECONNABORTED',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ENETUNREACH'
    ];

    const shouldRetry = retryCount < this.config.maxRetries && (
      retryableStatuses.includes((error as any).response?.status ?? 0) ||
      retryableCodes.includes((error as any).code ?? '') ||
      (error as any).message.includes('timeout')
    );

    const retryAttempt = retryCount + 1;
    
    if (shouldRetry) {
      // Log retry attempt
      aiLogger.warn(
        `Retrying request (attempt ${retryAttempt}/${this.config.maxRetries})`, 
        {
          retryCount: retryAttempt,
          maxRetries: this.config.maxRetries,
          errorCode: (error as any).code,
          status: (error as any).response?.status,
          url: (error as any).config?.url
        },
        'retry'
      );
    } else if (retryCount >= this.config.maxRetries) {
      // Log max retries reached
      aiLogger.error(
        `Max retries reached (${retryCount}/${this.config.maxRetries})`,
        {
          errorCode: (error as any).code,
          status: (error as any).response?.status,
          message: (error as any).message
        },
        'retry-exhausted'
      );
    }

    return shouldRetry;
  }

  public async getChatResponse(
    messages: Message[],
    attachments?: Attachment[],
    stream: boolean = false,
    userId?: number,
    sessionId?: number | null,
    isDeepThinking: boolean = false
  ): Promise<ChatResponse | NodeJS.ReadableStream> {
    let retryCount = 0;
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Always include system prompt as the first message
    const allMessages = [
      { role: 'system', content: this.SYSTEM_PROMPT }
    ];

    // Add the conversation history
    allMessages.push(...messages);

    // Add attachments if any
    if (attachments?.length) {
      const attachmentContent = attachments
        .map(att => `[Attachment: ${att.fileName}]\n${att.content}`)
        .join('\n\n');
      allMessages.push({ role: 'user', content: attachmentContent });
    }

    // Customize system prompt for deep thinking mode
    if (isDeepThinking) {
      allMessages[0].content += `\n\nYou're in deep thinking mode. Take your time to provide thorough, well-reasoned answers. Break down complex problems step by step. Consider multiple perspectives and provide detailed explanations.`;
    }

    // Determine the model to use
    let modelToUse = this.config.model;
    
    // For DeepSeek provider, switch to reasoner model in deep thinking mode
    if (this.config.provider === 'deepseek' && isDeepThinking) {
      modelToUse = 'deepseek-reasoner';
    }
    
    // Build the payload - OpenRouter might need different parameters
    const payload: any = {
      model: modelToUse,
      messages: allMessages,
      temperature: isDeepThinking ? 0.5 : 0.7, // Lower temperature for more focused responses in deep thinking mode
      max_tokens: isDeepThinking ? 4000 : 2000, // Larger response limit for deep thinking
      stream
    };
    
    // Add provider-specific parameters
    if (this.config.provider === 'openrouter') {
      // OpenRouter supports additional parameters
      payload.top_p = 1;
      payload.frequency_penalty = 0;
      payload.presence_penalty = 0;
      
      // Log the exact model being used for OpenRouter
      aiLogger.debug('OpenRouter request payload', {
        model: payload.model,
        messageCount: payload.messages.length,
        temperature: payload.temperature,
        maxTokens: payload.max_tokens,
        stream: payload.stream
      }, 'openrouter-payload');
    }

    // Log the request details
    aiLogger.debug('Preparing AI request', {
      requestId,
      messageCount: allMessages.length,
      lastUserMessage: messages.length > 0 ? messages[messages.length - 1].content.substring(0, 100) + '...' : 'None',
      stream,
      isDeepThinking,
      userId,
      sessionId,
      attachmentsCount: attachments?.length || 0
    }, 'request-prep');

    // Log the actual request if user info is available
    if (userId) {
      // Save actual prompt content to logs
      const lastUserMessage = messages.length > 0 ? messages[messages.length - 1].content : '';
      aiLogger.request(
        userId, 
        sessionId || null,
        lastUserMessage,
        isDeepThinking
      );
    }

    while (true) {
      try {
        // Performance measurement
        const requestStartTime = Date.now();
        
        aiLogger.debug(`Sending request to API`, {
          requestId,
          endpoint: '/chat/completions',
          model: payload.model,
          stream,
          retryCount
        }, 'api-request');
        
        const response = await this.axiosInstance.post(
          '/chat/completions',
          payload,
          { responseType: stream ? 'stream' : 'json' }
        );
        
        const requestDuration = Date.now() - requestStartTime;
        
        // Log success
        aiLogger.debug(`API request successful`, {
          requestId,
          duration: requestDuration,
          status: response.status,
          hasData: !!response.data
        }, 'api-response');
        
        // Track performance
        aiLogger.performance('api-request', requestDuration, {
          requestId,
          stream,
          isDeepThinking
        });

        if (stream) {
          // For streaming responses, we can't log content since it's a stream
          aiLogger.log(`Streaming response started`, {
            requestId,
            totalDuration: Date.now() - startTime
          }, 'stream-response');
          
          if (userId) {
            aiLogger.response(
              userId,
              sessionId || null,
              'success',
              0, // Cannot measure content length for streams
              Date.now() - startTime
            );
          }
          
          return response.data;
        } else {
          const content = response.data?.choices?.[0]?.message?.content;
          const usage = response.data?.usage;
          
          if (!content) {
            aiLogger.error(`Invalid response format from API`, {
              requestId,
              responseData: response.data
            }, 'response-validation');
            throw new Error('Invalid response format from API');
          }
          
          const tokenUsage: TokenUsage = {
            promptTokens: usage?.prompt_tokens || 0,
            completionTokens: usage?.completion_tokens || 0,
            totalTokens: usage?.total_tokens || 0
          };
          
          const totalDuration = Date.now() - startTime;
          
          // Log successful response with token usage
          aiLogger.debug(`Response received successfully`, {
            requestId,
            contentLength: content.length,
            totalDuration,
            tokenUsage,
            firstChars: content.substring(0, 50) + '...'
          }, 'response-success');
          
          // Log metrics if user info available
          if (userId) {
            aiLogger.response(
              userId,
              sessionId || null,
              'success',
              content.length,
              totalDuration
            );
          }
          
          return {
            content,
            tokenUsage
          };
        }
      } catch (error: any) {
        const requestDuration = Date.now() - startTime;
        
        if (error instanceof AxiosError && this.shouldRetry(error, retryCount)) {
          await this.delay(retryCount);
          retryCount++;
          continue;
        }
        
        // Log failed request metrics
        if (userId) {
          aiLogger.response(
            userId,
            sessionId || null,
            'error',
            0,
            requestDuration
          );
        }
        
        // Enhance error with request context
        error.requestId = requestId;
        error.requestDuration = requestDuration;
        
        throw error;
      }
    }
  }
}

export async function getChatResponse(
  messages: Message[],
  attachments?: Attachment[],
  stream: boolean = false,
  userId?: number,
  sessionId?: number | null,
  isDeepThinking: boolean = false,
  provider: AIProvider = 'deepseek',
  model?: string
): Promise<ChatResponse | NodeJS.ReadableStream> {
  try {
    // Start performance tracking
    const startTime = Date.now();
    
    // Check for API key based on provider with fallback support
    let apiKey: string | undefined;
    
    if (provider === 'deepseek') {
      apiKey = process.env.DEEPSEEK_API_KEY;
    } else {
      // For OpenRouter, try primary key first, then fallback
      apiKey = process.env.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY2;
      
      // Debug logging for OpenRouter key (safely log first few chars)
      if (apiKey) {
        // Validate OpenRouter API key format
        if (!apiKey.startsWith('sk-or-')) {
          aiLogger.warn('OpenRouter API key might be invalid - should start with "sk-or-"', {
            actualPrefix: apiKey.substring(0, 6),
            expectedPrefix: 'sk-or-'
          }, 'api-key-format');
        }
        
        const keyPreview = apiKey.substring(0, 15) + '...';
        aiLogger.info(`Using OpenRouter API key`, { 
          provider,
          keyPreview,
          keyLength: apiKey.length,
          validFormat: apiKey.startsWith('sk-or-'),
          hasPrimaryKey: !!process.env.OPENROUTER_API_KEY,
          hasFallbackKey: !!process.env.OPENROUTER_API_KEY2,
          usingFallback: !process.env.OPENROUTER_API_KEY && !!process.env.OPENROUTER_API_KEY2
        }, 'api-key');
      } else {
        aiLogger.error('No OpenRouter API key found', { 
          hasPrimaryKey: !!process.env.OPENROUTER_API_KEY,
          hasFallbackKey: !!process.env.OPENROUTER_API_KEY2 
        }, 'api-key-missing');
      }
    }
      
    if (!apiKey) {
      const keyName = provider === 'deepseek' ? 'DEEPSEEK_API_KEY' : 'OPENROUTER_API_KEY/OPENROUTER_API_KEY2';
      aiLogger.error(`${keyName} not configured`, new Error('Missing API key'), 'config');
      throw new Error(`${keyName} not configured`);
    }
    
    aiLogger.debug('Initializing AIClient', { 
      userId, 
      sessionId, 
      isDeepThinking,
      hasAttachments: !!attachments?.length,
      stream,
      provider,
      model: model || (provider === 'deepseek' ? 'deepseek-chat' : 'openai/gpt-3.5-turbo')
    }, 'init');

    // For automated testing - allow a longer timeout via environment variable
    const defaultTimeout = isDeepThinking ? 60000 : 30000;
    const configuredTimeout = process.env.AI_REQUEST_TIMEOUT ? 
      parseInt(process.env.AI_REQUEST_TIMEOUT, 10) : defaultTimeout;
    
    // Add 20% buffer to timeout for tests to avoid timing issues
    const testModeEnabled = process.env.NODE_ENV === 'test' || process.env.TEST_MODE === 'true';
    const finalTimeout = testModeEnabled ? configuredTimeout * 1.2 : configuredTimeout;
    
    aiLogger.debug('Setting AI request timeout', {
      baseTimeout: defaultTimeout,
      configuredTimeout,
      finalTimeout,
      testMode: testModeEnabled
    }, 'timeout-config');

    const client = AIClient.getInstance({
      provider,
      apiKey,
      model: model || (provider === 'deepseek' ? 'deepseek-chat' : 'openai/gpt-3.5-turbo'),
      maxRetries: 3,
      timeout: finalTimeout, // Use calculated timeout
      baseDelay: 1000,
      maxDelay: 10000,
      siteUrl: process.env.OPENROUTER_SITE_URL,
      siteName: process.env.OPENROUTER_SITE_NAME
    });
    
    // Log client initialization time
    aiLogger.performance('client-init', Date.now() - startTime);

    const response = await client.getChatResponse(messages, attachments, stream, userId, sessionId, isDeepThinking);
    
    // For streaming responses, return the stream directly
    if (stream) {
      return response as NodeJS.ReadableStream;
    }
    
    // For non-streaming responses, return the ChatResponse with token usage
    return response as ChatResponse;
  } catch (error: any) {
    // Log any unexpected errors
    aiLogger.error('Unexpected error in getChatResponse', error, 'unexpected');
    throw error;
  }
}
