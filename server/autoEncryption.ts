import { randomBytes } from 'crypto';
import nacl from 'tweetnacl';
import { storage } from './storage';

// Use Node.js Buffer instead of naclUtil for server-side encoding
const encodeBase64 = (array: Uint8Array): string => {
  return Buffer.from(array).toString('base64');
};

const decodeBase64 = (str: string): Uint8Array => {
  return new Uint8Array(Buffer.from(str, 'base64'));
};

const decodeUTF8 = (str: string): Uint8Array => {
  return new TextEncoder().encode(str);
};

const encodeUTF8 = (array: Uint8Array): string => {
  return new TextDecoder().decode(array);
};

/**
 * Automatic Encryption Management System
 * - Auto-generates strong encryption keys for users
 * - Rotates keys every 15 days automatically
 * - Handles all encryption server-side
 * - No user interaction required
 */

interface UserKeyPair {
  publicKey: string;
  secretKey: string;
  createdAt: Date;
  expiresAt: Date;
}

const userKeys = new Map<number, UserKeyPair>();
const KEY_ROTATION_DAYS = 15;

/**
 * Generate a strong encryption key pair for a user
 */
export function generateUserKeyPair(userId: number): UserKeyPair {
  const keyPair = nacl.box.keyPair();
  const now = new Date();
  const expiresAt = new Date(now.getTime() + (KEY_ROTATION_DAYS * 24 * 60 * 60 * 1000));
  
  const userKeyPair: UserKeyPair = {
    publicKey: encodeBase64(keyPair.publicKey),
    secretKey: encodeBase64(keyPair.secretKey),
    createdAt: now,
    expiresAt: expiresAt
  };
  
  userKeys.set(userId, userKeyPair);
  console.log(`[AutoEncryption] Generated new key pair for user ${userId}, expires: ${expiresAt.toISOString()}`);
  return userKeyPair;
}

/**
 * Get or generate encryption keys for a user
 */
export function getUserKeys(userId: number): UserKeyPair {
  let keys = userKeys.get(userId);
  
  if (!keys || keys.expiresAt <= new Date()) {
    console.log(`[AutoEncryption] Creating new keys for user ${userId}`);
    keys = generateUserKeyPair(userId);
  }
  
  return keys;
}

/**
 * Encrypt a message between two users
 */
export function autoEncryptMessage(senderId: number, recipientId: number, message: string): {
  encryptedContent: string;
  nonce: string;
} | null {
  try {
    const senderKeys = getUserKeys(senderId);
    const recipientKeys = getUserKeys(recipientId);
    
    // Generate random nonce
    const nonce = nacl.randomBytes(24);
    
    // Convert message to bytes
    const messageBytes = decodeUTF8(message);
    
    // Encrypt using sender's secret key and recipient's public key
    const encrypted = nacl.box(
      messageBytes,
      nonce,
      decodeBase64(recipientKeys.publicKey),
      decodeBase64(senderKeys.secretKey)
    );
    
    if (!encrypted) {
      console.error(`[AutoEncryption] Failed to encrypt message from ${senderId} to ${recipientId}`);
      return null;
    }
    
    return {
      encryptedContent: encodeBase64(encrypted),
      nonce: encodeBase64(nonce)
    };
  } catch (error) {
    console.error(`[AutoEncryption] Encryption error:`, error);
    return null;
  }
}

/**
 * Decrypt a message for a user
 */
export function autoDecryptMessage(
  recipientId: number, 
  senderId: number, 
  encryptedContent: string, 
  nonce: string
): string | null {
  try {
    const recipientKeys = getUserKeys(recipientId);
    const senderKeys = getUserKeys(senderId);
    
    // Decrypt using recipient's secret key and sender's public key
    const decrypted = nacl.box.open(
      decodeBase64(encryptedContent),
      decodeBase64(nonce),
      decodeBase64(senderKeys.publicKey),
      decodeBase64(recipientKeys.secretKey)
    );
    
    if (!decrypted) {
      console.warn(`[AutoEncryption] Failed to decrypt message from ${senderId} to ${recipientId}`);
      return null;
    }
    
    return encodeUTF8(decrypted);
  } catch (error) {
    console.error(`[AutoEncryption] Decryption error:`, error);
    return null;
  }
}

/**
 * Clean up expired keys periodically
 */
export function cleanupExpiredKeys(): void {
  const now = new Date();
  for (const [userId, keys] of Array.from(userKeys.entries())) {
    if (keys.expiresAt <= now) {
      userKeys.delete(userId);
      console.log(`[AutoEncryption] Cleaned up expired keys for user ${userId}`);
    }
  }
}

/**
 * Start automatic key rotation service
 */
export function startKeyRotationService(): void {
  console.log('[AutoEncryption] Key rotation service started - keys rotate every 15 days');
  
  // Clean up expired keys every hour
  setInterval(() => {
    cleanupExpiredKeys();
  }, 60 * 60 * 1000);
  
  // Optional: Pre-generate keys for active users every day
  setInterval(() => {
    console.log('[AutoEncryption] Running periodic key maintenance');
  }, 24 * 60 * 60 * 1000);
}

/**
 * Get public key for a user (for API endpoints)
 */
export function getUserPublicKey(userId: number): string {
  const keys = getUserKeys(userId);
  return keys.publicKey;
}

/**
 * Check if a user has encryption enabled (always true with auto encryption)
 */
export function isUserEncryptionEnabled(): boolean {
  return true;
}