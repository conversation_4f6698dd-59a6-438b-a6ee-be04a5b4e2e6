import { storage } from "./storage";
import { type InsertNotification } from "@shared/schema";

/**
 * Notification Service - Handles creating and sending notifications
 */

export class NotificationService {
  
  // Create a new message notification
  static async createMessageNotification(senderId: number, recipientId: number, messageContent: string) {
    // Don't notify self
    if (senderId === recipientId) return;

    // Get sender info
    const sender = await storage.getUser(senderId);
    if (!sender) return;

    const notification: InsertNotification = {
      userId: recipientId,
      type: 'message',
      title: 'New Message',
      message: `${sender.username} sent you a message: ${messageContent.substring(0, 50)}${messageContent.length > 50 ? '...' : ''}`,
      actionUrl: '/messages',
      senderId: senderId,
      relatedType: 'message'
    };

    await storage.createNotification(notification);
  }

  // Create a thread reply notification
  static async createThreadReplyNotification(replyAuthorId: number, threadId: number, threadAuthorId: number, replyContent: string) {
    // Don't notify self
    if (replyAuthorId === threadAuthorId) return;

    // Get reply author info
    const replyAuthor = await storage.getUser(replyAuthorId);
    if (!replyAuthor) return;

    // Get thread info
    const thread = await storage.getThreadById(threadId);
    if (!thread) return;

    const notification: InsertNotification = {
      userId: threadAuthorId,
      type: 'thread_reply',
      title: 'New Reply to Your Thread',
      message: `${replyAuthor.username} replied to "${thread.title}": ${replyContent.substring(0, 50)}${replyContent.length > 50 ? '...' : ''}`,
      actionUrl: `/thread/${threadId}`,
      senderId: replyAuthorId,
      relatedId: threadId,
      relatedType: 'thread'
    };

    await storage.createNotification(notification);
  }

  // Create a thread like notification
  static async createThreadLikeNotification(voterId: number, threadId: number, threadAuthorId: number, isUpvote: boolean) {
    // Don't notify self or downvotes
    if (voterId === threadAuthorId || !isUpvote) return;

    // Get voter info
    const voter = await storage.getUser(voterId);
    if (!voter) return;

    // Get thread info
    const thread = await storage.getThreadById(threadId);
    if (!thread) return;

    const notification: InsertNotification = {
      userId: threadAuthorId,
      type: 'thread_like',
      title: 'Thread Liked',
      message: `${voter.username} liked your thread "${thread.title}"`,
      actionUrl: `/thread/${threadId}`,
      senderId: voterId,
      relatedId: threadId,
      relatedType: 'thread'
    };

    await storage.createNotification(notification);
  }

  // Create an admin action notification
  static async createAdminActionNotification(targetUserId: number, adminId: number, action: string, reason?: string) {
    // Get admin info
    const admin = await storage.getUser(adminId);
    if (!admin) return;

    const notification: InsertNotification = {
      userId: targetUserId,
      type: 'admin_action',
      title: 'Admin Action',
      message: `${admin.username} ${action}${reason ? `: ${reason}` : ''}`,
      senderId: adminId,
      relatedType: 'user'
    };

    await storage.createNotification(notification);
  }

  // Create a system notification
  static async createSystemNotification(userId: number, title: string, message: string, actionUrl?: string) {
    const notification: InsertNotification = {
      userId,
      type: 'system',
      title,
      message,
      actionUrl
    };

    await storage.createNotification(notification);
  }

  // Create a mention notification (for future use)
  static async createMentionNotification(mentionerId: number, mentionedUserId: number, content: string, actionUrl: string) {
    // Don't notify self
    if (mentionerId === mentionedUserId) return;

    // Get mentioner info
    const mentioner = await storage.getUser(mentionerId);
    if (!mentioner) return;

    const notification: InsertNotification = {
      userId: mentionedUserId,
      type: 'mention',
      title: 'You were mentioned',
      message: `${mentioner.username} mentioned you: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
      actionUrl,
      senderId: mentionerId,
      relatedType: 'mention'
    };

    await storage.createNotification(notification);
  }

  // Create news notifications for all users
  static async createNewsNotification(authorId: number, newsId: number, title: string, content: string) {
    // Get all users
    const users = await storage.getUsers();
    
    // Get author info
    const author = await storage.getUser(authorId);
    if (!author) return;

    // Create notifications for all users except the author
    const notifications: InsertNotification[] = users
      .filter(user => user.id !== authorId)
      .map(user => ({
        userId: user.id,
        type: 'news' as const,
        title: `Fresh News: ${title}`,
        message: `${author.username} published: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`,
        actionUrl: '/news',
        senderId: authorId,
        relatedId: newsId,
        relatedType: 'news'
      }));

    // Create all notifications
    for (const notification of notifications) {
      await storage.createNotification(notification);
    }
  }

  // Friend request notifications
  static async createFriendRequestNotification(requesterId: number, receiverId: number, requesterName: string) {
    const notification: InsertNotification = {
      userId: receiverId,
      type: 'friend_request',
      title: 'New Friend Request',
      message: `${requesterName} sent you a friend request`,
      actionUrl: '/friends',
      senderId: requesterId,
      relatedType: 'friend_request'
    };

    await storage.createNotification(notification);
  }

  static async createFriendAcceptNotification(accepterId: number, requesterId: number, accepterName: string) {
    const notification: InsertNotification = {
      userId: requesterId,
      type: 'friend_accept',
      title: 'Friend Request Accepted',
      message: `${accepterName} accepted your friend request`,
      actionUrl: '/friends',
      senderId: accepterId,
      relatedType: 'friend_accept'
    };

    await storage.createNotification(notification);
  }
}