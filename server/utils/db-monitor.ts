import { performance } from 'perf_hooks';
import { executeWithRetry } from '../db';

interface QueryMetrics {
  operation: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

class DatabaseMonitor {
  private metrics: QueryMetrics[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 queries
  private readonly slowQueryThreshold = 100; // ms
  private queryTimeoutCache = new Map<string, number>(); // Cache to track frequent slow queries
  
  async trackQuery<T>(operation: string, queryFn: () => Promise<T>, timeout = 10000): Promise<T> {
    const startTime = performance.now();
    let success = true;
    let error: string | undefined;
    let timeoutId: NodeJS.Timeout | null = null;
    
    // Create a timeout promise to abort extremely slow queries
    const timeoutPromise = new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error(`Query timeout warning: ${operation} exceeded ${timeout}ms`);
        reject(new Error(`Query timeout: ${operation} exceeded ${timeout}ms`));
      }, timeout);
    });
    
    try {
      // Only enforce timeout for operations that have been consistently slow
      const slowQueryCount = this.queryTimeoutCache.get(operation) || 0;
      
      // Use executeWithRetry for robust database operations with automatic reconnection
      // Also race the query against the timeout if it's been slow frequently
      const result = slowQueryCount > 5 
        ? await Promise.race([executeWithRetry(() => queryFn()), timeoutPromise])
        : await executeWithRetry(() => queryFn());
        
      // Clear the timeout if it's still active
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      return result as T;
    } catch (e) {
      success = false;
      error = e instanceof Error ? e.message : 'Unknown error';
      
      // Special handling for database connection errors
      if (error.includes('terminating connection') || 
          error.includes('Connection terminated') ||
          error.includes('Connection refused')) {
        console.error(`Database connection error in operation '${operation}': ${error}`);
      }
      
      // Clear the timeout if it's still active
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      throw e;
    } finally {
      const duration = performance.now() - startTime;
      this.addMetric({
        operation,
        duration,
        timestamp: new Date(),
        success,
        error
      });
      
      // Update the slow query cache
      if (duration > this.slowQueryThreshold) {
        const count = this.queryTimeoutCache.get(operation) || 0;
        this.queryTimeoutCache.set(operation, count + 1);
      }
    }
  }

  private addMetric(metric: QueryMetrics) {
    this.metrics.push(metric);
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift(); // Remove oldest metric
    }
    
    // Log slow queries (over 100ms)
    if (metric.duration > 100) {
      console.warn(`Slow query detected: ${metric.operation} took ${metric.duration.toFixed(2)}ms`);
    }

    // Log failed queries
    if (!metric.success) {
      console.error(`Query failed: ${metric.operation}`, {
        error: metric.error,
        duration: metric.duration
      });
    }
  }

  getMetrics() {
    return {
      metrics: this.metrics,
      summary: {
        total: this.metrics.length,
        successful: this.metrics.filter(m => m.success).length,
        failed: this.metrics.filter(m => !m.success).length,
        averageDuration: this.metrics.reduce((acc, m) => acc + m.duration, 0) / this.metrics.length,
        slowQueries: this.metrics.filter(m => m.duration > 100).length
      }
    };
  }

  clearMetrics() {
    this.metrics = [];
  }
}

export const dbMonitor = new DatabaseMonitor();
