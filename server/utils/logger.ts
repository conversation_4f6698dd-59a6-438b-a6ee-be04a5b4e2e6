import fs from 'fs';
import path from 'path';

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Define log files
const aiLogFile = path.join(logsDir, 'ai-chat.log');
const aiErrorLogFile = path.join(logsDir, 'ai-chat-error.log');
const aiDebugLogFile = path.join(logsDir, 'ai-chat-debug.log');
const aiRequestLogFile = path.join(logsDir, 'ai-chat-requests.log');
const aiResponseLogFile = path.join(logsDir, 'ai-chat-responses.log');

// Log with severity levels and customizable output
export const aiLogger = {
  debug: (message: string, data?: any, context: string = 'general') => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - DEBUG [${context}]: ${message}${data ? '\nData: ' + JSON.stringify(data, null, 2) : ''}\n`;
    
    // Write to debug file
    fs.appendFileSync(aiDebugLogFile, logEntry);
    
    // Only log to console in development
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`[AI Debug][${context}] ${message}`);
      if (data) console.debug(`[AI Debug Data][${context}]`, data);
    }
  },
  
  // Added info method for more granular logging
  info: (message: string, data?: any, context: string = 'general') => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - INFO [${context}]: ${message}${data ? '\nData: ' + JSON.stringify(data, null, 2) : ''}\n`;
    
    // Write to file, using the same as general log file
    fs.appendFileSync(aiLogFile, logEntry);
    
    // Also log to console, with slightly different label to differentiate
    console.info(`[AI Info][${context}] ${message}`);
    if (data) console.info(`[AI Info Data][${context}]`, data);
  },
  
  log: (message: string, data?: any, context: string = 'general') => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - INFO [${context}]: ${message}${data ? '\nData: ' + JSON.stringify(data, null, 2) : ''}\n`;
    
    // Write to file
    fs.appendFileSync(aiLogFile, logEntry);
    
    // Also log to console
    console.log(`[AI Chat][${context}] ${message}`);
    if (data) console.log(`[AI Chat Data][${context}]`, data);
  },
  
  warn: (message: string, data?: any, context: string = 'general') => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - WARN [${context}]: ${message}${data ? '\nData: ' + JSON.stringify(data, null, 2) : ''}\n`;
    
    // Write to file
    fs.appendFileSync(aiLogFile, logEntry);
    
    // Also log to console
    console.warn(`[AI Chat Warning][${context}] ${message}`);
    if (data) console.warn(`[AI Chat Warning Data][${context}]`, data);
  },
  
  error: (message: string, error: any, context: string = 'general') => {
    const timestamp = new Date().toISOString();
    
    // Safe JSON stringify that handles circular references
    const safeStringify = (obj: any) => {
      const seen = new WeakSet();
      return JSON.stringify(obj, (key, val) => {
        if (val != null && typeof val === 'object') {
          if (seen.has(val)) return '[Circular]';
          seen.add(val);
        }
        return val;
      }, 2);
    };
    
    const errorDetails = error instanceof Error 
      ? `${error.name}: ${error.message}\n${error.stack}`
      : safeStringify(error);
    
    const logEntry = `${timestamp} - ERROR [${context}]: ${message}\nError Details: ${errorDetails}\n`;
    
    // Write to both general and error-specific file
    fs.appendFileSync(aiLogFile, logEntry);
    fs.appendFileSync(aiErrorLogFile, logEntry);
    
    // Also log to console
    console.error(`[AI Chat Error][${context}] ${message}`);
    console.error(error);
  },
  
  request: (userId: number, sessionId: number | null, message: string, isDeepThinking: boolean) => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - REQUEST: UserId: ${userId}, SessionId: ${sessionId}, DeepThinking: ${isDeepThinking}\nPrompt: ${message}\n`;
    
    // Write to request file
    fs.appendFileSync(aiRequestLogFile, logEntry);
    
    // Also log summary to main file
    fs.appendFileSync(aiLogFile, `${timestamp} - REQUEST: UserId: ${userId}, SessionId: ${sessionId}, DeepThinking: ${isDeepThinking}\n`);
    
    // Console output
    console.log(`[AI Request] User: ${userId}, Session: ${sessionId}, DeepThinking: ${isDeepThinking}`);
  },
  
  response: (userId: number, sessionId: number | null, status: 'success' | 'error', responseLength: number, timeTaken: number) => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - RESPONSE: UserId: ${userId}, SessionId: ${sessionId}, Status: ${status}, Length: ${responseLength} chars, Time: ${timeTaken}ms\n`;
    
    // Write to response file
    fs.appendFileSync(aiResponseLogFile, logEntry);
    
    // Also log to main file
    fs.appendFileSync(aiLogFile, logEntry);
    
    // Console output
    console.log(`[AI Response] User: ${userId}, Session: ${sessionId}, Status: ${status}, Length: ${responseLength} chars, Time: ${timeTaken}ms`);
  },
  
  // Track performance metrics
  performance: (operation: string, timeTaken: number, metadata: any = {}) => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - PERFORMANCE: Operation: ${operation}, Time: ${timeTaken}ms, Metadata: ${JSON.stringify(metadata)}\n`;
    
    // Write to file
    fs.appendFileSync(aiLogFile, logEntry);
    
    // Console output if significant delay
    if (timeTaken > 1000) {
      console.warn(`[AI Performance] Slow operation: ${operation} took ${timeTaken}ms`);
    }
  }
};
