import type { Express } from "express";
import express from "express";
import { createServer, type Server } from "http";
import { setupAuth } from "./auth";
import { SupabaseStorage } from "./supabase-storage";

const storage = new SupabaseStorage();
import { getChatResponse } from "./ai";
import { comparePasswords, hashPassword } from "./auth";
import { NotificationService } from "./notificationService";
import { insertThreadSchema, insertReplySchema, insertChatRoomMessageSchema, insertMessageSchema, insertAIChatSessionSchema, insertAIChatSchema, insertNewsSchema, insertFriendRequestSchema, updateBioSchema } from "@shared/schema";
import { Readable } from 'stream';
import { aiLogger } from './utils/logger';
import { z } from "zod";
import passport from 'passport';
import { and, or, eq, gt, desc, inArray, sql } from "drizzle-orm";
import { db } from "./db";
import { userEvents, users, threads } from "@shared/schema";
import { spawn } from 'child_process';
import fs from 'fs';
import multer from 'multer';
import sharp from 'sharp';
import path from 'path';

// Add message schema validation
const messageSchema = z.object({
  recipientId: z.number(),
  content: z.string().min(1, "Message cannot be empty").max(1000, "Message too long")
});

interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// User interface should match the database schema including date types
interface User {
  id: number;
  username: string;
  password?: string; 
  avatar?: string | null; 
  isAdmin?: boolean; 
  suspendedUntil?: Date | null; 
  bannedAt?: Date | null; 
  banReason?: string | null;
  createdAt?: Date;
  lastPasswordChange?: Date;
  creationIp?: string | null;
  lastLoginIp?: string | null;
  lastLoginAt?: Date | null;
}

const THREAD_RATE_LIMIT = new Map<number, number>(); 
const THREAD_COOLDOWN = 30000;

// Configure multer for avatar uploads
const avatarStorage = multer.memoryStorage();
const avatarUpload = multer({
  storage: avatarStorage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));
    }
  }
}); 

interface DatabaseStorage {
    getDbMetrics(): any;
    clearDbMetrics(): void;
    getUserWithDetails(userId: number): Promise<User | null>;
    updateUserLoginInfo(userId: number, ipAddress: string): Promise<void>;
    updateUserAdminStatus(userId: number, isAdmin: boolean, adminId: number):Promise<void>;
    suspendUser(userId: number, until: Date, reason: string, adminId: number): Promise<void>;
    banUser(userId: number, reason: string, adminId: number): Promise<void>;
    unsuspendUser(userId: number, adminId: number): Promise<void>;
    unbanUser(userId: number, adminId: number): Promise<void>;
    getUserTimeline(userId: number): Promise<any>; 
    getUsers(): Promise<User[]>; 
    getUser(userId: number): Promise<User | null>; 
    createMessage(message: any): Promise<any>; 
    getMessagesBetweenUsers(userId1: number, userId2: number): Promise<any>; 
    getAllMessages(userId: number): Promise<any[]>; 
    createChatRoomMessage(message: any): Promise<any>; 
    getChatRoomMessages(roomId: string): Promise<any[]>; 
    updateUserAvatar(userId: number, avatar: string): Promise<void>; 
    updateUserPassword(userId: number, password: string): Promise<void>; 
    deleteUser(userId: number): Promise<void>; 
    getThreads(): Promise<any[]>;
    getThreadById(threadId: number): Promise<any | null>;
    createThread(thread: any): Promise<any>;
    deleteThread(threadId: number, userId: number): Promise<boolean>;
    getRepliesByThreadId(threadId: number): Promise<any[]>;
    createReply(reply: any): Promise<any>;
    deleteReply(replyId: number, userId: number): Promise<boolean>;

}

export async function registerRoutes(app: Express): Promise<Server> {
  // Serve static avatar files
  app.use('/static', express.static(path.join(process.cwd(), 'static')));
  
  setupAuth(app);
  const httpServer = createServer(app);

  app.get("/api/threads", async (_req, res) => {
    const threads = await storage.getThreads();
    res.json(threads);
  });

  app.get("/api/threads/:threadId", async (req, res) => {
    try {
      const thread = await storage.getThreadById(parseInt(req.params.threadId));
      if (!thread) {
        return res.status(404).json({ message: "Thread not found" });
      }
      res.json(thread);
    } catch (error) {
      console.error('Error fetching thread:', error);
      res.status(500).json({ message: "Error fetching thread" });
    }
  });

  // Offensive language filter
  const containsOffensiveLanguage = (text: string): boolean => {
    const offensiveWords = [
      'offensive', 'slur', 'profanity', 'hate speech', 'racism', 'inappropriate',
      // Add more offensive words as needed
    ];
    const lowerText = text.toLowerCase();
    return offensiveWords.some(word => lowerText.includes(word));
  };

  app.post("/api/threads", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    // Check rate limit
    const lastCreation = THREAD_RATE_LIMIT.get(req.user.id) || 0;
    const now = Date.now();
    if (now - lastCreation < THREAD_COOLDOWN) {
      return res.status(429).json({
        message: `Please wait ${Math.ceil((THREAD_COOLDOWN - (now - lastCreation)) / 1000)} seconds before creating another thread`
      });
    }

    // Parse and validate the request body
    const parseResult = insertThreadSchema.safeParse(req.body);
    if (!parseResult.success) {
      // Check if any validation error is related to content moderation
      const errorMessages = parseResult.error.errors.map(e => e.message);
      const hasContentFilterError = errorMessages.some(msg => 
        msg.includes('Content Moderation') || msg.includes('community guidelines')
      );
      
      if (hasContentFilterError) {
        return res.status(422).json({ 
          message: "Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again.",
          type: "content_filter"
        });
      }
      
      return res.status(400).json({ 
        message: "Validation failed", 
        errors: parseResult.error.errors 
      });
    }
    
    const validatedData = parseResult.data;

    try {
      // We don't need to check for offensive language here anymore as it's handled by 
      // the contentFilter transform in the Zod schema
      
      const thread = await storage.createThread({
        ...validatedData,
        authorId: req.user.id,
      });

      // Update rate limit
      THREAD_RATE_LIMIT.set(req.user.id, now);

      res.status(201).json(thread);
    } catch (error) {
      console.error('Error creating thread:', error);
      
      // Special handling for content filter errors
      if (error instanceof Error) {
        if (error.name === 'ContentFilterError') {
          return res.status(400).json({ message: error.message });
        }
        
        if (error.message.includes('Content Moderation') || 
            error.message.includes('inappropriate language') || 
            error.message.includes('community guidelines')) {
          return res.status(400).json({ 
            message: "⚠️ Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again."
          });
        }
      }
      
      res.status(500).json({ message: "Failed to create thread" });
    }
  });

  app.delete("/api/threads/:threadId", async (req, res) => {
    console.log("=== DELETE THREAD ROUTE START ===");
    console.log("User:", req.user ? `ID: ${req.user.id}, Admin: ${req.user.isAdmin}` : "No user");
    console.log("Thread ID:", req.params.threadId);
    
    if (!req.user) {
      console.log("No user authenticated, returning 401");
      return res.sendStatus(401);
    }

    const threadId = parseInt(req.params.threadId);
    console.log("Parsed thread ID:", threadId);
    console.log("User is admin:", req.user.isAdmin);
    
    // For debugging - let's check if the thread exists first
    try {
      const existsCheck = await db
        .select({ id: threads.id, isDeleted: threads.isDeleted, authorId: threads.authorId })
        .from(threads)
        .where(eq(threads.id, threadId));
      
      console.log("Thread exists check:", existsCheck);
      
      if (existsCheck.length === 0) {
        console.log("Thread does not exist at all");
        return res.status(404).json({ message: "Thread not found" });
      }
      
      const thread = existsCheck[0];
      console.log("Thread details:", thread);
      
      if (thread.isDeleted) {
        console.log("Thread already deleted");
        return res.status(404).json({ message: "Thread not found" });
      }
      
      // Check permissions
      if (req.user.isAdmin || thread.authorId === req.user.id) {
        console.log("User has permission to delete thread");
        
        const result = await db
          .update(threads)
          .set({ isDeleted: true })
          .where(eq(threads.id, threadId))
          .returning({ id: threads.id });
        
        console.log("Delete result:", result);
        
        if (result.length > 0) {
          console.log("Thread successfully deleted");
          return res.sendStatus(200);
        } else {
          console.log("Delete operation failed");
          return res.status(500).json({ message: "Delete operation failed" });
        }
      } else {
        console.log("User does not have permission to delete thread");
        return res.status(403).json({ message: "Unauthorized to delete this thread" });
      }
      
    } catch (error) {
      console.error("Error in delete thread route:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/api/threads/:threadId/replies", async (req, res) => {
    const replies = await storage.getRepliesByThreadId(parseInt(req.params.threadId));
    res.json(replies);
  });

  app.post("/api/threads/:threadId/replies", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    // Parse and validate the request body
    const parseResult = insertReplySchema.safeParse(req.body);
    if (!parseResult.success) {
      // Check if any validation error is related to content moderation
      const errorMessages = parseResult.error.errors.map(e => e.message);
      const hasContentFilterError = errorMessages.some(msg => 
        msg.includes('Content Moderation') || msg.includes('community guidelines')
      );
      
      if (hasContentFilterError) {
        return res.status(422).json({ 
          message: "Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again.",
          type: "content_filter"
        });
      }
      
      return res.status(400).json({ 
        message: "Validation failed", 
        errors: parseResult.error.errors 
      });
    }
    
    const validatedData = parseResult.data;

    try {
      // We don't need to check for offensive language here anymore as it's handled by 
      // the contentFilter transform in the Zod schema
      
      const threadId = parseInt(req.params.threadId);
      const reply = await storage.createReply({
        ...validatedData,
        threadId,
        authorId: req.user.id,
      });

      // Get thread info to notify the author
      const thread = await storage.getThreadById(threadId);
      if (thread && thread.authorId !== req.user.id) {
        await NotificationService.createThreadReplyNotification(
          req.user.id,
          threadId,
          thread.authorId,
          validatedData.content
        );
      }

      res.status(201).json(reply);
    } catch (error) {
      console.error('Error creating reply:', error);
      
      // Special handling for content filter errors
      if (error instanceof Error) {
        if (error.name === 'ContentFilterError') {
          return res.status(400).json({ message: error.message });
        }
        
        if (error.message.includes('Content Moderation') || 
            error.message.includes('inappropriate language') || 
            error.message.includes('community guidelines')) {
          return res.status(400).json({ 
            message: "⚠️ Content Moderation Alert: Your submission contains language that violates our community guidelines. Please revise your content and try again."
          });
        }
      }
      
      res.status(500).json({ message: "Failed to post reply" });
    }
  });

  app.delete("/api/threads/:threadId/replies/:replyId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const replyId = parseInt(req.params.replyId);
    const success = await storage.deleteReply(replyId, req.user.id);

    if (!success) {
      return res.status(404).json({ message: "Reply not found or unauthorized" });
    }

    res.sendStatus(200);
  });

  app.get("/api/users", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    const users = await storage.getUsers();
    res.json(users.filter(u => u.id !== req.user!.id));
  });

  app.post("/api/messages", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    try {
      // Use the insertMessageSchema for E2EE messages
      const parseResult = insertMessageSchema.safeParse({
        ...req.body,
        senderId: req.user.id // Add senderId from the authenticated user
      });
      
      if (!parseResult.success) {
        return res.status(400).json({ 
          message: "Invalid message format",
          errors: parseResult.error.errors 
        });
      }

      const { recipientId, content, nonce, isEncrypted } = parseResult.data;

      // Check for offensive language only in non-encrypted messages
      if (!isEncrypted && containsOffensiveLanguage(content)) {
        return res.status(400).json({
          message: "Your message contains inappropriate language. Please revise and try again."
        });
      }

      // Verify recipient exists
      const recipient = await storage.getUser(recipientId);
      if (!recipient) {
        return res.status(404).json({ message: "Recipient not found" });
      }

      const message = await storage.createMessage({
        senderId: req.user.id,
        recipientId,
        content: content.trim(),
        nonce,
        isEncrypted
      });

      // Create notification for new message
      await NotificationService.createMessageNotification(
        req.user.id,
        recipientId,
        content.trim()
      );

      res.status(201).json(message);
    } catch (error) {
      console.error('Error creating message:', error);
      res.status(500).json({ message: "Error creating message" });
    }
  });

  app.get("/api/messages/:userId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    try {
      const userId = parseInt(req.params.userId);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      // Verify the requested user exists
      const otherUser = await storage.getUser(userId);
      if (!otherUser) {
        return res.status(404).json({ message: "User not found" });
      }

      const messages = await storage.getMessagesBetweenUsers(
        req.user.id,
        userId
      );
      res.json(messages);
    } catch (error) {
      console.error('Error fetching messages:', error);
      res.status(500).json({ message: "Error fetching messages" });
    }
  });

  app.get("/api/messages", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    try {
      const allMessages = await storage.getAllMessages(req.user.id);
      res.json(allMessages);
    } catch (error) {
      console.error('Error fetching all messages:', error);
      res.status(500).json({ message: "Error fetching messages" });
    }
  });

  // AI Sessions management
  app.get("/api/ai/sessions", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    
    try {
      const sessions = await storage.getAIChatSessionsByUserId(req.user.id);
      res.json(sessions);
    } catch (error) {
      console.error('Error fetching AI chat sessions:', error);
      res.status(500).json({ error: "Failed to fetch AI chat sessions" });
    }
  });
  
  app.post("/api/ai/sessions", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    
    const parseResult = insertAIChatSessionSchema.safeParse(req.body);
    if (!parseResult.success) {
      return res.status(400).json({ error: parseResult.error.message });
    }
    
    try {
      const session = await storage.createAIChatSession({
        ...parseResult.data,
        userId: req.user.id
      });
      res.status(201).json(session);
    } catch (error) {
      console.error('Error creating AI chat session:', error);
      res.status(500).json({ error: "Failed to create AI chat session" });
    }
  });
  
  app.get("/api/ai/sessions/:sessionId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    
    const sessionId = parseInt(req.params.sessionId);
    if (isNaN(sessionId)) {
      return res.status(400).json({ error: "Invalid session ID" });
    }
    
    try {
      const session = await storage.getAIChatSessionById(sessionId);
      if (!session) {
        return res.status(404).json({ error: "Session not found" });
      }
      
      // Only the owner can access their sessions
      if (session.userId !== req.user.id) {
        return res.status(403).json({ error: "Unauthorized access to session" });
      }
      
      res.json(session);
    } catch (error) {
      console.error('Error fetching AI chat session:', error);
      res.status(500).json({ error: "Failed to fetch AI chat session" });
    }
  });
  
  app.patch("/api/ai/sessions/:sessionId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    
    const sessionId = parseInt(req.params.sessionId);
    if (isNaN(sessionId)) {
      return res.status(400).json({ error: "Invalid session ID" });
    }
    
    if (!req.body.title || typeof req.body.title !== 'string') {
      return res.status(400).json({ error: "Title is required" });
    }
    
    try {
      // First verify the session exists and belongs to the user
      const session = await storage.getAIChatSessionById(sessionId);
      if (!session) {
        return res.status(404).json({ error: "Session not found" });
      }
      
      if (session.userId !== req.user.id) {
        return res.status(403).json({ error: "Unauthorized to update this session" });
      }
      
      await storage.updateAIChatSessionTitle(sessionId, req.body.title);
      
      res.json({
        id: sessionId,
        title: req.body.title,
        userId: req.user.id,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating AI chat session:', error);
      res.status(500).json({ error: "Failed to update AI chat session" });
    }
  });
  
  app.delete("/api/ai/sessions/:sessionId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    
    const sessionId = parseInt(req.params.sessionId);
    if (isNaN(sessionId)) {
      aiLogger.warn('Invalid session ID format', {
        providedId: req.params.sessionId,
        userId: req.user.id
      }, 'invalid-session-id');
      return res.status(400).json({ error: "Invalid session ID" });
    }
    
    try {
      // First verify the session exists and belongs to the user
      const session = await storage.getAIChatSessionById(sessionId);
      
      // If session doesn't exist, consider it already deleted (idempotent delete)
      if (!session) {
        aiLogger.info('Attempted to delete non-existent session', {
          sessionId,
          userId: req.user.id
        }, 'session-not-found');
        // Return 204 (success) since the end result is the same - session doesn't exist
        return res.status(204).end();
      }
      
      if (session.userId !== req.user.id) {
        aiLogger.warn('Unauthorized session deletion attempt', {
          sessionId,
          userId: req.user.id,
          sessionOwnerId: session.userId
        }, 'unauthorized-deletion');
        return res.status(403).json({ error: "Unauthorized to delete this session" });
      }
      
      aiLogger.info('Deleting AI chat session', {
        sessionId,
        userId: req.user.id
      }, 'session-deletion');
      
      await storage.deleteAIChatSession(sessionId);
      
      aiLogger.debug('AI chat session deleted successfully', {
        sessionId,
        userId: req.user.id
      }, 'session-deleted');
      
      res.status(204).end();
    } catch (error) {
      aiLogger.error('Error deleting AI chat session', error, 'session-deletion-error');
      res.status(500).json({ error: "Failed to delete AI chat session" });
    }
  });
  
  app.get("/api/ai/sessions/:sessionId/messages", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    
    const sessionId = parseInt(req.params.sessionId);
    if (isNaN(sessionId)) {
      aiLogger.warn('Invalid session ID format', {
        providedId: req.params.sessionId,
        userId: req.user.id
      }, 'invalid-session-id');
      return res.status(400).json({ error: "Invalid session ID" });
    }
    
    try {
      // First verify the session exists and belongs to the user
      const session = await storage.getAIChatSessionById(sessionId);
      
      // If session doesn't exist, log and return 404
      if (!session) {
        aiLogger.info('Attempted to access messages for non-existent session', {
          sessionId,
          userId: req.user.id
        }, 'session-not-found');
        return res.status(404).json({ error: "Session not found" });
      }
      
      // Check authorization
      if (session.userId !== req.user.id) {
        aiLogger.warn('Unauthorized session access attempt', {
          sessionId,
          userId: req.user.id,
          sessionOwnerId: session.userId
        }, 'unauthorized-access');
        return res.status(403).json({ error: "Unauthorized to access messages for this session" });
      }
      
      // Fetch messages with proper error handling
      try {
        const messages = await storage.getAIChatsBySessionId(sessionId);
        
        aiLogger.debug('Session messages retrieved', {
          sessionId,
          userId: req.user.id,
          messageCount: messages.length
        }, 'session-messages-retrieved');
        
        res.json(messages);
      } catch (messageError) {
        // Specific error handling for database issues
        aiLogger.error('Database error fetching session messages', messageError, 'db-error');
        res.status(500).json({ error: "Failed to fetch session messages due to database error" });
      }
    } catch (error) {
      aiLogger.error('Error fetching session messages', error, 'session-messages-error');
      res.status(500).json({ error: "Failed to fetch session messages" });
    }
  });

  app.post("/api/ai/chat", async (req, res) => {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    
    if (!req.user) {
      aiLogger.warn('Unauthorized chat request attempt', { requestId, ip: req.ip }, 'auth');
      return res.sendStatus(401);
    }

    const { messages, attachments, stream, sessionId, isDeepThinking = false, provider = 'deepseek', model } = req.body;
    
    // Enhanced request logging with user context
    aiLogger.log('Received chat request', { 
      requestId,
      userId: req.user.id,
      username: req.user.username,
      messagesCount: messages?.length,
      hasAttachments: !!attachments,
      attachmentsCount: attachments?.length || 0,
      stream: !!stream,
      sessionId: sessionId || null,
      isDeepThinking: !!isDeepThinking,
      ip: req.ip
    }, 'request');

    // Validate messages array exists
    if (!messages || !Array.isArray(messages)) {
      aiLogger.error('Invalid messages array', { 
        requestId, 
        userId: req.user.id,
        messages: messages || 'undefined',
        type: typeof messages
      }, 'validation');
      
      return res.status(400).json({
        error: 'Invalid messages array. Expected an array of messages.'
      });
    }

    // Validate message format
    const isValidMessage = (msg: any): msg is Message => (
      msg &&
      typeof msg === 'object' &&
      ['system', 'user', 'assistant'].includes(msg.role) &&
      typeof msg.content === 'string'
    );

    // Check each message for validity
    const invalidMessages = messages.filter(msg => !isValidMessage(msg));
    if (invalidMessages.length > 0) {
      aiLogger.error('Invalid message format', { 
        requestId,
        userId: req.user.id,
        invalidMessages,
        validationTime: Date.now() - startTime
      }, 'validation');
      
      return res.status(400).json({
        error: 'Invalid message format. Each message must have a valid role and content.'
      });
    }
    
    // Check if there's an active session
    if (sessionId) {
      try {
        const session = await storage.getAIChatSessionById(parseInt(sessionId));
        if (!session) {
          aiLogger.warn(`Session not found: ${sessionId}`, { 
            requestId, 
            userId: req.user.id
          }, 'session');
          
          return res.status(404).json({
            error: 'Session not found. Please create a new session.'
          });
        }
        
        if (session.userId !== req.user.id) {
          aiLogger.warn(`Unauthorized access attempt to session ${sessionId}`, {
            requestId,
            userId: req.user.id,
            sessionOwnerId: session.userId
          }, 'security');
          
          return res.status(403).json({
            error: 'You do not have permission to access this session.'
          });
        }
        
        aiLogger.debug(`Session validated successfully`, {
          requestId,
          sessionId,
          userId: req.user.id,
          validationTime: Date.now() - startTime
        }, 'session');
      } catch (error) {
        aiLogger.error(`Error validating session`, {
          requestId,
          sessionId,
          userId: req.user.id,
          error: error instanceof Error ? error.message : String(error)
        }, 'session');
        
        return res.status(500).json({
          error: 'Failed to validate session. Please try again.'
        });
      }
    }

    try {
      // Performance tracking
      const processingStartTime = Date.now();
      
      // Filter out any system messages from the input to prevent tampering
      const formattedMessages = messages
        .filter(msg => msg.role !== 'system')
        .map(({ role, content }) => ({
          role,
          content: content.trim()
        })) as Message[];

      // Get last user message for logging and storage
      const lastUserMessage = formattedMessages
        .filter(msg => msg.role === 'user')
        .pop();
        
      const messageContent = lastUserMessage?.content || '';
      
      aiLogger.debug('Formatted messages for AI', { 
        requestId,
        messageCount: formattedMessages.length,
        lastUserMessagePreview: messageContent.substring(0, 50) + (messageContent.length > 50 ? '...' : ''),
        processingTime: Date.now() - processingStartTime,
        isDeepThinking
      }, 'preprocessing');

      if (stream === true) {
        aiLogger.debug('Starting streaming response', { 
          requestId, 
          userId: req.user?.id,
          sessionId,
          isDeepThinking,
          messageCount: formattedMessages.length
        }, 'stream-start');
        
        // Track all state for metrics and error handling
        let fullResponse = '';
        let chunkCount = 0;
        let firstChunkTime = 0;
        const streamStartTime = Date.now();
        let streamEnded = false;
        let clientDisconnected = false;
        let errorOccurred = false;
        
        // Set up client disconnect detection
        req.on('close', () => {
          const disconnectTime = Date.now();
          clientDisconnected = true;
          
          // Comprehensive metrics for disconnections
          aiLogger.info('Client disconnected', {
            requestId,
            userId: req.user?.id,
            sessionId: sessionId || null,
            disconnectTime: disconnectTime - streamStartTime,
            chunkCount,
            responseLength: fullResponse.length,
            firstChunkTime,
            streamProgress: Math.round((fullResponse.length / 1024) * 100) / 100 + ' KB',
            streamDuration: disconnectTime - streamStartTime,
            isDeepThinking
          }, 'client-disconnect');
          
          // We'll destroy the stream in a try-catch below
        });
        
        try {
          // Call the AI service with all context
          const aiResponseStream = await getChatResponse(
            formattedMessages, 
            attachments, 
            true,
            req.user?.id,
            sessionId ? parseInt(sessionId) : null,
            isDeepThinking,
            provider,
            model
          );

          if (!(aiResponseStream instanceof Readable)) {
            aiLogger.error('Invalid stream response', { 
              requestId,
              type: typeof aiResponseStream 
            }, 'stream-error');
            throw new Error('Expected a readable stream from AI response');
          }

          // Set headers for SSE
          res.setHeader('Content-Type', 'text/event-stream');
          res.setHeader('Cache-Control', 'no-cache');
          res.setHeader('Connection', 'keep-alive');

          // Handle client disconnect - must be done after we have the stream
          req.on('close', () => {
            if (!streamEnded && aiResponseStream && !aiResponseStream.destroyed) {
              aiLogger.log('Destroying stream due to client disconnect', {
                requestId,
                chunkCount,
                streamDuration: Date.now() - streamStartTime
              }, 'stream-destroy');
              
              try {
                aiResponseStream.destroy();
              } catch (destroyError) {
                aiLogger.error('Error destroying stream', destroyError, 'stream-destroy-error');
              }
            }
          });

          aiResponseStream.on('data', (chunk: Buffer) => {
            try {
              // Don't process further if client disconnected
              if (clientDisconnected) return;
              
              // Track time to first chunk
              if (chunkCount === 0) {
                firstChunkTime = Date.now() - streamStartTime;
                aiLogger.debug(`First chunk received after ${firstChunkTime}ms`, {
                  requestId,
                  ttfb: firstChunkTime,
                  contentLength: chunk.length,
                  isDeepThinking
                }, 'stream-first-chunk');
              }
              
              chunkCount++;
              const chunkStr = chunk.toString();
              fullResponse += chunkStr;

              // Check if the chunk is a complete SSE data
              if (chunkStr.includes('data: ')) {
                res.write(chunkStr);
              } else {
                // Format as SSE data if it's not already
                const jsonData = {
                  choices: [{
                    delta: {
                      content: chunkStr
                    }
                  }]
                };
                res.write(`data: ${JSON.stringify(jsonData)}\n\n`);
              }
              
              // Periodically log progress for long streams
              if (chunkCount % 50 === 0) {
                const currentTime = Date.now();
                const elapsedTime = currentTime - streamStartTime;
                const chunkRate = chunkCount / (elapsedTime / 1000);
                
                aiLogger.debug(`Stream progress: ${chunkCount} chunks received`, {
                  requestId,
                  chunkCount,
                  contentLength: fullResponse.length,
                  elapsedTime,
                  chunkRate: Math.round(chunkRate * 100) / 100 + ' chunks/sec',
                  responseSize: Math.round((fullResponse.length / 1024) * 100) / 100 + ' KB',
                  isDeepThinking
                }, 'stream-progress');
              }
            } catch (error) {
              aiLogger.error('Error processing chunk', error, 'stream-chunk-error');
            }
          });

          aiResponseStream.on('end', async () => {
            streamEnded = true;
            const streamDuration = Date.now() - streamStartTime;
            
            // Don't process further if there was an error or client disconnected
            if (errorOccurred || clientDisconnected) {
              aiLogger.debug('Stream ended but not processing due to error or disconnect', {
                requestId,
                errorOccurred,
                clientDisconnected
              }, 'stream-end-skip');
              return;
            }
            
            aiLogger.log('Stream ended successfully', { 
              requestId,
              userId: req.user?.id,
              sessionId: sessionId || null,
              chunkCount,
              streamDuration,
              responseLength: fullResponse.length,
              responseSizeKB: Math.round((fullResponse.length / 1024) * 100) / 100,
              firstChunkTime,
              averageChunkTime: chunkCount > 0 ? streamDuration / chunkCount : 0,
              chunkRate: chunkCount > 0 ? Math.round((chunkCount / (streamDuration / 1000)) * 100) / 100 : 0,
              isDeepThinking
            }, 'stream-end');
            
            try {
              const user = req.user as User;
              
              if (lastUserMessage) {
                aiLogger.debug('Persisting chat history to database', {
                  requestId,
                  userId: user.id,
                  sessionId: sessionId || null,
                  isDeepThinking,
                  messageLength: lastUserMessage.content.length,
                  responseLength: fullResponse.length
                }, 'persistence');
                
                // Store in AI chat database with session if provided
                // For streaming responses, we estimate token usage since it's not provided by the stream
                const estimatedPromptTokens = Math.ceil(lastUserMessage.content.length / 4);
                const estimatedCompletionTokens = Math.ceil(fullResponse.length / 4);
                const estimatedTotalTokens = estimatedPromptTokens + estimatedCompletionTokens;
                
                await storage.createAIChat({
                  userId: user.id,
                  sessionId: sessionId ? parseInt(sessionId) : undefined,
                  message: lastUserMessage.content,
                  response: fullResponse,
                  isDeepThinking,
                  promptTokens: estimatedPromptTokens,
                  completionTokens: estimatedCompletionTokens,
                  totalTokens: estimatedTotalTokens
                });
                
                aiLogger.debug('Chat history persisted successfully', {
                  requestId,
                  processTime: Date.now() - processingStartTime
                }, 'persistence-complete');
              } else {
                aiLogger.warn('No user message found to persist', {
                  requestId,
                  userId: user.id,
                  formattedMessages
                }, 'persistence-skip');
              }
              
              res.write('data: [DONE]\n\n');
              res.end();
              
              // Log total request time
              aiLogger.performance('ai-chat-stream', Date.now() - startTime, {
                requestId,
                userId: req.user?.id ?? 0, // Add null check for req.user
                sessionId: sessionId || null,
                streamDuration,
                responseLength: fullResponse.length,
                ttfb: firstChunkTime,
                processingTime: processingStartTime - startTime,
                isDeepThinking,
                success: true
              });
            } catch (error) {
              aiLogger.error('Error persisting chat history', error, 'persistence-error');
              res.end();
            }
          });

          aiResponseStream.on('error', (error) => {
            errorOccurred = true;
            aiLogger.error('Stream error', {
              error,
              requestId,
              userId: req.user?.id,
              sessionId,
              streamDuration: Date.now() - streamStartTime,
              chunkCount,
              responseLength: fullResponse.length,
              isDeepThinking
            }, 'stream-error');
            
            // Send an error message to the client if possible
            try {
              if (!res.headersSent) {
                res.status(500).json({ error: 'Stream error occurred' });
              } else {
                // If headers are already sent, try to send an SSE error message
                const errorMessage = {
                  error: true,
                  message: 'An error occurred during streaming'
                };
                res.write(`data: ${JSON.stringify(errorMessage)}\n\n`);
                res.write('data: [DONE]\n\n');
                res.end();
              }
            } catch (responseError) {
              aiLogger.error('Error sending error response', responseError, 'response-error');
              try { res.end(); } catch (e) { /* ignore */ }
            }
          });
          
        } catch (error) {
          errorOccurred = true;
          aiLogger.error('Error setting up stream', {
            error, 
            requestId,
            userId: req.user?.id
          }, 'stream-setup-error');
          
          // Try to send an error response
          if (!res.headersSent) {
            // Safely extract error message since error is unknown type
            const errorMessage = error instanceof Error 
              ? error.message 
              : 'Unknown error occurred';
              
            res.status(500).json({ 
              error: 'Error initializing AI stream',
              message: errorMessage 
            });
          } else {
            try { res.end(); } catch (e) { /* ignore */ }
          }
        }
      } else {
        aiLogger.log('Processing non-streaming response');
        const responseData = await getChatResponse(
          formattedMessages, 
          attachments, 
          false,
          req.user?.id,
          sessionId ? parseInt(sessionId) : null,
          isDeepThinking,
          provider,
          model
        ) as { content: string; tokenUsage: { promptTokens: number; completionTokens: number; totalTokens: number; } };

        // Get the last user message
        const lastUserMessage = formattedMessages
          .filter(msg => msg.role === 'user')
          .pop();
          
        if (lastUserMessage) {
          // Store in AI chat database with session if provided
          const user = req.user as User;
          await storage.createAIChat({
            userId: user.id,
            sessionId: sessionId || undefined,
            message: lastUserMessage.content,
            response: responseData.content,
            isDeepThinking,
            promptTokens: responseData.tokenUsage.promptTokens,
            completionTokens: responseData.tokenUsage.completionTokens,
            totalTokens: responseData.tokenUsage.totalTokens
          });
        }

        res.json({
          choices: [{
            message: {
              content: responseData.content
            }
          }],
          usage: responseData.tokenUsage
        });
      }
    } catch (error) {
      aiLogger.error('AI chat error', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to get AI response';
      res.status(500).json({ error: errorMessage });
    }
  });

  app.get("/api/chat/messages/:roomId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);
    try {
      const messages = await storage.getChatRoomMessages(req.params.roomId);
      res.json(messages);
    } catch (error) {
      console.error('Error fetching chat room messages:', error);
      res.status(500).json({ message: "Error fetching messages" });
    }
  });

  app.post("/api/chat/messages", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const parseResult = insertChatRoomMessageSchema.safeParse(req.body);
    if (!parseResult.success) {
      return res.status(400).json(parseResult.error);
    }

    try {
      const { content } = parseResult.data;
      
      // Check for offensive language in chat messages
      if (typeof content === 'string' && containsOffensiveLanguage(content)) {
        return res.status(400).json({
          message: "Your message contains inappropriate language. Please revise and try again."
        });
      }
      
      const message = await storage.createChatRoomMessage({
        ...parseResult.data,
        userId: req.user.id
      });
      res.status(201).json(message);
    } catch (error) {
      console.error('Error creating chat room message:', error);
      res.status(500).json({ message: "Error creating message" });
    }
  });

  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    res.json(req.user);
  });

  // Add avatar routes after the existing user routes
  app.get("/api/user/avatar", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    try {
      const user = await storage.getUser(req.user!.id);
      if (!user) return res.status(404).json({ message: "User not found" });
      res.json({ avatar: user.avatar || "" });
    } catch (error) {
      console.error('Error fetching avatar:', error);
      res.status(500).json({ message: "Error fetching avatar" });
    }
  });

  // Legacy text avatar update
  app.post("/api/user/avatar", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    try {
      const { avatar } = req.body;
      if (typeof avatar !== 'string') {
        return res.status(400).json({ message: "Invalid avatar format" });
      }
      await storage.updateUserAvatar(req.user!.id, avatar);
      res.json({ message: "Avatar updated successfully" });
    } catch (error) {
      console.error('Error updating avatar:', error);
      res.status(500).json({ message: "Error updating avatar" });
    }
  });

  // New image avatar upload
  app.post("/api/user/avatar/upload", avatarUpload.single('avatar'), async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      const userId = req.user!.id;
      const filename = `avatar_${userId}_${Date.now()}.webp`;
      const avatarPath = path.join('static', 'avatars', filename);
      
      // Ensure static/avatars directory exists
      const dir = path.dirname(avatarPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Process image: resize to 128x128 and convert to WebP
      await sharp(req.file.buffer)
        .resize(128, 128, { 
          fit: 'cover',
          position: 'center'
        })
        .webp({ quality: 85 })
        .toFile(avatarPath);

      // Update user avatar in database with file path
      const avatarUrl = `/static/avatars/${filename}`;
      await storage.updateUserAvatar(userId, avatarUrl);

      res.json({ 
        message: "Avatar uploaded successfully",
        avatarUrl 
      });
    } catch (error) {
      console.error('Error uploading avatar:', error);
      res.status(500).json({ message: "Error uploading avatar" });
    }
  });
  
  // Add routes for public key management for E2EE
  app.post("/api/user/public-key", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    try {
      const { publicKey } = req.body;
      if (typeof publicKey !== 'string') {
        return res.status(400).json({ message: "Invalid public key format" });
      }
      await storage.updateUserPublicKey(req.user!.id, publicKey);
      res.json({ message: "Public key updated successfully" });
    } catch (error) {
      console.error('Error updating public key:', error);
      res.status(500).json({ message: "Error updating public key" });
    }
  });
  
  app.get("/api/users/:userId/public-key", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    try {
      const userId = parseInt(req.params.userId);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }
      
      const publicKey = await storage.getUserPublicKey(userId);
      if (!publicKey) {
        return res.status(404).json({ message: "Public key not found" });
      }
      
      res.json({ publicKey });
    } catch (error) {
      console.error('Error fetching public key:', error);
      res.status(500).json({ message: "Error fetching public key" });
    }
  });

  // User statistics endpoint
  app.get("/api/user/stats", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const stats = await storage.getUserStats(req.user!.id);
      res.json(stats);
    } catch (error) {
      console.error('Error fetching user stats:', error);
      res.status(500).json({ error: "Failed to fetch user statistics" });
    }
  });

  // User activity timeline endpoint
  app.get("/api/user/activity", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const activity = await storage.getUserActivity(req.user!.id);
      res.json(activity);
    } catch (error) {
      console.error('Error fetching user activity:', error);
      res.status(500).json({ error: "Failed to fetch user activity" });
    }
  });

  // User security information endpoint
  app.get("/api/user/security", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const securityInfo = await storage.getUserSecurityInfo(req.user!.id);
      res.json(securityInfo);
    } catch (error) {
      console.error('Error fetching security info:', error);
      res.status(500).json({ error: "Failed to fetch security information" });
    }
  });


  // Add these routes inside the registerRoutes function, near other admin routes
  app.get("/api/admin/users", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    try {
      const users = await storage.getUsers();
      res.json(users);
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ message: "Error fetching users" });
    }
  });

  app.get("/api/admin/users/:userId", async (req, res) => { 
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    try {
      const user = await storage.getUserWithDetails(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error) {
      console.error('Error fetching user details:', error);
      res.status(500).json({ message: "Error fetching user details" });
    }
  });

  app.get("/api/admin/users/:userId/timeline", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    try {
      const timeline = await storage.getUserTimeline(userId);
      res.json(timeline);
    } catch (error) {
      console.error('Error fetching user timeline:', error);
      res.status(500).json({ message: "Error fetching timeline" });
    }
  });

  app.post("/api/admin/users/:userId/suspend", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const userId = parseInt(req.params.userId);
    const { until, reason, adminId } = req.body;

    if (!until || !reason || !adminId) {
      return res.status(400).json({ message: "Suspension duration, reason and adminId are required" });
    }

    try {
      await storage.suspendUser(userId, new Date(until), reason, adminId);
      res.json({ message: "User suspended successfully" });
    } catch (error) {
      console.error('Error suspending user:', error);
      res.status(500).json({ message: "Error suspending user" });
    }
  });

  app.post("/api/admin/users/:userId/ban", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const userId = parseInt(req.params.userId);
    const { reason, adminId } = req.body;

    if (!reason || !adminId) {
      return res.status(400).json({ message: "Ban reason and adminId are required" });
    }

    try {
      await storage.banUser(userId, reason, adminId);
      res.json({ message: "User banned successfully" });
    } catch (error) {
      console.error('Error banning user:', error);
      res.status(500).json({ message: "Error banning user" });
    }
  });

  app.post("/api/admin/users/:userId/unsuspend", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const userId = parseInt(req.params.userId);
    const { adminId } = req.body;

    if (!adminId) {
      return res.status(400).json({ message: "adminId is required" });
    }

    try {
      await storage.unsuspendUser(userId, adminId);
      res.json({ message: "User unsuspended successfully" });
    } catch (error) {
      console.error('Error unsuspending user:', error);
      res.status(500).json({ message: "Error unsuspending user" });
    }
  });

  app.post("/api/admin/users/:userId/unban", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const userId = parseInt(req.params.userId);
    const { adminId } = req.body;

    if (!adminId) {
      return res.status(400).json({ message: "adminId is required" });
    }

    try {
      await storage.unbanUser(userId, adminId);
      res.json({ message: "User unbanned successfully" });
    } catch (error) {
      console.error('Error unbanning user:', error);
      res.status(500).json({ message: "Error unbanning user" });
    }
  });

  app.post("/api/admin/users/toggle-admin", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const { userId, isAdmin, adminId } = req.body;
    if (typeof userId !== 'number' || typeof isAdmin !== 'boolean' || typeof adminId !== 'number') {
      return res.status(400).json({ message: "Invalid request body" });
    }

    try {
      // Don't allow changing own admin status
      if (userId === req.user.id) {
        return res.status(400).json({ message: "Cannot modify your own admin status" });
      }

      await storage.updateUserAdminStatus(userId, isAdmin, adminId);

      res.json({ message: "User admin status updated successfully" });
    } catch (error) {
      console.error('Error updating user admin status:', error);
      res.status(500).json({ message: "Error updating user admin status" });
    }
  });

  app.get("/api/system/db-metrics", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    const metrics = (storage as DatabaseStorage).getDbMetrics();
    res.json(metrics);
  });

  app.post("/api/system/db-metrics/clear", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    (storage as DatabaseStorage).clearDbMetrics();
    res.json({ message: "Metrics cleared successfully" });
  });

  app.post("/api/user/change-password", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    const { currentPassword, newPassword } = req.body;
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: "Missing required fields" });
    }

    try {
      const user = await storage.getUser(req.user!.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const isValidPassword = await comparePasswords(currentPassword, user.password);
      if (!isValidPassword) {
        return res.status(400).json({ message: "Current password is incorrect" });
      }

      const hashedPassword = await hashPassword(newPassword);
      await storage.updateUserPassword(user.id, hashedPassword);

      res.json({ message: "Password updated successfully" });
    } catch (error) {
      console.error('Error changing password:', error);
      res.status(500).json({ message: "Error changing password" });
    }
  });

  app.post("/api/user/delete", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const user = await storage.getUser(req.user!.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const isValidPassword = await comparePasswords(req.body.password, user.password);
      if (!isValidPassword) {
        return res.status(400).json({ message: "Password is incorrect" });
      }

      await storage.deleteUser(user.id);

      // Set content type to application/json
      res.setHeader('Content-Type', 'application/json');

      req.logout((err) => {
        if (err) {
          console.error('Error during logout:', err);
          return res.status(500).json({ message: "Error during account deletion" });
        }
        res.json({ message: "Account deleted successfully" });
      });
    } catch (error) {
      console.error('Error deleting account:', error);
      res.status(500).json({ message: "Error deleting account" });
    }
  });

  // Update login route to check for suspended/banned status
  app.post("/api/login", (req, res, next) => {
    passport.authenticate("local", async (err: any, user: any) => {
      if (err) return next(err);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      try {
        // Check if user is banned
        if (user.bannedAt) {
          return res.status(403).json({
            message: "Account banned",
            reason: user.banReason
          });
        }

        // Check if user is suspended
        if (user.suspendedUntil && new Date(user.suspendedUntil) > new Date()) {
          return res.status(403).json({
            message: "Account suspended",
            until: user.suspendedUntil,
            reason: user.banReason
          });
        }

        // Update last login information
        await storage.updateUserLoginInfo(user.id, req.ip || 'unknown');

        req.logIn(user, (err) => {
          if (err) return next(err);
          res.json(user);
        });
      } catch (error) {
        console.error('Error updating login info:', error);
        next(error);
      }
    })(req, res, next);
  });

  // Admin logs endpoint
  // Test Runner endpoints
  app.get("/api/admin/tests", async (req, res) => {
    if (!req.user || !req.user.isAdmin) return res.sendStatus(403);
    
    // Return a list of available tests
    const availableTests = {
      standard: [
        "login", 
        "logout", 
        "registerUser", 
        "forumThread", 
        "forumReply", 
        "threadVoting", 
        "directMessaging", 
        "publicChat", 
        "adminLogin", 
        "userManagement", 
        "responsiveLayout", 
        "pageLoadPerformance"
      ],
      ai: [
        "aiChat",
        "aiChatAttachments",
        "aiResponseStreaming",
        "aiSessionManagement",
        "aiChatDeep"
      ],
      encryption: [
        "encryptedMessaging",
        "e2eeKeyExchange",
        "messageEncryption",
        "messageDecryption",
        "keySecurity"
      ],
      specialized: [
        "aiChatDeep",
        "encryptedMessaging"
      ]
    };
    
    res.json(availableTests);
  });
  
  app.post("/api/admin/tests/run", async (req, res) => {
    if (!req.user || !req.user.isAdmin) return res.sendStatus(403);
    
    const { testType, tests, config } = req.body;
    
    if (!testType || !["standard", "ai", "encryption", "specialized", "all"].includes(testType)) {
      return res.status(400).json({ message: "Invalid test type" });
    }
    
    try {
      // Start a child process to run the tests
      let args = ['testScripts/run.js', testType];
      
      // Add specific tests if provided
      if (tests && tests.length > 0) {
        args.push('--tests=' + tests.join(','));
      }
      
      // Add config overrides if provided
      if (config) {
        if (config.headless !== undefined) args.push('--headless=' + config.headless);
        if (config.baseUrl) args.push('--url=' + config.baseUrl);
        if (config.retries !== undefined) args.push('--retries=' + config.retries);
      }
      
      console.log(`Starting test process with args: ${args.join(' ')}`);
      
      // Make sure testScripts directory exists
      if (!fs.existsSync('testScripts/test-results')) {
        fs.mkdirSync('testScripts/test-results', { recursive: true });
      }
      
      const testProcess = spawn('node', args, {
        cwd: process.cwd(),
        env: { ...process.env, NODE_ENV: 'test' },
        detached: false, // Changed to false for better control
        stdio: ['ignore', 'pipe', 'pipe']
      });
      
      // Create a unique ID for this test run
      const testRunId = Date.now().toString();
      
      // Store process output for later retrieval
      const outputChunks: string[] = [];
      const errorChunks: string[] = [];
      
      // Initialize test results immediately so status can be polled right away
      // Use a type assertion to safely access the global testResults
      if (!(global as any).testResults) {
        (global as any).testResults = new Map();
      }
      
      // Add initial entry with enhanced status information
      // Determine the estimated total tests
      // Total test counts for each type
      const testCounts = {
        standard: 13,
        ai: 7,
        encryption: 8,
        specialized: 2
      };
      
      // Calculate total test count
      let totalTestCount = 0;
      
      if (testType === 'all') {
        // Sum all test types for 'all'
        totalTestCount = Object.values(testCounts).reduce((sum, count) => sum + count, 0);
      } else if (testType in testCounts) {
        totalTestCount = testCounts[testType as keyof typeof testCounts];
      } else {
        // Default fallback
        totalTestCount = 10;
      }
      
      console.log(`Starting ${testType} tests with estimated count: ${totalTestCount}`);
      
      (global as any).testResults.set(testRunId, {
        code: null,
        output: "Starting tests...\n",
        errors: "",
        completed: false,
        status: "running",
        startedAt: new Date().toISOString(),
        progress: {
          total: totalTestCount,
          completed: 0,
          currentTest: "Initializing test environment",
          currentSuite: testType || "standard"
        }
      });
      
      testProcess.stdout.on('data', (data) => {
        const chunk = data.toString();
        outputChunks.push(chunk);
        console.log(`[Test stdout] ${chunk.trim()}`);
        
        // Update the current output in real-time
        if ((global as any).testResults && (global as any).testResults.has(testRunId)) {
          const currentResult = (global as any).testResults.get(testRunId);
          
          // Extract current test information from the output
          let updatedProgress = { ...currentResult.progress };
          
          // Match completed test
          if (chunk.includes('✓') || chunk.includes('✗')) {
            updatedProgress.completed++;
            
            // Extract current test from output
            const testMatch = chunk.match(/(?:✓|✗)\s+(.*?)(?:\:|\n|$)/);
            if (testMatch && testMatch[1]) {
              updatedProgress.currentTest = testMatch[1].trim();
            }
          }
          
          // Match test suite name
          if (chunk.includes('=== Running') && chunk.includes('Tests ===')) {
            const suiteMatch = chunk.match(/=== Running\s+(.*?)\s+Tests ===/);
            if (suiteMatch && suiteMatch[1]) {
              updatedProgress.currentSuite = suiteMatch[1].trim();
              // Reset completed counter for new suite
              updatedProgress.completed = 0;
            }
          }
          
          // Update test results with progress information
          (global as any).testResults.set(testRunId, {
            ...currentResult,
            output: outputChunks.join(''),
            progress: updatedProgress
          });
        }
      });
      
      testProcess.stderr.on('data', (data) => {
        const chunk = data.toString();
        errorChunks.push(chunk);
        console.error(`[Test stderr] ${chunk.trim()}`);
        
        // Update the current errors in real-time
        if ((global as any).testResults && (global as any).testResults.has(testRunId)) {
          const currentResult = (global as any).testResults.get(testRunId);
          (global as any).testResults.set(testRunId, {
            ...currentResult,
            errors: errorChunks.join('')
          });
        }
      });
      
      // Return immediately with the test run ID
      res.status(202).json({ 
        message: "Test execution started", 
        testRunId,
        command: `node ${args.join(' ')}`
      });
      
      // When the process completes, store the results
      testProcess.on('close', (code) => {
        const output = outputChunks.join('');
        const errors = errorChunks.join('');
        
        // Store results in a global Map or database
        if (!(global as any).testResults) {
          (global as any).testResults = new Map();
        }
        
        // Extract summary information
        let summary = null;
        let duration = null;
        
        try {
          // Try to parse JSON summary from output
          const resultMatches = output.match(/\{[\s\S]*"summary"[\s\S]*\}/g);
          if (resultMatches && resultMatches.length > 0) {
            const resultJson = JSON.parse(resultMatches[0]);
            summary = resultJson.summary;
            duration = resultJson.duration;
          }
          // If no JSON, try to extract from output text
          else if (output.includes("=== Test Summary ===")) {
            const summaryMatch = output.match(/===\s*Test Summary\s*===[\s\S]*Pass rate:\s*(\d+)%/);
            const totalMatch = output.match(/Total tests:\s*(\d+)/);
            const passedMatch = output.match(/Passed:\s*(\d+)/);
            const failedMatch = output.match(/Failed:\s*(\d+)/);
            const durationMatch = output.match(/Duration:\s*(\d+)ms/);
            
            // Find all test suite summaries in the output
            const suiteRegex = /===\s*Running\s+(.*?)\s+Tests\s+===[\s\S]*?(\d+)\s+tests\s+passed[\s\S]*?(\d+)\s+tests\s+failed/g;
            let suiteMatch;
            let totalPassed = 0;
            let totalFailed = 0;
            
            while ((suiteMatch = suiteRegex.exec(output)) !== null) {
              const suitePassed = parseInt(suiteMatch[2], 10);
              const suiteFailed = parseInt(suiteMatch[3], 10);
              
              if (!isNaN(suitePassed)) totalPassed += suitePassed;
              if (!isNaN(suiteFailed)) totalFailed += suiteFailed;
            }
            
            // Check if we found tests in suites
            const totalFromSuites = totalPassed + totalFailed;
            
            if (summaryMatch) {
              const passRateStr = summaryMatch[1];
              let passRate = parseInt(passRateStr, 10);
              
              // If total from final summary is 0 but we have suite results, use those instead
              const finalTotal = totalMatch ? parseInt(totalMatch[1], 10) : 0;
              const finalPassed = passedMatch ? parseInt(passedMatch[1], 10) : 0;
              const finalFailed = failedMatch ? parseInt(failedMatch[1], 10) : 0;
              
              if ((finalTotal === 0 || isNaN(finalTotal)) && totalFromSuites > 0) {
                // Calculate pass rate based on suite results
                passRate = Math.round((totalPassed / totalFromSuites) * 100);
                
                summary = {
                  total: totalFromSuites,
                  passed: totalPassed,
                  failed: totalFailed,
                  passRate: passRate
                };
                
                console.log(`Aggregated test results from suites: ${totalPassed} passed, ${totalFailed} failed, ${passRate}% pass rate`);
              } else {
                // Use the values from the final summary
                summary = {
                  total: finalTotal,
                  passed: finalPassed,
                  failed: finalFailed,
                  passRate: isNaN(passRate) ? 0 : passRate
                };
              }
              
              if (durationMatch) {
                duration = parseInt(durationMatch[1], 10);
              }
            } else if (totalFromSuites > 0) {
              // No final summary but we have suite results
              const passRate = Math.round((totalPassed / totalFromSuites) * 100);
              
              summary = {
                total: totalFromSuites,
                passed: totalPassed,
                failed: totalFailed,
                passRate: passRate
              };
              
              console.log(`Aggregated test results from suites: ${totalPassed} passed, ${totalFailed} failed, ${passRate}% pass rate`);
            }
          }
        } catch (err) {
          console.error('Failed to parse test results:', err);
        }
        
        // Calculate execution time if no duration was found
        const startedAt = (global as any).testResults.get(testRunId)?.startedAt;
        if (!duration && startedAt) {
          const started = new Date(startedAt).getTime();
          const completed = new Date().getTime();
          duration = completed - started;
        }
        
        // Get current result to preserve progress information
        const currentResult = (global as any).testResults.get(testRunId);
        
        // Update the test status with final summary
        (global as any).testResults.set(testRunId, {
          ...currentResult,
          code,
          output,
          errors,
          completed: true,
          status: code === 0 ? "success" : "failed",
          completedAt: new Date().toISOString(),
          duration,
          summary,
          // Update progress to show completion
          progress: {
            ...currentResult.progress,
            total: summary?.total || currentResult.progress?.total || 0,
            completed: summary?.total || currentResult.progress?.total || 0,
            currentTest: 'Test execution completed',
            currentSuite: currentResult.progress?.currentSuite || 'all'
          },
          // Add debugging information about summary source
          summarySource: summary ? 
            (summary.total > 0 ? "final-summary" : "aggregated-manually") 
          : "not-found"
        });
        
        console.log(`Test process completed with exit code: ${code}`);
      });
      
      // Add event handlers for error cases
      testProcess.on('error', (err) => {
        console.error('Test process error:', err);
        if (!(global as any).testResults) {
          (global as any).testResults = new Map();
        }
        
        // Calculate execution time if possible
        let duration = null;
        const startedAt = (global as any).testResults.get(testRunId)?.startedAt;
        if (startedAt) {
          const started = new Date(startedAt).getTime();
          const completed = new Date().getTime();
          duration = completed - started;
        }
        
        // Get current result to preserve progress information
        const currentResult = (global as any).testResults.get(testRunId) || {};
        
        (global as any).testResults.set(testRunId, {
          ...currentResult,
          code: 1,
          output: currentResult.output || '',
          errors: `Failed to start test process: ${err.message}`,
          completed: true,
          status: "error",
          completedAt: new Date().toISOString(),
          duration,
          summary: {
            total: 0,
            passed: 0,
            failed: 1,
            passRate: 0
          },
          // Update progress to show error
          progress: {
            ...currentResult.progress,
            currentTest: 'Error: ' + err.message,
            currentSuite: currentResult.progress?.currentSuite || 'all'
          }
        });
      });
      
    } catch (error: any) {
      console.error('Error starting test process:', error);
      res.status(500).json({ message: "Failed to start test execution", error: error.message });
    }
  });
  
  app.get("/api/admin/tests/status/:testRunId", async (req, res) => {
    if (!req.user || !req.user.isAdmin) return res.sendStatus(403);
    
    const { testRunId } = req.params;
    
    // Check if the test run exists
    if (!(global as any).testResults || !(global as any).testResults.has(testRunId)) {
      return res.status(404).json({ message: "Test run not found" });
    }
    
    const result = (global as any).testResults.get(testRunId);
    res.json(result);
  });
  
  app.get("/api/admin/logs", async (req, res) => {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: "Unauthorized. Admin access required." });
    }

    try {
      // Use the storage interface to get user events
      const allEvents = await storage.getUserEvents();
      
      // Filter for admin events
      const adminEvents = allEvents.filter(event => 
        event.type.startsWith('admin_')
      );
      
      if (adminEvents.length === 0) {
        return res.json([]);
      }
      
      console.log(`Found ${adminEvents.length} admin events`);
      
      // Format logs for the client
      const formattedLogs = adminEvents.map(event => {
        return {
          id: event.id,
          type: event.type,
          timestamp: event.timestamp,
          details: event.details,
          reason: event.reason || null,
          username: event.username || 'Unknown User',
          adminUsername: event.adminUsername || 'Unknown Admin'
        };
      });
      
      res.json(formattedLogs);
    } catch (error) {
      console.error('Error fetching admin logs:', error);
      res.status(500).json({ message: "Error fetching admin logs" });
    }
  });

  // Notification routes
  app.get("/api/notifications", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    try {
      const notifications = await storage.getUserNotifications(req.user.id);
      res.json(notifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      res.status(500).json({ error: "Failed to fetch notifications" });
    }
  });

  app.get("/api/notifications/unread-count", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    try {
      const count = await storage.getUnreadNotificationCount(req.user.id);
      res.json(count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
      res.status(500).json({ error: "Failed to fetch unread count" });
    }
  });

  app.put("/api/notifications/:id/read", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const notificationId = parseInt(req.params.id);
    if (isNaN(notificationId)) {
      console.error('Invalid notification ID:', req.params.id);
      return res.status(400).json({ error: "Invalid notification ID" });
    }

    console.log(`Marking notification ${notificationId} as read for user ${req.user.id}`);

    try {
      const success = await storage.markNotificationAsRead(notificationId, req.user.id);
      console.log(`Mark as read result: ${success}`);
      
      if (!success) {
        console.error(`Notification ${notificationId} not found for user ${req.user.id}`);
        return res.status(404).json({ error: "Notification not found or unauthorized" });
      }
      
      res.json({ success: true });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      res.status(500).json({ error: "Failed to mark notification as read" });
    }
  });

  app.put("/api/notifications/mark-all-read", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    console.log(`Marking all notifications as read for user ${req.user.id}`);

    try {
      await storage.markAllNotificationsAsRead(req.user.id);
      console.log(`Successfully marked all notifications as read for user ${req.user.id}`);
      res.json({ success: true });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      res.status(500).json({ error: "Failed to mark all notifications as read" });
    }
  });

  app.delete("/api/notifications/:id", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const notificationId = parseInt(req.params.id);
    if (isNaN(notificationId)) {
      console.error('Invalid notification ID:', req.params.id);
      return res.status(400).json({ error: "Invalid notification ID" });
    }

    console.log(`Attempting to delete notification ${notificationId} for user ${req.user.id}`);

    try {
      const success = await storage.deleteNotification(notificationId, req.user.id);
      console.log(`Delete notification result: ${success}`);
      
      if (!success) {
        console.error(`Notification ${notificationId} not found for user ${req.user.id}`);
        return res.status(404).json({ error: "Notification not found or unauthorized" });
      }
      
      res.json({ success: true });
    } catch (error) {
      console.error('Error deleting notification:', error);
      res.status(500).json({ error: "Failed to delete notification" });
    }
  });

  // News routes
  app.get("/api/news", async (req, res) => {
    try {
      const newsArticles = await storage.getNews();
      res.json(newsArticles);
    } catch (error) {
      console.error('Error fetching news:', error);
      res.status(500).json({ error: "Failed to fetch news" });
    }
  });

  app.post("/api/news", async (req, res) => {
    if (!req.user?.isAdmin) return res.sendStatus(403);

    const parseResult = insertNewsSchema.safeParse(req.body);
    if (!parseResult.success) {
      return res.status(400).json({ 
        message: "Validation failed", 
        errors: parseResult.error.errors 
      });
    }

    try {
      const newsData = parseResult.data;
      const newsArticle = await storage.createNews({
        ...newsData,
        authorId: req.user.id
      });

      // If published, notify all users
      if (newsData.isPublished) {
        await NotificationService.createNewsNotification(
          req.user.id,
          newsArticle.id,
          newsData.title,
          newsData.content
        );
      }

      res.status(201).json(newsArticle);
    } catch (error) {
      console.error('Error creating news:', error);
      res.status(500).json({ error: "Failed to create news article" });
    }
  });

  app.put("/api/news/:id", async (req, res) => {
    if (!req.user?.isAdmin) return res.sendStatus(403);

    const newsId = parseInt(req.params.id);
    if (isNaN(newsId)) {
      return res.status(400).json({ error: "Invalid news ID" });
    }

    const parseResult = insertNewsSchema.safeParse(req.body);
    if (!parseResult.success) {
      return res.status(400).json({ 
        message: "Validation failed", 
        errors: parseResult.error.errors 
      });
    }

    try {
      const newsData = parseResult.data;
      
      // Check if we're publishing for the first time
      const existingNews = await storage.getNewsById(newsId);
      const wasUnpublished = existingNews && !existingNews.isPublished;
      
      const success = await storage.updateNews(newsId, newsData);
      
      if (!success) {
        return res.status(404).json({ error: "News article not found" });
      }

      // If publishing for the first time, notify all users
      if (newsData.isPublished && wasUnpublished) {
        await NotificationService.createNewsNotification(
          req.user.id,
          newsId,
          newsData.title,
          newsData.content
        );
      }

      res.json({ success: true });
    } catch (error) {
      console.error('Error updating news:', error);
      res.status(500).json({ error: "Failed to update news article" });
    }
  });

  app.delete("/api/news/:id", async (req, res) => {
    if (!req.user?.isAdmin) return res.sendStatus(403);

    const newsId = parseInt(req.params.id);
    if (isNaN(newsId)) {
      return res.status(400).json({ error: "Invalid news ID" });
    }

    try {
      const success = await storage.deleteNews(newsId);
      if (!success) {
        return res.status(404).json({ error: "News article not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error('Error deleting news:', error);
      res.status(500).json({ error: "Failed to delete news article" });
    }
  });

  // Friend Request Routes
  app.post("/api/friends/request", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const parseResult = insertFriendRequestSchema.safeParse(req.body);
    if (!parseResult.success) {
      return res.status(400).json({ 
        message: "Validation failed", 
        errors: parseResult.error.errors 
      });
    }

    try {
      const { receiverId } = parseResult.data;
      const friendRequest = await storage.sendFriendRequest(req.user.id, receiverId);
      
      // Create notification for receiver
      await NotificationService.createFriendRequestNotification(
        req.user.id,
        receiverId,
        req.user.username
      );

      res.status(201).json(friendRequest);
    } catch (error: any) {
      console.error('Error sending friend request:', error);
      res.status(400).json({ error: error.message || "Failed to send friend request" });
    }
  });

  app.get("/api/friends/requests", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    try {
      const requests = await storage.getFriendRequests(req.user.id);
      res.json(requests);
    } catch (error) {
      console.error('Error fetching friend requests:', error);
      res.status(500).json({ error: "Failed to fetch friend requests" });
    }
  });

  app.post("/api/friends/respond", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const { requestId, accept } = req.body;
    if (typeof requestId !== 'number' || typeof accept !== 'boolean') {
      return res.status(400).json({ error: "Invalid request data" });
    }

    try {
      const success = await storage.respondToFriendRequest(requestId, req.user.id, accept);
      if (!success) {
        return res.status(404).json({ error: "Friend request not found" });
      }

      // If accepted, create notification for requester
      if (accept) {
        const requests = await storage.getFriendRequests(req.user.id);
        const request = requests.find(r => r.id === requestId);
        if (request) {
          await NotificationService.createFriendAcceptNotification(
            req.user.id,
            request.requesterId,
            req.user.username
          );
        }
      }

      res.json({ success: true });
    } catch (error) {
      console.error('Error responding to friend request:', error);
      res.status(500).json({ error: "Failed to respond to friend request" });
    }
  });

  app.get("/api/friends", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    try {
      const friends = await storage.getFriends(req.user.id);
      res.json(friends);
    } catch (error) {
      console.error('Error fetching friends:', error);
      res.status(500).json({ error: "Failed to fetch friends" });
    }
  });

  app.get("/api/friends/check/:userId", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    try {
      const friendship = await storage.checkFriendship(req.user.id, userId);
      res.json(friendship);
    } catch (error) {
      console.error('Error checking friendship:', error);
      res.status(500).json({ error: "Failed to check friendship" });
    }
  });

  // Bio update route
  app.post("/api/user/bio", async (req, res) => {
    if (!req.user) return res.sendStatus(401);

    const parseResult = updateBioSchema.safeParse(req.body);
    if (!parseResult.success) {
      return res.status(400).json({ 
        message: "Validation failed", 
        errors: parseResult.error.errors 
      });
    }

    try {
      const { bio } = parseResult.data;
      await storage.updateUserBio(req.user.id, bio || '');
      res.json({ success: true });
    } catch (error) {
      console.error('Error updating bio:', error);
      res.status(500).json({ error: "Failed to update bio" });
    }
  });

  // Public profile route
  app.get("/api/users/:userId/profile", async (req, res) => {
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    try {
      const profile = await storage.getPublicProfile(userId);
      if (!profile) {
        return res.status(404).json({ error: "User not found" });
      }
      
      // Check friendship status between current user and profile user (if logged in)
      let friendStatus = 'none';
      if (req.user) {
        const friendship = await storage.checkFriendship(req.user.id, userId);
        friendStatus = friendship.requestStatus || (friendship.isFriend ? 'accepted' : 'none');
      }
      
      res.json({
        ...profile,
        friendStatus
      });
    } catch (error) {
      console.error('Error fetching public profile:', error);
      res.status(500).json({ error: "Failed to fetch profile" });
    }
  });

  // Route to get public profile by username
  app.get("/api/user/:username", async (req, res) => {
    const { username } = req.params;
    if (!username) {
      return res.status(400).json({ error: "Username is required" });
    }

    try {
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      const profile = await storage.getPublicProfile(user.id);
      if (!profile) {
        return res.status(404).json({ error: "Profile not found" });
      }
      
      // Check friendship status between current user and profile user (if logged in)
      let friendStatus = 'none';
      if (req.user) {
        const friendship = await storage.checkFriendship(req.user.id, user.id);
        friendStatus = friendship.requestStatus || (friendship.isFriend ? 'accepted' : 'none');
      }
      
      res.json({
        ...profile,
        friendStatus
      });
    } catch (error) {
      console.error('Error fetching public profile by username:', error);
      res.status(500).json({ error: "Failed to fetch profile" });
    }
  });

  return httpServer;
}