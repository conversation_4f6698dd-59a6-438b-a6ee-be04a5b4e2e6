import { supabaseAdmin, handleSupabaseError, DatabaseUser, DatabaseThread, DatabaseReply, DatabaseMessage, DatabaseAIChat, DatabaseAIChatSession } from "./supabase";
import type { IStorage } from "./storage";
import type { User, Thread, Reply, Vote, Message, AIChat, AIChatSession, ChatRoomMessage, UserEvent, Notification, News, FriendRequest } from "@shared/schema";
import session from "express-session";

export class SupabaseStorage implements IStorage {
  public sessionStore: session.Store;

  constructor() {
    // Use memory store for sessions since we're using Supabase Auth
    this.sessionStore = new session.MemoryStore();
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return undefined;
        handleSupabaseError(error, 'getUser');
      }

      return this.mapDatabaseUserToUser(data);
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return undefined;
        handleSupabaseError(error, 'getUserByUsername');
      }

      return this.mapDatabaseUserToUser(data);
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async createUser(userData: Partial<User>): Promise<User> {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .insert({
          username: userData.username!,
          password: userData.password || '',
          is_admin: userData.isAdmin || false,
          avatar: userData.avatar,
          bio: userData.bio || '',
          public_key: userData.publicKey,
          auto_encryption_enabled: userData.autoEncryptionEnabled !== false,
          created_at: new Date().toISOString(),
          last_password_change: new Date().toISOString(),
          login_count: 0
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, 'createUser');
      }

      return this.mapDatabaseUserToUser(data);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async getUsers(): Promise<User[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        handleSupabaseError(error, 'getUsers');
      }

      return data.map(user => this.mapDatabaseUserToUser(user));
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }

  // Thread operations
  async getThreads(): Promise<(Thread & { authorName: string; voteCount: number })[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('threads')
        .select(`
          *,
          users!threads_author_id_fkey(username),
          votes(vote_type)
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (error) {
        handleSupabaseError(error, 'getThreads');
      }

      return data.map(thread => ({
        id: thread.id,
        title: thread.title,
        content: thread.content,
        authorId: thread.author_id,
        category: thread.category,
        isDeleted: thread.is_deleted,
        createdAt: new Date(thread.created_at).getTime(),
        authorName: thread.users.username,
        voteCount: this.calculateVoteCount(thread.votes)
      }));
    } catch (error) {
      console.error('Error getting threads:', error);
      return [];
    }
  }

  async getThreadById(threadId: number): Promise<(Thread & { authorName: string }) | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('threads')
        .select(`
          *,
          users!threads_author_id_fkey(username)
        `)
        .eq('id', threadId)
        .eq('is_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return undefined;
        handleSupabaseError(error, 'getThreadById');
      }

      return {
        id: data.id,
        title: data.title,
        content: data.content,
        authorId: data.author_id,
        category: data.category,
        isDeleted: data.is_deleted,
        createdAt: new Date(data.created_at).getTime(),
        authorName: data.users.username
      };
    } catch (error) {
      console.error('Error getting thread by ID:', error);
      return undefined;
    }
  }

  async createThread(threadData: Partial<Thread>): Promise<Thread> {
    try {
      const { data, error } = await supabaseAdmin
        .from('threads')
        .insert({
          title: threadData.title!,
          content: threadData.content!,
          author_id: threadData.authorId!,
          category: threadData.category || 'GENERAL',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, 'createThread');
      }

      return {
        id: data.id,
        title: data.title,
        content: data.content,
        authorId: data.author_id,
        category: data.category,
        isDeleted: data.is_deleted,
        createdAt: new Date(data.created_at).getTime()
      };
    } catch (error) {
      console.error('Error creating thread:', error);
      throw error;
    }
  }

  // Helper methods
  private mapDatabaseUserToUser(dbUser: DatabaseUser): User {
    return {
      id: dbUser.id,
      username: dbUser.username,
      password: dbUser.password,
      isAdmin: dbUser.is_admin,
      avatar: dbUser.avatar,
      bio: dbUser.bio,
      publicKey: dbUser.public_key,
      autoEncryptionEnabled: dbUser.auto_encryption_enabled,
      createdAt: new Date(dbUser.created_at).getTime(),
      lastPasswordChange: new Date(dbUser.last_password_change).getTime(),
      suspendedUntil: dbUser.suspended_until ? new Date(dbUser.suspended_until).getTime() : undefined,
      bannedAt: dbUser.banned_at ? new Date(dbUser.banned_at).getTime() : undefined,
      banReason: dbUser.ban_reason,
      lastLogin: dbUser.last_login ? new Date(dbUser.last_login).getTime() : undefined,
      lastIp: dbUser.last_ip,
      loginCount: dbUser.login_count
    };
  }

  private calculateVoteCount(votes: { vote_type: string }[]): number {
    return votes.reduce((count, vote) => {
      return count + (vote.vote_type === 'up' ? 1 : -1);
    }, 0);
  }

  // Placeholder methods - will implement these next
  async getRepliesByThreadId(threadId: number): Promise<(Reply & { authorName: string })[]> {
    // TODO: Implement
    return [];
  }

  async createReply(replyData: Partial<Reply>): Promise<Reply> {
    // TODO: Implement
    throw new Error('Not implemented');
  }

  async getVotesByThreadId(threadId: number): Promise<Vote[]> {
    // TODO: Implement
    return [];
  }

  async createVote(voteData: Partial<Vote>): Promise<Vote> {
    // TODO: Implement
    throw new Error('Not implemented');
  }

  async updateVote(voteId: number, voteType: string): Promise<void> {
    // TODO: Implement
  }

  async deleteVote(voteId: number): Promise<void> {
    // TODO: Implement
  }

  async getMessagesBetweenUsers(userId1: number, userId2: number): Promise<any[]> {
    // TODO: Implement
    return [];
  }

  async createMessage(data: { senderId: number, recipientId: number, content: string, nonce: string, isEncrypted?: boolean }): Promise<any> {
    // TODO: Implement
    throw new Error('Not implemented');
  }

  async getAllMessages(userId: number): Promise<any[]> {
    // TODO: Implement
    return [];
  }

  async updateUserPublicKey(userId: number, publicKey: string): Promise<void> {
    // TODO: Implement
  }

  async getUserPublicKey(userId: number): Promise<string | undefined> {
    // TODO: Implement
    return undefined;
  }

  async createAIChat(chat: any): Promise<AIChat> {
    // TODO: Implement
    throw new Error('Not implemented');
  }

  async getAIChatsByUserId(userId: number): Promise<AIChat[]> {
    // TODO: Implement
    return [];
  }

  async getAIChatsBySessionId(sessionId: number): Promise<AIChat[]> {
    // TODO: Implement
    return [];
  }

  async updateUserLoginInfo(userId: number, ipAddress: string): Promise<void> {
    // TODO: Implement
  }
}
