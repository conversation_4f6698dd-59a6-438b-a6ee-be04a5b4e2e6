# 🚀 Crow-AI Forum - Production Environment Variables
# Copy this file to .env and update with your actual values
# NEVER commit .env file to version control - it contains sensitive data

# ================================
# 🌍 DEPLOYMENT ENVIRONMENT
# ================================
NODE_ENV=production

# ================================
# 🗄️ SUPABASE DATABASE CONFIGURATION
# ================================
# Your Supabase project URL (found in Settings > API)
SUPABASE_URL=https://zfstxixesisigvqpycuw.supabase.co

# Public anonymous key for client-side operations (safe to expose)
# Found in Supabase Dashboard > Settings > API > Project API keys > anon public
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgxNTMzNjMsImV4cCI6MjA3MzcyOTM2M30.xAQRT4uOVUXMr7cf6aGBdrgeXpM6y5nsM3pS7O-uxLg

# Service role key for server-side operations (KEEP SECRET!)
# Found in Supabase Dashboard > Settings > API > Project API keys > service_role secret
# ⚠️ CRITICAL: Never expose this key in client-side code
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODE1MzM2MywiZXhwIjoyMDczNzI5MzYzfQ.YU4Z8FmivHEimg1hV4fpcdy5lTA3ikno2hSHl5jvVog

# ================================
# 🔐 SECURITY CONFIGURATION
# ================================
# Session secret for encrypting user sessions (MUST be changed in production)
# Generate with: openssl rand -base64 32
# ⚠️ CRITICAL: Use a strong, unique secret in production
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# Security settings for production deployment
TRUST_PROXY=true
SECURE_COOKIES=true

# ================================
# 🤖 AI INTEGRATION (OPTIONAL)
# ================================
# DeepSeek API key for AI chat functionality
# Get from: https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# OpenRouter API key for additional AI models
# Get from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your-openrouter-api-key-here

# ================================
# 🖥️ SERVER CONFIGURATION
# ================================
# Server port (not used in Cloudflare Pages, but needed for local development)
PORT=5000

# Server host binding
HOST=0.0.0.0

# ================================
# 👑 ADMIN CONFIGURATION
# ================================
# Default admin credentials (CHANGE THESE IN PRODUCTION!)
# ⚠️ CRITICAL: Use strong, unique credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-secure-password

# ================================
# 📧 EMAIL CONFIGURATION (FUTURE)
# ================================
# Resend API key for email functionality
# RESEND_API_KEY=your-resend-api-key-here

# Email sender configuration
# EMAIL_FROM=<EMAIL>
# EMAIL_FROM_NAME=Crow-AI Forum

# ================================
# 🔧 DEVELOPMENT SETTINGS
# ================================
# Enable debug logging (set to false in production)
DEBUG=false

# Enable development features (set to false in production)
DEV_MODE=false

# ================================
# 📊 ANALYTICS (FUTURE)
# ================================
# Google Analytics tracking ID
# GA_TRACKING_ID=G-XXXXXXXXXX

# Cloudflare Web Analytics token
# CF_WEB_ANALYTICS_TOKEN=your-token-here
