# Production Environment Variables for Crow-AI Forum

# Node Environment
NODE_ENV=production

# Supabase Configuration
SUPABASE_URL=https://zfstxixesisigvqpycuw.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgxNTMzNjMsImV4cCI6MjA3MzcyOTM2M30.xAQRT4uOVUXMr7cf6aGBdrgeXpM6y5nsM3pS7O-uxLg
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODE1MzM2MywiZXhwIjoyMDczNzI5MzYzfQ.YU4Z8FmivHEimg1hV4fpcdy5lTA3ikno2hSHl5jvVog

# Session Secret (change this in production)
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# AI Configuration (optional - for AI chat features)
DEEPSEEK_API_KEY=your-deepseek-api-key-here
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Server Configuration
PORT=5000
HOST=0.0.0.0

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-secure-password

# Security Settings
TRUST_PROXY=true
SECURE_COOKIES=true
