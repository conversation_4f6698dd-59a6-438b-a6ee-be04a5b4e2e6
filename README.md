# Crow-AI Forum

A modern, terminal-themed community forum with integrated AI chat capabilities, built with React, TypeScript, and Supabase.

## Features

- 🔐 **Secure Authentication** - Email-based signup/login with Supabase Auth
- 💬 **Forum Discussions** - Create threads, reply, and vote on content
- 🤖 **AI Chat Integration** - Built-in AI chat capabilities
- 🔒 **End-to-End Encryption** - Secure messaging between users
- 👥 **User Management** - Admin controls, user profiles, and moderation
- 🎨 **Terminal Theme** - Retro terminal aesthetic with modern UX
- 📱 **Responsive Design** - Works on desktop and mobile devices

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite, TailwindCSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Deployment**: Cloudflare Pages
- **UI Components**: Radix UI, Lucide Icons

## Production Deployment

### Prerequisites

1. Supabase project set up with the database schema
2. Cloudflare account for Pages deployment
3. Environment variables configured

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Required
NODE_ENV=production
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SESSION_SECRET=your-secure-session-secret

# Optional (for AI features)
DEEPSEEK_API_KEY=your-deepseek-api-key
OPENROUTER_API_KEY=your-openrouter-api-key
```

### Cloudflare Pages Deployment

1. **Build the application**:
   ```bash
   npm install
   npm run build
   ```

2. **Deploy to Cloudflare Pages**:
   ```bash
   npm run deploy
   ```

3. **Configure environment variables** in Cloudflare Pages dashboard:
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `SESSION_SECRET`

### Database Setup

The Supabase database is already configured with:
- User authentication tables
- Forum threads and replies
- Voting system
- Direct messaging with E2EE
- AI chat sessions
- Admin and moderation features
- Row Level Security (RLS) policies

### Security Features

- **Row Level Security**: All database tables have RLS policies
- **JWT Authentication**: Secure token-based authentication
- **CORS Protection**: Configured for production domains
- **Input Validation**: All user inputs are validated and sanitized
- **Rate Limiting**: Built-in protection against abuse

## Development

### Local Development

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

### Project Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utility libraries
│   │   └── pages/          # Page components
├── server/                 # Express backend
│   ├── auth.ts            # Authentication logic
│   ├── routes.ts          # API routes
│   ├── supabase.ts        # Supabase client
│   └── supabase-storage.ts # Database operations
├── shared/                 # Shared types and schemas
└── functions/             # Cloudflare Pages Functions
```

## API Endpoints

- `POST /api/login` - User login
- `POST /api/register` - User registration
- `POST /api/logout` - User logout
- `GET /api/user` - Get current user
- `GET /api/threads` - Get forum threads
- `POST /api/threads` - Create new thread
- `GET /api/threads/:id` - Get specific thread
- `POST /api/replies` - Create reply
- `POST /api/votes` - Vote on thread

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions, please create an issue in the GitHub repository.
