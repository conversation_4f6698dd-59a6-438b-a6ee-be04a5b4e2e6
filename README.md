# Crow-AI Forum

A modern, terminal-themed community forum with integrated AI chat capabilities, built with React, TypeScript, and Supabase.

## Features

- 🔐 **Secure Authentication** - Email-based signup/login with <PERSON>pabase Auth
- 💬 **Forum Discussions** - Create threads, reply, and vote on content
- 🤖 **AI Chat Integration** - Built-in AI chat capabilities
- 🔒 **End-to-End Encryption** - Secure messaging between users
- 👥 **User Management** - Admin controls, user profiles, and moderation
- 🎨 **Terminal Theme** - Retro terminal aesthetic with modern UX
- 📱 **Responsive Design** - Works on desktop and mobile devices

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite, TailwindCSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Deployment**: Cloudflare Pages
- **UI Components**: Radix UI, Lucide Icons

## 🚀 Production Deployment

### 📋 Prerequisites

1. **Supabase Project**: Database configured with RLS policies and authentication
2. **Cloudflare Account**: Pages deployment access
3. **Domain Setup**: crow-ai.net configured in Cloudflare
4. **Environment Variables**: Production secrets configured

### 🔧 Quick Production Setup

#### Step 1: Install Dependencies

```bash
npm install
```

#### Step 2: Configure Environment Variables

**For Cloudflare Pages (Recommended):**

1. Go to Cloudflare Pages Dashboard
2. Select your `crow-ai-forum` project
3. Navigate to Settings > Environment variables
4. Add the following variables for **Production** environment:

```bash
NODE_ENV=production
SUPABASE_URL=https://zfstxixesisigvqpycuw.supabase.co
SUPABASE_ANON_KEY=[from Supabase Dashboard > Settings > API]
SUPABASE_SERVICE_ROLE_KEY=[from Supabase Dashboard > Settings > API]
SESSION_SECRET=[generate with: openssl rand -base64 32]
```

**For Local Testing:**

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your actual values
# See .env.example for detailed configuration instructions
```

#### Step 3: Build and Deploy

```bash
# Build the application
npm run build:full

# Deploy to Cloudflare Pages
npm run deploy

# Or deploy with preview
npm run deploy:preview
```

### 🛡️ Security Configuration

The application includes comprehensive security headers via `_headers` file:

- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options, X-Content-Type-Options
- Permissions Policy for enhanced privacy

### ⚡ Performance Optimizations

- **Code Splitting**: Vendor, UI, and Supabase chunks
- **Asset Optimization**: Minification and compression
- **Caching**: Aggressive caching for static assets
- **Bundle Analysis**: Optimized chunk sizes

### 🔍 Production Verification

After deployment, verify:

1. **Functionality**: User registration, login, forum posting, AI chat
2. **Security**: Test at [Security Headers](https://securityheaders.com) (target: A+)
3. **Performance**: Lighthouse audit (target: 90+ scores)
4. **SSL**: Valid certificate at <https://crow-ai.net>

### 📊 Database Setup

The Supabase database is already configured with:

- User authentication tables
- Forum threads and replies
- Voting system
- Direct messaging with E2EE
- AI chat sessions
- Admin and moderation features
- Row Level Security (RLS) policies

### Security Features

- **Row Level Security**: All database tables have RLS policies
- **JWT Authentication**: Secure token-based authentication
- **CORS Protection**: Configured for production domains
- **Input Validation**: All user inputs are validated and sanitized
- **Rate Limiting**: Built-in protection against abuse

## Development

### Local Development

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

### Project Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utility libraries
│   │   └── pages/          # Page components
├── server/                 # Express backend
│   ├── auth.ts            # Authentication logic
│   ├── routes.ts          # API routes
│   ├── supabase.ts        # Supabase client
│   └── supabase-storage.ts # Database operations
├── shared/                 # Shared types and schemas
└── functions/             # Cloudflare Pages Functions
```

## API Endpoints

- `POST /api/login` - User login
- `POST /api/register` - User registration
- `POST /api/logout` - User logout
- `GET /api/user` - Get current user
- `GET /api/threads` - Get forum threads
- `POST /api/threads` - Create new thread
- `GET /api/threads/:id` - Get specific thread
- `POST /api/replies` - Create reply
- `POST /api/votes` - Vote on thread

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions, please create an issue in the GitHub repository.
