@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');

:root {
  --primary: #FF8C00;
  --secondary: #1A1A1A;
  --accent: #FFB366;
  --text: #FFA500;
  --glow: #FF4500;
  --scanline-opacity: 0.25;
  --crt-opacity: 0.1;
  --flicker-enabled: 0;
  --glow-enabled: 1;
}

.crt {
  font-family: 'VT323', monospace;
  background: var(--secondary);
  position: relative;
  overflow: hidden;
}

.crt::before {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    to bottom,
    rgba(18, 16, 16, 0) 50%,
    rgba(0, 0, 0, var(--scanline-opacity)) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 100;
  opacity: var(--scanline-opacity);
}

.crt::after {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(18, 16, 16, var(--crt-opacity));
  opacity: calc(var(--flicker-enabled) * 0.3);
  z-index: 100;
  pointer-events: none;
  animation: flicker 0.15s infinite;
  animation-play-state: paused;
}

.crt.flicker-enabled::after {
  animation-play-state: running;
}

@keyframes flicker {
  0% { opacity: 0.27861; }
  5% { opacity: 0.34769; }
  10% { opacity: 0.23604; }
  15% { opacity: 0.90626; }
  20% { opacity: 0.18128; }
  25% { opacity: 0.83891; }
  30% { opacity: 0.65583; }
  35% { opacity: 0.67807; }
  40% { opacity: 0.26559; }
  45% { opacity: 0.84693; }
  50% { opacity: 0.96019; }
  55% { opacity: 0.08594; }
  60% { opacity: 0.20313; }
  65% { opacity: 0.71988; }
  70% { opacity: 0.53455; }
  75% { opacity: 0.37288; }
  80% { opacity: 0.71428; }
  85% { opacity: 0.70419; }
  90% { opacity: 0.7003; }
  95% { opacity: 0.36108; }
  100% { opacity: 0.24387; }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* Enhanced glow effects */
.terminal-text {
  text-shadow: calc(var(--glow-enabled) * 0px 0px 10px var(--glow));
}

/* CRT curvature effect */
.crt {
  border-radius: calc(var(--crt-opacity) * 20px);
  box-shadow: 
    inset 0 0 calc(var(--crt-opacity) * 100px) rgba(0, 0, 0, 0.5),
    0 0 calc(var(--crt-opacity) * 20px) var(--glow);
  transform: perspective(1000px) 
             rotateX(calc(var(--crt-opacity) * 2deg)) 
             rotateY(calc(var(--crt-opacity) * 1deg));
}

/* Responsive scanlines */
@media (max-width: 768px) {
  .crt::before {
    background-size: 100% 2px;
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .crt::before {
    opacity: calc(var(--scanline-opacity) * 0.5);
  }
  
  .crt::after {
    opacity: calc(var(--flicker-enabled) * 0.1);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .crt::after {
    animation: none !important;
  }
}

.animate-blink {
  animation: blink 1s step-end infinite;
}

.text-glow {
  text-shadow: 0 0 5px var(--glow);
  color: var(--text);
}

.terminal-input {
  background: transparent;
  border: 1px solid var(--text);
  color: var(--text);
  padding: 0.5rem;
  font-family: 'VT323', monospace;
  font-size: 1.2rem;
}

.terminal-input:focus {
  outline: none;
  box-shadow: 0 0 10px var(--glow);
}