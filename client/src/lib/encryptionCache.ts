import { clientLogger } from './logger';
import { fetchUserPublicKey } from './encryption';

/**
 * Performance Optimizations for Encryption Operations
 * Intelligent caching and batch operations for better performance
 */

const cacheLogger = {
  debug: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      clientLogger.log(`[EncryptionCache] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    clientLogger.log(`[EncryptionCache] ${message}`, data);
  },
  warn: (message: string, error?: any) => {
    console.warn(`[EncryptionCache Warning] ${message}`, error);
  },
  error: (message: string, error?: any) => {
    clientLogger.error(`[EncryptionCache] ${message}`, error);
  }
};

interface CachedKey {
  publicKey: string;
  userId: number;
  username?: string;
  cachedAt: Date;
  expiresAt: Date;
  fetchCount: number;
  lastAccessed: Date;
}

interface CacheMetrics {
  hits: number;
  misses: number;
  evictions: number;
  totalFetches: number;
  avgFetchTime: number;
}

class PublicKeyCache {
  private static instance: PublicKeyCache;
  private cache = new Map<number, CachedKey>();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalFetches: 0,
    avgFetchTime: 0
  };
  
  // Cache configuration
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of cached keys
  private readonly DEFAULT_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours
  private readonly CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // 1 hour
  private cleanupTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupTimer();
  }

  public static getInstance(): PublicKeyCache {
    if (!PublicKeyCache.instance) {
      PublicKeyCache.instance = new PublicKeyCache();
    }
    return PublicKeyCache.instance;
  }

  /**
   * Get public key with intelligent caching
   */
  public async getPublicKey(userId: number, username?: string): Promise<string | null> {
    const startTime = Date.now();
    
    try {
      // Check cache first
      const cached = this.getCachedKey(userId);
      if (cached) {
        this.metrics.hits++;
        cached.lastAccessed = new Date();
        cached.fetchCount++;
        
        cacheLogger.debug(`Cache hit for user ${userId}`, {
          cachedAt: cached.cachedAt,
          fetchCount: cached.fetchCount
        });
        
        return cached.publicKey;
      }

      // Cache miss - fetch from server
      this.metrics.misses++;
      cacheLogger.debug(`Cache miss for user ${userId}, fetching from server`);

      const publicKey = await this.fetchAndCacheKey(userId, username);
      
      // Update fetch time metrics
      const fetchTime = Date.now() - startTime;
      this.updateFetchTimeMetrics(fetchTime);
      
      return publicKey;
    } catch (error) {
      cacheLogger.error(`Failed to get public key for user ${userId}`, error);
      throw error;
    }
  }

  /**
   * Batch fetch multiple public keys efficiently
   */
  public async batchGetPublicKeys(userIds: number[]): Promise<Map<number, string | null>> {
    const results = new Map<number, string | null>();
    const uncachedUserIds: number[] = [];

    // Check cache for all users first
    for (const userId of userIds) {
      const cached = this.getCachedKey(userId);
      if (cached) {
        results.set(userId, cached.publicKey);
        cached.lastAccessed = new Date();
        cached.fetchCount++;
        this.metrics.hits++;
      } else {
        uncachedUserIds.push(userId);
        this.metrics.misses++;
      }
    }

    // Batch fetch uncached keys
    if (uncachedUserIds.length > 0) {
      cacheLogger.debug(`Batch fetching ${uncachedUserIds.length} uncached keys`);
      
      const fetchPromises = uncachedUserIds.map(async (userId) => {
        try {
          const publicKey = await this.fetchAndCacheKey(userId);
          return { userId, publicKey };
        } catch (error) {
          cacheLogger.warn(`Failed to fetch key for user ${userId}`, error);
          return { userId, publicKey: null };
        }
      });

      const fetchResults = await Promise.allSettled(fetchPromises);
      
      fetchResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          results.set(result.value.userId, result.value.publicKey);
        }
      });
    }

    cacheLogger.debug(`Batch operation completed`, {
      totalRequested: userIds.length,
      cacheHits: userIds.length - uncachedUserIds.length,
      networkFetches: uncachedUserIds.length
    });

    return results;
  }

  /**
   * Preload keys for frequently contacted users
   */
  public async preloadFrequentKeys(userIds: number[]): Promise<void> {
    try {
      cacheLogger.debug(`Preloading ${userIds.length} frequent user keys`);
      
      const uncachedUsers = userIds.filter(userId => !this.getCachedKey(userId));
      
      if (uncachedUsers.length > 0) {
        await this.batchGetPublicKeys(uncachedUsers);
        cacheLogger.info(`Preloaded ${uncachedUsers.length} keys for better performance`);
      }
    } catch (error) {
      cacheLogger.error('Failed to preload frequent keys', error);
    }
  }

  /**
   * Invalidate cache for a specific user (when their key changes)
   */
  public invalidateKey(userId: number): void {
    if (this.cache.delete(userId)) {
      cacheLogger.debug(`Invalidated cache for user ${userId}`);
    }
  }

  /**
   * Clear entire cache
   */
  public clearCache(): void {
    const cacheSize = this.cache.size;
    this.cache.clear();
    this.resetMetrics();
    cacheLogger.info(`Cleared entire cache (${cacheSize} entries)`);
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    metrics: CacheMetrics;
    oldestEntry?: Date;
    newestEntry?: Date;
  } {
    const entries = Array.from(this.cache.values());
    const totalRequests = this.metrics.hits + this.metrics.misses;
    
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRate: totalRequests > 0 ? (this.metrics.hits / totalRequests) * 100 : 0,
      metrics: { ...this.metrics },
      oldestEntry: entries.length > 0 ? new Date(Math.min(...entries.map(e => e.cachedAt.getTime()))) : undefined,
      newestEntry: entries.length > 0 ? new Date(Math.max(...entries.map(e => e.cachedAt.getTime()))) : undefined
    };
  }

  /**
   * Get cached key if valid
   */
  private getCachedKey(userId: number): CachedKey | null {
    const cached = this.cache.get(userId);
    
    if (!cached) {
      return null;
    }

    // Check if expired
    if (new Date() > cached.expiresAt) {
      this.cache.delete(userId);
      cacheLogger.debug(`Expired cache entry removed for user ${userId}`);
      return null;
    }

    return cached;
  }

  /**
   * Fetch key from server and cache it
   */
  private async fetchAndCacheKey(userId: number, username?: string): Promise<string | null> {
    try {
      const publicKey = await fetchUserPublicKey(userId);
      
      if (publicKey) {
        this.cacheKey(userId, publicKey, username);
      }
      
      return publicKey;
    } catch (error) {
      cacheLogger.error(`Failed to fetch public key for user ${userId}`, error);
      throw error;
    }
  }

  /**
   * Cache a public key
   */
  private cacheKey(userId: number, publicKey: string, username?: string): void {
    // Check if cache is full and evict if necessary
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastRecentlyUsed();
    }

    const now = new Date();
    const cachedKey: CachedKey = {
      publicKey,
      userId,
      username,
      cachedAt: now,
      expiresAt: new Date(now.getTime() + this.DEFAULT_TTL_MS),
      fetchCount: 1,
      lastAccessed: now
    };

    this.cache.set(userId, cachedKey);
    cacheLogger.debug(`Cached public key for user ${userId}`, {
      cacheSize: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE
    });
  }

  /**
   * Evict least recently used entries
   */
  private evictLeastRecentlyUsed(): void {
    let oldestEntry: { userId: number; lastAccessed: Date } | null = null;
    
    for (const [userId, cached] of this.cache.entries()) {
      if (!oldestEntry || cached.lastAccessed < oldestEntry.lastAccessed) {
        oldestEntry = { userId, lastAccessed: cached.lastAccessed };
      }
    }

    if (oldestEntry) {
      this.cache.delete(oldestEntry.userId);
      this.metrics.evictions++;
      cacheLogger.debug(`Evicted LRU entry for user ${oldestEntry.userId}`);
    }
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.CLEANUP_INTERVAL_MS);
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredEntries(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [userId, cached] of this.cache.entries()) {
      if (now > cached.expiresAt) {
        this.cache.delete(userId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      cacheLogger.debug(`Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  /**
   * Update fetch time metrics
   */
  private updateFetchTimeMetrics(fetchTime: number): void {
    this.metrics.totalFetches++;
    
    // Calculate rolling average
    this.metrics.avgFetchTime = 
      ((this.metrics.avgFetchTime * (this.metrics.totalFetches - 1)) + fetchTime) / 
      this.metrics.totalFetches;
  }

  /**
   * Reset metrics
   */
  private resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalFetches: 0,
      avgFetchTime: 0
    };
  }

  /**
   * Cleanup on destruction
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clearCache();
  }
}

/**
 * Message chunking for large messages
 */
export class MessageChunker {
  private static readonly CHUNK_SIZE = 32 * 1024; // 32KB chunks
  private static readonly MAX_MESSAGE_SIZE = 1024 * 1024; // 1MB max

  /**
   * Split large message into chunks for encryption
   */
  public static chunkMessage(message: string): string[] {
    if (message.length <= this.CHUNK_SIZE) {
      return [message];
    }

    if (message.length > this.MAX_MESSAGE_SIZE) {
      throw new Error('Message too large for encryption');
    }

    const chunks: string[] = [];
    for (let i = 0; i < message.length; i += this.CHUNK_SIZE) {
      chunks.push(message.slice(i, i + this.CHUNK_SIZE));
    }

    cacheLogger.debug(`Split message into ${chunks.length} chunks`);
    return chunks;
  }

  /**
   * Reconstruct message from chunks
   */
  public static reconstructMessage(chunks: string[]): string {
    const message = chunks.join('');
    cacheLogger.debug(`Reconstructed message from ${chunks.length} chunks`);
    return message;
  }
}

/**
 * Encryption operation batching for better performance
 */
export class EncryptionBatcher {
  private pendingOperations: Array<{
    message: string;
    recipientId: number;
    resolve: (result: any) => void;
    reject: (error: any) => void;
  }> = [];

  private batchTimer: NodeJS.Timeout | null = null;
  private readonly BATCH_DELAY_MS = 10; // 10ms batch window

  /**
   * Add encryption operation to batch
   */
  public queueEncryption(message: string, recipientId: number): Promise<any> {
    return new Promise((resolve, reject) => {
      this.pendingOperations.push({
        message,
        recipientId,
        resolve,
        reject
      });

      // Start batch timer if not already running
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.processBatch();
        }, this.BATCH_DELAY_MS);
      }
    });
  }

  /**
   * Process batched operations
   */
  private async processBatch(): Promise<void> {
    if (this.pendingOperations.length === 0) {
      return;
    }

    const operations = [...this.pendingOperations];
    this.pendingOperations = [];
    this.batchTimer = null;

    cacheLogger.debug(`Processing batch of ${operations.length} encryption operations`);

    // Group by recipient for optimal key fetching
    const operationsByRecipient = new Map<number, typeof operations>();
    
    operations.forEach(op => {
      if (!operationsByRecipient.has(op.recipientId)) {
        operationsByRecipient.set(op.recipientId, []);
      }
      operationsByRecipient.get(op.recipientId)!.push(op);
    });

    // Process each recipient group
    for (const [recipientId, recipientOps] of operationsByRecipient) {
      try {
        // Batch fetch recipient key
        const keyCache = PublicKeyCache.getInstance();
        const recipientPublicKey = await keyCache.getPublicKey(recipientId);

        // Process all operations for this recipient
        recipientOps.forEach(op => {
          // Individual encryption logic would go here
          // For now, just resolve with success
          op.resolve({ 
            success: true, 
            recipientPublicKey 
          });
        });
      } catch (error) {
        // Reject all operations for this recipient
        recipientOps.forEach(op => {
          op.reject(error);
        });
      }
    }
  }
}

// Export singleton instances
export const publicKeyCache = PublicKeyCache.getInstance();
export const encryptionBatcher = new EncryptionBatcher();