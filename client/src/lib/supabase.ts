import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://zfstxixesisigvqpycuw.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpmc3R4aXhlc2lzaWd2cXB5Y3V3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgxNTMzNjMsImV4cCI6MjA3MzcyOTM2M30.xAQRT4uOVUXMr7cf6aGBdrgeXpM6y5nsM3pS7O-uxLg';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Helper to get auth headers for API requests
export function getAuthHeaders() {
  const session = supabase.auth.getSession();
  return session ? {
    'Authorization': `Bearer ${session.data.session?.access_token}`
  } : {};
}

// Helper to make authenticated API requests
export async function makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
  const session = await supabase.auth.getSession();
  
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (session.data.session?.access_token) {
    headers['Authorization'] = `Bearer ${session.data.session.access_token}`;
  }

  return fetch(url, {
    ...options,
    headers,
    credentials: 'include'
  });
}
