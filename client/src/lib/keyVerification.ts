import * as naclUtil from 'tweetnacl-util';
import { clientLogger } from './logger';

/**
 * Key Verification and Fingerprinting utilities
 * Helps users verify they're communicating with the right person
 */

const verifyLogger = {
  debug: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      clientLogger.log(`[KeyVerify] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    clientLogger.log(`[KeyVerify] ${message}`, data);
  },
  warn: (message: string, error?: any) => {
    console.warn(`[KeyVerify Warning] ${message}`, error);
  },
  error: (message: string, error?: any) => {
    clientLogger.error(`[KeyVerify] ${message}`, error);
  }
};

export interface KeyFingerprint {
  publicKey: string;
  fingerprint: string;
  shortFingerprint: string;
  emoji: string;
  words: string;
  userId: number;
  username: string;
  verificationStatus: 'unverified' | 'pending' | 'verified' | 'warning';
  verifiedAt?: Date;
  verificationMethod?: 'manual' | 'qr-code' | 'voice-call' | 'in-person';
}

export interface VerificationRecord {
  userId: number;
  keyFingerprint: string;
  verifiedAt: Date;
  verificationMethod: string;
  notes?: string;
}

/**
 * Generate a human-readable fingerprint from a public key
 */
export async function generateKeyFingerprint(publicKeyBase64: string): Promise<string> {
  try {
    const publicKeyBytes = naclUtil.decodeBase64(publicKeyBase64);
    
    // Use SubtleCrypto to generate SHA-256 hash
    const hashBuffer = await crypto.subtle.digest('SHA-256', publicKeyBytes);
    const hashArray = new Uint8Array(hashBuffer);
    
    // Convert to hex string and format as fingerprint
    const hexString = Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    // Format as groups of 4 characters separated by spaces
    return hexString.match(/.{1,4}/g)?.join(' ').toUpperCase() || hexString.toUpperCase();
  } catch (error) {
    verifyLogger.error('Failed to generate key fingerprint', error);
    throw new Error('Failed to generate key fingerprint');
  }
}

/**
 * Generate a short fingerprint for quick verification
 */
export async function generateShortFingerprint(publicKeyBase64: string): Promise<string> {
  try {
    const publicKeyBytes = naclUtil.decodeBase64(publicKeyBase64);
    
    const hashBuffer = await crypto.subtle.digest('SHA-256', publicKeyBytes);
    const hashArray = new Uint8Array(hashBuffer);
    
    // Take first 8 bytes and format as hex
    const shortHex = Array.from(hashArray.slice(0, 8))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    // Format as groups of 4
    return shortHex.match(/.{1,4}/g)?.join(' ').toUpperCase() || shortHex.toUpperCase();
  } catch (error) {
    verifyLogger.error('Failed to generate short fingerprint', error);
    throw new Error('Failed to generate short fingerprint');
  }
}

/**
 * Generate emoji representation of key for easy visual verification
 */
export function generateEmojiFingerprint(publicKeyBase64: string): string {
  try {
    const publicKeyBytes = naclUtil.decodeBase64(publicKeyBase64);
    
    // Use a deterministic set of emojis for consistency
    const emojiSet = [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼',
      '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🐔',
      '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉',
      '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋',
      '🌸', '🌺', '🌻', '🌹', '🌷', '🌼', '🌿', '🍀',
      '🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍑',
      '⭐', '🌟', '✨', '🌙', '☀️', '🌈', '⚡', '🔥',
      '❄️', '🌊', '🍯', '🎵', '🎨', '🎭', '🎪', '🎯'
    ];
    
    // Generate deterministic emoji sequence from key
    let emojiFingerprint = '';
    for (let i = 0; i < 8; i++) {
      const index = publicKeyBytes[i * 4] % emojiSet.length;
      emojiFingerprint += emojiSet[index];
    }
    
    return emojiFingerprint;
  } catch (error) {
    verifyLogger.error('Failed to generate emoji fingerprint', error);
    return '🔑🔒🛡️🔐'; // Fallback emoji
  }
}

/**
 * Generate word-based fingerprint for voice verification
 */
export function generateWordFingerprint(publicKeyBase64: string): string {
  try {
    const publicKeyBytes = naclUtil.decodeBase64(publicKeyBase64);
    
    // NATO phonetic alphabet + common words for clarity
    const wordList = [
      'Alpha', 'Bravo', 'Charlie', 'Delta', 'Echo', 'Foxtrot', 'Golf', 'Hotel',
      'India', 'Juliet', 'Kilo', 'Lima', 'Mike', 'November', 'Oscar', 'Papa',
      'Quebec', 'Romeo', 'Sierra', 'Tango', 'Uniform', 'Victor', 'Whiskey', 'Xray',
      'Yankee', 'Zulu', 'Apple', 'Banana', 'Cherry', 'Dragon', 'Eagle', 'Forest',
      'Guitar', 'Honey', 'Island', 'Jungle', 'Kitten', 'Lemon', 'Mountain', 'Neptune',
      'Ocean', 'Panda', 'Queen', 'River', 'Sunset', 'Tiger', 'Universe', 'Volcano',
      'Wizard', 'Oxygen', 'Yellow', 'Zebra'
    ];
    
    // Generate 6 words from the key
    const words = [];
    for (let i = 0; i < 6; i++) {
      const index = publicKeyBytes[i * 5] % wordList.length;
      words.push(wordList[index]);
    }
    
    return words.join(' ');
  } catch (error) {
    verifyLogger.error('Failed to generate word fingerprint', error);
    return 'Security Key Protection Shield';
  }
}

/**
 * Create complete fingerprint information for a user's key
 */
export async function createKeyFingerprint(
  publicKeyBase64: string, 
  userId: number, 
  username: string
): Promise<KeyFingerprint> {
  try {
    const [fingerprint, shortFingerprint] = await Promise.all([
      generateKeyFingerprint(publicKeyBase64),
      generateShortFingerprint(publicKeyBase64)
    ]);

    const emoji = generateEmojiFingerprint(publicKeyBase64);
    const words = generateWordFingerprint(publicKeyBase64);
    
    // Check if this key is already verified
    const verificationStatus = getKeyVerificationStatus(userId, fingerprint);
    
    return {
      publicKey: publicKeyBase64,
      fingerprint,
      shortFingerprint,
      emoji,
      words,
      userId,
      username,
      verificationStatus: verificationStatus.status,
      verifiedAt: verificationStatus.verifiedAt,
      verificationMethod: verificationStatus.method
    };
  } catch (error) {
    verifyLogger.error('Failed to create key fingerprint', error);
    throw error;
  }
}

/**
 * Get verification status for a key
 */
function getKeyVerificationStatus(userId: number, fingerprint: string): {
  status: 'unverified' | 'pending' | 'verified' | 'warning';
  verifiedAt?: Date;
  method?: string;
} {
  try {
    const stored = localStorage.getItem(`key_verification_${userId}`);
    if (!stored) {
      return { status: 'unverified' };
    }
    
    const verification: VerificationRecord = JSON.parse(stored);
    
    // Check if the stored fingerprint matches current one
    if (verification.keyFingerprint !== fingerprint) {
      verifyLogger.warn(`Key fingerprint mismatch for user ${userId}`);
      return { status: 'warning' }; // Key has changed!
    }
    
    return {
      status: 'verified',
      verifiedAt: new Date(verification.verifiedAt),
      method: verification.verificationMethod as 'manual' | 'qr-code' | 'voice-call' | 'in-person'
    };
  } catch (error) {
    verifyLogger.error('Failed to get verification status', error);
    return { status: 'unverified' };
  }
}

/**
 * Mark a key as verified
 */
export function verifyKey(
  userId: number, 
  fingerprint: string, 
  method: string, 
  notes?: string
): void {
  try {
    const verification: VerificationRecord = {
      userId,
      keyFingerprint: fingerprint,
      verifiedAt: new Date(),
      verificationMethod: method,
      notes
    };
    
    localStorage.setItem(`key_verification_${userId}`, JSON.stringify(verification));
    verifyLogger.info(`Key verified for user ${userId} via ${method}`);
  } catch (error) {
    verifyLogger.error('Failed to store key verification', error);
    throw error;
  }
}

/**
 * Remove verification for a key (if user reports it as wrong)
 */
export function unverifyKey(userId: number): void {
  try {
    localStorage.removeItem(`key_verification_${userId}`);
    verifyLogger.info(`Key verification removed for user ${userId}`);
  } catch (error) {
    verifyLogger.error('Failed to remove key verification', error);
  }
}

/**
 * Generate QR code data for key verification
 */
export function generateVerificationQRData(fingerprint: KeyFingerprint): string {
  const qrData = {
    type: 'key_verification',
    userId: fingerprint.userId,
    username: fingerprint.username,
    fingerprint: fingerprint.shortFingerprint,
    emoji: fingerprint.emoji,
    timestamp: Date.now()
  };
  
  return JSON.stringify(qrData);
}

/**
 * Parse QR code data for verification
 */
export function parseVerificationQRData(qrData: string): {
  userId: number;
  username: string;
  fingerprint: string;
  emoji: string;
  timestamp: number;
} | null {
  try {
    const data = JSON.parse(qrData);
    if (data.type !== 'key_verification') {
      return null;
    }
    
    return {
      userId: data.userId,
      username: data.username,
      fingerprint: data.fingerprint,
      emoji: data.emoji,
      timestamp: data.timestamp
    };
  } catch (error) {
    verifyLogger.error('Failed to parse verification QR data', error);
    return null;
  }
}

/**
 * Compare two fingerprints for verification
 */
export function compareFingerprintsSafely(
  fingerprint1: string, 
  fingerprint2: string
): boolean {
  // Normalize both fingerprints (remove spaces, convert to uppercase)
  const normalize = (fp: string) => fp.replace(/\s/g, '').toUpperCase();
  
  const fp1 = normalize(fingerprint1);
  const fp2 = normalize(fingerprint2);
  
  return fp1 === fp2;
}