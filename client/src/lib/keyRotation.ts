import nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';
import { clientLogger } from './logger';
import { SecureKeyManager } from './secureKeyManager';
import { sendPublicKeyToServer } from './encryption';

/**
 * Key Rotation and Forward Secrecy Management
 * Provides periodic key rotation and maintains old keys for decryption
 */

const rotationLogger = {
  debug: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      clientLogger.log(`[KeyRotation] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    clientLogger.log(`[KeyRotation] ${message}`, data);
  },
  warn: (message: string, error?: any) => {
    console.warn(`[KeyRotation Warning] ${message}`, error);
  },
  error: (message: string, error?: any) => {
    clientLogger.error(`[KeyRotation] ${message}`, error);
  }
};

interface KeyRotationSchedule {
  currentKeyId: string;
  nextRotationDate: Date;
  rotationIntervalDays: number;
  gracePeriodDays: number;
  lastRotationDate: Date;
}

interface HistoricalKey {
  keyId: string;
  publicKey: string;
  secretKey: string; // Encrypted with current password
  createdAt: Date;
  retiredAt?: Date;
  isActive: boolean;
}

interface KeyRotationHistory {
  keys: HistoricalKey[];
  currentKeyId: string;
  schedule: KeyRotationSchedule;
}

class KeyRotationManager {
  private static instance: KeyRotationManager;
  private keyManager: SecureKeyManager;
  
  private constructor() {
    this.keyManager = SecureKeyManager.getInstance();
  }

  public static getInstance(): KeyRotationManager {
    if (!KeyRotationManager.instance) {
      KeyRotationManager.instance = new KeyRotationManager();
    }
    return KeyRotationManager.instance;
  }

  /**
   * Initialize key rotation with schedule
   */
  public async initializeRotationSchedule(
    rotationIntervalDays: number = 90, // Default: rotate every 3 months
    gracePeriodDays: number = 30       // Keep old keys for 30 days
  ): Promise<void> {
    try {
      const currentKeyId = this.keyManager.getCurrentKeyId();
      if (!currentKeyId) {
        throw new Error('No active encryption keys found. Please initialize encryption first.');
      }

      const schedule: KeyRotationSchedule = {
        currentKeyId,
        nextRotationDate: new Date(Date.now() + rotationIntervalDays * 24 * 60 * 60 * 1000),
        rotationIntervalDays,
        gracePeriodDays,
        lastRotationDate: new Date()
      };

      this.storeRotationSchedule(schedule);
      rotationLogger.info('Key rotation schedule initialized', { rotationIntervalDays, gracePeriodDays });
    } catch (error) {
      rotationLogger.error('Failed to initialize rotation schedule', error);
      throw error;
    }
  }

  /**
   * Check if keys need rotation
   */
  public shouldRotateKeys(): boolean {
    try {
      const schedule = this.getRotationSchedule();
      if (!schedule) {
        return false; // No schedule set up
      }

      return new Date() >= schedule.nextRotationDate;
    } catch (error) {
      rotationLogger.error('Failed to check rotation schedule', error);
      return false;
    }
  }

  /**
   * Get days until next rotation
   */
  public getDaysUntilRotation(): number {
    try {
      const schedule = this.getRotationSchedule();
      if (!schedule) {
        return -1; // No schedule
      }

      const msUntilRotation = schedule.nextRotationDate.getTime() - Date.now();
      return Math.ceil(msUntilRotation / (24 * 60 * 60 * 1000));
    } catch (error) {
      rotationLogger.error('Failed to calculate days until rotation', error);
      return -1;
    }
  }

  /**
   * Perform key rotation
   */
  public async rotateKeys(password: string): Promise<{ publicKey: string; keyId: string }> {
    try {
      rotationLogger.info('Starting key rotation process');

      // Get current key information before rotation
      const currentKeyId = this.keyManager.getCurrentKeyId();
      const currentPublicKey = this.keyManager.getPublicKey();
      const currentSecretKey = this.keyManager.getSecretKey();

      if (!currentKeyId || !currentPublicKey || !currentSecretKey) {
        throw new Error('No active keys found for rotation');
      }

      // Archive current key
      await this.archiveCurrentKey(currentKeyId, currentPublicKey, currentSecretKey);

      // Generate new key pair with same password
      const newKeyResult = await this.keyManager.initializeWithPassword(password);

      // Update rotation schedule
      const schedule = this.getRotationSchedule();
      if (schedule) {
        schedule.currentKeyId = newKeyResult.keyId;
        schedule.lastRotationDate = new Date();
        schedule.nextRotationDate = new Date(Date.now() + schedule.rotationIntervalDays * 24 * 60 * 60 * 1000);
        this.storeRotationSchedule(schedule);
      }

      // Send new public key to server
      await sendPublicKeyToServer(newKeyResult.publicKey);

      // Clean up old keys beyond grace period
      await this.cleanupExpiredKeys();

      rotationLogger.info('Key rotation completed successfully', { newKeyId: newKeyResult.keyId });
      
      return newKeyResult;
    } catch (error) {
      rotationLogger.error('Key rotation failed', error);
      throw error;
    }
  }

  /**
   * Archive current key for future decryption
   */
  private async archiveCurrentKey(keyId: string, publicKey: string, secretKey: string): Promise<void> {
    try {
      const history = this.getKeyHistory();
      
      const historicalKey: HistoricalKey = {
        keyId,
        publicKey,
        secretKey, // Already encrypted by SecureKeyManager
        createdAt: new Date(),
        isActive: false
      };

      history.keys.push(historicalKey);
      this.storeKeyHistory(history);

      rotationLogger.debug('Current key archived', { keyId });
    } catch (error) {
      rotationLogger.error('Failed to archive current key', error);
      throw error;
    }
  }

  /**
   * Get all available keys for decryption (current + historical)
   */
  public getDecryptionKeys(): { keyId: string; secretKey: string }[] {
    try {
      const keys: { keyId: string; secretKey: string }[] = [];
      
      // Add current key
      const currentKeyId = this.keyManager.getCurrentKeyId();
      const currentSecretKey = this.keyManager.getSecretKey();
      
      if (currentKeyId && currentSecretKey) {
        keys.push({ keyId: currentKeyId, secretKey: currentSecretKey });
      }

      // Add historical keys
      const history = this.getKeyHistory();
      history.keys.forEach(historicalKey => {
        if (!historicalKey.retiredAt) { // Only non-retired keys
          keys.push({
            keyId: historicalKey.keyId,
            secretKey: historicalKey.secretKey
          });
        }
      });

      return keys;
    } catch (error) {
      rotationLogger.error('Failed to get decryption keys', error);
      return [];
    }
  }

  /**
   * Try to decrypt with multiple keys (for backward compatibility)
   */
  public async tryDecryptWithHistoricalKeys(
    encryptedMessage: string,
    nonce: string,
    senderPublicKey: string
  ): Promise<string | null> {
    const decryptionKeys = this.getDecryptionKeys();
    
    for (const keyInfo of decryptionKeys) {
      try {
        // Attempt decryption with this key
        const recipientSecretKey = naclUtil.decodeBase64(keyInfo.secretKey);
        const senderPubKey = naclUtil.decodeBase64(senderPublicKey);
        const encryptedBytes = naclUtil.decodeBase64(encryptedMessage);
        const nonceBytes = naclUtil.decodeBase64(nonce);

        const decryptedBytes = nacl.box.open(
          encryptedBytes,
          nonceBytes,
          senderPubKey,
          recipientSecretKey
        );

        if (decryptedBytes) {
          const decryptedText = naclUtil.encodeUTF8(decryptedBytes);
          rotationLogger.debug(`Successfully decrypted with key ${keyInfo.keyId}`);
          return decryptedText;
        }
      } catch (error) {
        // Try next key
        continue;
      }
    }

    rotationLogger.warn('Failed to decrypt with any available keys');
    return null;
  }

  /**
   * Clean up keys beyond grace period
   */
  private async cleanupExpiredKeys(): Promise<void> {
    try {
      const schedule = this.getRotationSchedule();
      if (!schedule) return;

      const history = this.getKeyHistory();
      const cutoffDate = new Date(Date.now() - schedule.gracePeriodDays * 24 * 60 * 60 * 1000);

      // Mark old keys as retired
      let cleanedCount = 0;
      history.keys.forEach(key => {
        if (key.createdAt < cutoffDate && !key.retiredAt) {
          key.retiredAt = new Date();
          key.isActive = false;
          cleanedCount++;
        }
      });

      if (cleanedCount > 0) {
        this.storeKeyHistory(history);
        rotationLogger.info(`Cleaned up ${cleanedCount} expired keys`);
      }
    } catch (error) {
      rotationLogger.error('Failed to cleanup expired keys', error);
    }
  }

  /**
   * Force key rotation (for security incidents)
   */
  public async forceKeyRotation(password: string, reason: string): Promise<{ publicKey: string; keyId: string }> {
    try {
      rotationLogger.warn(`Force rotating keys due to: ${reason}`);
      
      const result = await this.rotateKeys(password);
      
      // Log security incident
      this.logSecurityIncident('forced_rotation', reason);
      
      return result;
    } catch (error) {
      rotationLogger.error('Forced key rotation failed', error);
      throw error;
    }
  }

  /**
   * Get rotation schedule from storage
   */
  private getRotationSchedule(): KeyRotationSchedule | null {
    try {
      const stored = localStorage.getItem('e2ee_rotation_schedule');
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      return {
        ...parsed,
        nextRotationDate: new Date(parsed.nextRotationDate),
        lastRotationDate: new Date(parsed.lastRotationDate)
      };
    } catch (error) {
      rotationLogger.error('Failed to parse rotation schedule', error);
      return null;
    }
  }

  /**
   * Store rotation schedule
   */
  private storeRotationSchedule(schedule: KeyRotationSchedule): void {
    try {
      localStorage.setItem('e2ee_rotation_schedule', JSON.stringify(schedule));
    } catch (error) {
      rotationLogger.error('Failed to store rotation schedule', error);
    }
  }

  /**
   * Get key history from storage
   */
  private getKeyHistory(): KeyRotationHistory {
    try {
      const stored = localStorage.getItem('e2ee_key_history');
      if (!stored) {
        return {
          keys: [],
          currentKeyId: this.keyManager.getCurrentKeyId() || '',
          schedule: this.getRotationSchedule() || {} as KeyRotationSchedule
        };
      }

      const parsed = JSON.parse(stored);
      // Convert date strings back to Date objects
      parsed.keys.forEach((key: any) => {
        key.createdAt = new Date(key.createdAt);
        if (key.retiredAt) key.retiredAt = new Date(key.retiredAt);
      });

      return parsed;
    } catch (error) {
      rotationLogger.error('Failed to parse key history', error);
      return {
        keys: [],
        currentKeyId: this.keyManager.getCurrentKeyId() || '',
        schedule: this.getRotationSchedule() || {} as KeyRotationSchedule
      };
    }
  }

  /**
   * Store key history
   */
  private storeKeyHistory(history: KeyRotationHistory): void {
    try {
      localStorage.setItem('e2ee_key_history', JSON.stringify(history));
    } catch (error) {
      rotationLogger.error('Failed to store key history', error);
    }
  }

  /**
   * Log security incidents
   */
  private logSecurityIncident(type: string, details: string): void {
    try {
      const incidents = JSON.parse(localStorage.getItem('e2ee_security_incidents') || '[]');
      incidents.push({
        type,
        details,
        timestamp: new Date().toISOString(),
        keyId: this.keyManager.getCurrentKeyId()
      });

      // Keep only last 100 incidents
      if (incidents.length > 100) {
        incidents.splice(0, incidents.length - 100);
      }

      localStorage.setItem('e2ee_security_incidents', JSON.stringify(incidents));
    } catch (error) {
      rotationLogger.error('Failed to log security incident', error);
    }
  }

  /**
   * Get rotation status for UI
   */
  public getRotationStatus(): {
    isScheduled: boolean;
    daysUntilRotation: number;
    lastRotation?: Date;
    totalKeys: number;
    activeKeys: number;
  } {
    const schedule = this.getRotationSchedule();
    const history = this.getKeyHistory();
    
    return {
      isScheduled: !!schedule,
      daysUntilRotation: this.getDaysUntilRotation(),
      lastRotation: schedule?.lastRotationDate,
      totalKeys: history.keys.length + 1, // +1 for current key
      activeKeys: history.keys.filter(k => !k.retiredAt).length + 1
    };
  }
}

export default KeyRotationManager;
export { KeyRotationManager, type KeyRotationSchedule, type HistoricalKey };