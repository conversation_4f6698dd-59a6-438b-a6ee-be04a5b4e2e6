import { useCallback } from 'react';

// Sound effect type definitions
export type SoundEffect = 'message-sent' | 'message-received' | 'error' | 'typing';

// Create audio context lazily to avoid autoplay restrictions
let audioContext: AudioContext | null = null;

const getAudioContext = () => {
  if (!audioContext) {
    audioContext = new AudioContext();
  }
  return audioContext;
};

// Vintage-style sound generation functions
const generateMessageSentTone = async (ctx: AudioContext) => {
  const oscillator = ctx.createOscillator();
  const gainNode = ctx.createGain();

  oscillator.type = 'square';
  oscillator.frequency.setValueAtTime(800, ctx.currentTime);
  oscillator.frequency.exponentialRampToValueAtTime(1200, ctx.currentTime + 0.1);

  gainNode.gain.setValueAtTime(0.3, ctx.currentTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.1);

  oscillator.connect(gainNode);
  gainNode.connect(ctx.destination);

  oscillator.start();
  oscillator.stop(ctx.currentTime + 0.1);
};

const generateMessageReceivedTone = async (ctx: AudioContext) => {
  const oscillator = ctx.createOscillator();
  const gainNode = ctx.createGain();

  oscillator.type = 'sine';
  oscillator.frequency.setValueAtTime(600, ctx.currentTime);
  oscillator.frequency.exponentialRampToValueAtTime(400, ctx.currentTime + 0.15);

  gainNode.gain.setValueAtTime(0.2, ctx.currentTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15);

  oscillator.connect(gainNode);
  gainNode.connect(ctx.destination);

  oscillator.start();
  oscillator.stop(ctx.currentTime + 0.15);
};

const generateErrorTone = async (ctx: AudioContext) => {
  const oscillator = ctx.createOscillator();
  const gainNode = ctx.createGain();

  oscillator.type = 'sawtooth';
  oscillator.frequency.setValueAtTime(200, ctx.currentTime);

  gainNode.gain.setValueAtTime(0.2, ctx.currentTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3);

  oscillator.connect(gainNode);
  gainNode.connect(ctx.destination);

  oscillator.start();
  oscillator.stop(ctx.currentTime + 0.3);
};

const generateTypingTone = async (ctx: AudioContext) => {
  const oscillator = ctx.createOscillator();
  const gainNode = ctx.createGain();

  oscillator.type = 'square';
  oscillator.frequency.setValueAtTime(440, ctx.currentTime);

  gainNode.gain.setValueAtTime(0.1, ctx.currentTime); // Reduced gain for more subtle sound
  gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.05);

  oscillator.connect(gainNode);
  gainNode.connect(ctx.destination);

  oscillator.start();
  oscillator.stop(ctx.currentTime + 0.05); // Shorter duration for better rapid playback
};

// Sound effect player
const playSoundEffect = async (effect: SoundEffect) => {
  try {
    const ctx = getAudioContext();

    switch (effect) {
      case 'message-sent':
        await generateMessageSentTone(ctx);
        break;
      case 'message-received':
        await generateMessageReceivedTone(ctx);
        break;
      case 'error':
        await generateErrorTone(ctx);
        break;
      case 'typing':
        await generateTypingTone(ctx);
        break;
    }
  } catch (error) {
    console.error('Failed to play sound effect:', error);
  }
};

// React hook for using sound effects
export const useSoundEffects = () => {
  const playSound = useCallback((effect: SoundEffect) => {
    void playSoundEffect(effect);
  }, []);

  return { playSound };
};