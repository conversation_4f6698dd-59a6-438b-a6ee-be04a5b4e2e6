import { QueryClient, QueryFunction } from "@tanstack/react-query";

interface ApiRequestOptions<T> {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: T;
}

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = await res.text();
    let message = `${res.status} ${res.statusText}`;
    let errorType = "";
    
    try {
      const json = JSON.parse(text);
      if (json.message) message = json.message;
      else if (json.error) message = json.error;
      
      // Check if this is a content filter error
      if (json.type === "content_filter" || 
          (json.message && (
            json.message.includes("Content Moderation") || 
            json.message.includes("community guidelines") ||
            json.message.includes("inappropriate language")
          ))
      ) {
        errorType = "ContentFilterError";
      }
    } catch (e) {
      // If the response is not JSON, use the raw text
      message = text || message;
    }
    
    // Create an appropriate error
    const error = new Error(message);
    if (errorType) {
      error.name = errorType;
    }
    throw error;
  }
}

export async function apiRequest<T>(
  method: ApiRequestOptions<T>['method'] = 'GET',
  url: string,
  data?: T,
): Promise<Response> {
  // Use relative URLs
  const fullUrl = url.startsWith('/') ? url : `/${url}`;

  try {
    const res = await fetch(fullUrl, {
      method,
      headers: {
        ...(data ? { 'Content-Type': 'application/json' } : {}),
        'Accept': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
      credentials: 'include',
      mode: 'cors',
    });

    // For authentication endpoints, handle 401 specially
    if (res.status === 401 && (url.includes('/login') || url.includes('/register'))) {
      const text = await res.text();
      throw new Error(text || 'Authentication failed');
    }
    
    // Special handling for content filter errors - status 422 usually indicates validation errors
    if (res.status === 422) {
      const text = await res.text();
      try {
        const json = JSON.parse(text);
        // Check if this is a content filter error
        if (json.type === "content_filter" || 
            (json.message && (
              json.message.includes("Content Moderation") || 
              json.message.includes("community guidelines") || 
              json.message.includes("inappropriate language")
            ))
        ) {
          // Return the response without throwing so that the calling code can handle it
          return res;
        }
      } catch (e) {
        // If we can't parse the response, continue with normal error handling
      }
    }

    await throwIfResNotOk(res);
    return res;
  } catch (error) {
    // Clean, concise error logging
    const errorMsg = error instanceof Error ? error.message : String(error);
    console.error(`API ${method} ${url} failed: ${errorMsg}`);
    
    // If this is a content filter error, don't throw an error but return a special response
    if (error instanceof Error && 
        (error.name === 'ContentFilterError' || 
         error.message.includes("Content Moderation") || 
         error.message.includes("inappropriate language") || 
         error.message.includes("community guidelines"))) {
      
      // Instead of throwing an error, return a response with a specific status code
      // that our UI can handle without triggering Vite's error overlay
      const fakeResponse = new Response(JSON.stringify({ 
        success: false,
        error: error.message,
        type: "content_filter"
      }), {
        status: 422, // Use 422 to indicate content validation issue
        headers: {
          'Content-Type': 'application/json',
          'X-Content-Filter-Error': 'true' // Custom header to identify this type of error
        }
      });
      
      return fakeResponse;
    }
    
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const url = queryKey[0] as string;
    const fullUrl = url.startsWith('/') ? url : `/${url}`;

    try {
      const res = await fetch(fullUrl, {
        credentials: "include",
        headers: {
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      if (unauthorizedBehavior === "returnNull" && res.status === 401) {
        return null;
      }

      await throwIfResNotOk(res);
      return await res.json();
    } catch (error) {
      console.error('Query failed:', error);
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: 1,
    },
    mutations: {
      retry: 1,
    },
  },
});