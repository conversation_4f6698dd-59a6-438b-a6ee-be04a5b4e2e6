// Simple client-side logger
export const clientLogger = {
  log: (message: string, data?: any) => {
    console.log(`[AI Chat Client] ${message}`);
    if (data) console.log('[AI Chat Client Data]', data);
  },
  
  warn: (message: string, data?: any) => {
    console.warn(`[AI Chat Client Warning] ${message}`);
    if (data) console.warn('[AI Chat Client Warning Data]', data);
  },
  
  error: (message: string, error: any) => {
    console.error(`[AI Chat Client Error] ${message}`);
    console.error(error);
  }
};
