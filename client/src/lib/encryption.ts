import nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';
import { clientLogger } from './logger';
import { SecureKeyManager } from './secureKeyManager';
import { publicKeyCache } from './encryptionCache';
import { KeyRotationManager } from './keyRotation';

/**
 * End-to-End Encryption utilities for messaging
 * Using TweetNaCl.js (NaCl box) for public-key encryption
 */

// Create a specialized logger just for encryption
const encLogger = {
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      clientLogger.log(`[E2EE] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    clientLogger.log(`[E2EE] ${message}`, data);
  },
  warn: (message: string, error?: any) => {
    console.warn(`[E2EE Warning] ${message}`, error);
  },
  error: (message: string, error?: any) => {
    clientLogger.error(`[E2EE] ${message}`, error);
  }
};

interface KeyPair {
  publicKey: Uint8Array;
  secretKey: Uint8Array;
}

/**
 * Generate a new NaCl key pair for box encryption
 * @returns A key pair with public and secret keys as base64 strings
 */
export function generateKeyPair(): { publicKeyBase64: string; secretKeyBase64: string } {
  const keyPair = nacl.box.keyPair();
  return {
    publicKeyBase64: naclUtil.encodeBase64(keyPair.publicKey),
    secretKeyBase64: naclUtil.encodeBase64(keyPair.secretKey),
  };
}

/**
 * Store the secret key securely using the SecureKeyManager
 * @deprecated Use SecureKeyManager.initializeWithPassword() instead
 */
export function storeSecretKey(secretKeyBase64: string): void {
  // Legacy function - now handled by SecureKeyManager
  encLogger.warn('storeSecretKey is deprecated - use SecureKeyManager instead');
}

/**
 * Retrieve the secret key from secure storage
 * @returns The secret key as a base64 string or null if not found
 */
export function getSecretKey(): string | null {
  const keyManager = SecureKeyManager.getInstance();
  return keyManager.getSecretKey();
}

/**
 * Get the current public key from secure storage
 * @returns The public key as a base64 string or null if not found
 */
export function getPublicKey(): string | null {
  const keyManager = SecureKeyManager.getInstance();
  return keyManager.getPublicKey();
}

/**
 * Check if encryption session is active
 * @returns True if user has active encrypted session
 */
export function isEncryptionActive(): boolean {
  const keyManager = SecureKeyManager.getInstance();
  return keyManager.isSessionActive();
}

/**
 * Initialize secure encryption with password
 * @param password User's password for key derivation
 * @returns Promise resolving to public key and key ID
 */
export async function initializeSecureEncryption(password: string): Promise<{ publicKey: string; keyId: string }> {
  const keyManager = SecureKeyManager.getInstance();
  
  // Check if user has existing keys to recover
  const existingParams = keyManager.getStoredDerivationParams();
  
  try {
    const result = await keyManager.initializeWithPassword(password, existingParams || undefined);
    encLogger.info('Secure encryption initialized successfully');
    return result;
  } catch (error) {
    encLogger.error('Failed to initialize secure encryption', error);
    throw error;
  }
}

/**
 * Clear encryption session (logout)
 */
export function clearEncryptionSession(): void {
  const keyManager = SecureKeyManager.getInstance();
  keyManager.clearSession();
  encLogger.info('Encryption session cleared');
}

/**
 * Check if user has existing encrypted keys
 * @returns True if user has existing keys that can be recovered with password
 */
export function hasExistingEncryptedKeys(): boolean {
  const keyManager = SecureKeyManager.getInstance();
  return keyManager.hasExistingKeys();
}

/**
 * Migrate from legacy localStorage storage to secure storage
 * @param password User's password for secure storage
 * @returns Promise resolving to true if migration was successful
 */
export async function migrateLegacyKeys(password: string): Promise<boolean> {
  const keyManager = SecureKeyManager.getInstance();
  return await keyManager.migrateFromLegacyStorage(password);
}

/**
 * Encrypt a message using the recipient's public key and our secret key
 * @param message The plaintext message to encrypt
 * @param recipientPublicKeyBase64 The recipient's public key as a base64 string
 * @param senderSecretKeyBase64 Our secret key as a base64 string
 * @returns Object containing the encrypted message and nonce as base64 strings, or null if encryption fails
 */
export function encryptMessage(
  message: string,
  recipientPublicKeyBase64: string | null,
  senderSecretKeyBase64: string
): { encryptedMessage: string; nonce: string } | null {
  // Return null if recipient public key is missing
  if (!recipientPublicKeyBase64) {
    encLogger.error('Encryption failed: Missing recipient public key');
    return null;
  }

  try {
    // First validate the input format is valid base64
    if (!/^[A-Za-z0-9+/=]+$/.test(recipientPublicKeyBase64)) {
      encLogger.error(`Invalid recipient public key format: not valid base64`);
      // Throw a specific error instead of returning null so caller knows why it failed
      throw new Error('Invalid public key format: not valid base64');
    }
    
    if (!/^[A-Za-z0-9+/=]+$/.test(senderSecretKeyBase64)) {
      encLogger.error(`Invalid sender secret key format: not valid base64`);
      // Throw a specific error instead of returning null
      throw new Error('Invalid secret key format: not valid base64');
    }
    
    try {
      // Convert keys from base64 to Uint8Array
      const recipientPublicKey = naclUtil.decodeBase64(recipientPublicKeyBase64);
      const senderSecretKey = naclUtil.decodeBase64(senderSecretKeyBase64);
      
      // Validate key lengths
      if (recipientPublicKey.length !== nacl.box.publicKeyLength) {
        encLogger.error(`Invalid recipient public key length: got ${recipientPublicKey.length}, expected ${nacl.box.publicKeyLength}`);
        throw new Error(`Invalid public key length: expected ${nacl.box.publicKeyLength} bytes`);
      }
      
      if (senderSecretKey.length !== nacl.box.secretKeyLength) {
        encLogger.error(`Invalid sender secret key length: got ${senderSecretKey.length}, expected ${nacl.box.secretKeyLength}`);
        throw new Error(`Invalid secret key length: expected ${nacl.box.secretKeyLength} bytes`);
      }
    } catch (e) {
      // Convert decoding errors into a more useful message
      if (e instanceof Error && e.message.includes('Invalid character')) {
        encLogger.error(`Base64 decoding failed: ${e.message}`);
        throw new Error('Invalid key encoding: contains invalid base64 characters');
      }
      // Re-throw our own validation errors
      if (e instanceof Error && (e.message.includes('Invalid public key') || e.message.includes('Invalid secret key'))) {
        throw e;
      }
      // For unexpected errors, throw with a generic message
      encLogger.error('Key validation error', e);
      throw new Error('Failed to validate encryption keys');
    }
    
    // Generate a random nonce
    const nonce = nacl.randomBytes(nacl.box.nonceLength);
    
    // Convert the message to Uint8Array
    const messageUint8 = naclUtil.decodeUTF8(message);
    
    // Encrypt the message with error handling
    let encryptedMessageUint8;
    try {
      encLogger.debug('Attempting to encrypt message');
      // Convert back from base64 to get actual keys
      const recipientPublicKeyBytes = naclUtil.decodeBase64(recipientPublicKeyBase64);
      const senderSecretKeyBytes = naclUtil.decodeBase64(senderSecretKeyBase64);
      
      encryptedMessageUint8 = nacl.box(
        messageUint8,
        nonce,
        recipientPublicKeyBytes,
        senderSecretKeyBytes
      );
    } catch (boxError) {
      encLogger.error('NaCl box encryption error', boxError);
      return null;
    }
    
    if (!encryptedMessageUint8) {
      encLogger.error('Encryption failed: nacl.box returned null or undefined');
      return null;
    }
    
    // Convert to base64 for storage/transmission
    const result = {
      encryptedMessage: naclUtil.encodeBase64(encryptedMessageUint8),
      nonce: naclUtil.encodeBase64(nonce)
    };
    
    encLogger.debug('Message encrypted successfully');
    return result;
  } catch (error) {
    // Better error logging
    if (error instanceof Error) {
      encLogger.error(`Encryption failed: ${error.name} - ${error.message}`, error);
    } else {
      encLogger.error('Encryption failed with unknown error', error);
    }
    return null;
  }
}

/**
 * Decrypt a message using the sender's public key and our secret key
 * @param encryptedMessageBase64 The encrypted message as a base64 string
 * @param nonceBase64 The nonce used for encryption as a base64 string
 * @param senderPublicKeyBase64 The sender's public key as a base64 string or null
 * @param recipientSecretKeyBase64 Our secret key as a base64 string
 * @returns The decrypted message as a string, or null if decryption fails
 */
export function decryptMessage(
  encryptedMessageBase64: string,
  nonceBase64: string,
  senderPublicKeyBase64: string | null,
  recipientSecretKeyBase64: string
): string | null {
  // If sender public key is missing, we can't decrypt
  if (!senderPublicKeyBase64) {
    encLogger.error('Decryption failed: Missing sender public key');
    return null;
  }
  
  // First validate the input format is valid base64
  if (!/^[A-Za-z0-9+/=]+$/.test(encryptedMessageBase64)) {
    encLogger.error(`Invalid encrypted message format: not valid base64`);
    return null;
  }
  
  if (!/^[A-Za-z0-9+/=]+$/.test(nonceBase64)) {
    encLogger.error(`Invalid nonce format: not valid base64`);
    return null;
  }
  
  if (!/^[A-Za-z0-9+/=]+$/.test(senderPublicKeyBase64)) {
    encLogger.error(`Invalid sender public key format: not valid base64`);
    // Return null but log a detailed warning for diagnostic purposes
    encLogger.warn(`Public key format validation failed`, {
      publicKeyFormat: senderPublicKeyBase64.substring(0, 10) + '...',
      validationPattern: '^[A-Za-z0-9+/=]+$'
    });
    return null;
  }
  
  if (!/^[A-Za-z0-9+/=]+$/.test(recipientSecretKeyBase64)) {
    encLogger.error(`Invalid recipient secret key format: not valid base64`);
    return null;
  }
  
  try {
    // Try to convert from base64 to Uint8Array with explicit error handling
    let encryptedMessage, nonce, senderPublicKey, recipientSecretKey;
    
    try {
      encryptedMessage = naclUtil.decodeBase64(encryptedMessageBase64);
    } catch (e) {
      encLogger.error(`Failed to decode encrypted message from base64`, e);
      return null;
    }
    
    try {
      nonce = naclUtil.decodeBase64(nonceBase64);
    } catch (e) {
      encLogger.error(`Failed to decode nonce from base64`, e);
      return null;
    }
    
    try {
      senderPublicKey = naclUtil.decodeBase64(senderPublicKeyBase64);
    } catch (e) {
      encLogger.error(`Failed to decode sender public key from base64`, e);
      return null;
    }
    
    try {
      recipientSecretKey = naclUtil.decodeBase64(recipientSecretKeyBase64);
    } catch (e) {
      encLogger.error(`Failed to decode recipient secret key from base64`, e);
      return null;
    }
    
    // Validate the inputs have correct lengths before attempting to decrypt
    if (nonce.length !== nacl.box.nonceLength) {
      encLogger.error(`Invalid nonce length: got ${nonce.length}, expected ${nacl.box.nonceLength}`);
      return null;
    }
    
    if (senderPublicKey.length !== nacl.box.publicKeyLength) {
      encLogger.error(`Invalid sender public key length: got ${senderPublicKey.length}, expected ${nacl.box.publicKeyLength}`);
      return null;
    }
    
    if (recipientSecretKey.length !== nacl.box.secretKeyLength) {
      encLogger.error(`Invalid recipient secret key length: got ${recipientSecretKey.length}, expected ${nacl.box.secretKeyLength}`);
      return null;
    }
    
    // Decrypt the message with error handling
    let decryptedMessageUint8;
    try {
      encLogger.debug('Attempting to decrypt message');
      decryptedMessageUint8 = nacl.box.open(
        encryptedMessage,
        nonce,
        senderPublicKey,
        recipientSecretKey
      );
    } catch (boxError) {
      encLogger.error('NaCl box.open error', boxError);
      return null;
    }
    
    // Check if decryption failed
    if (!decryptedMessageUint8) {
      // Only warn occasionally, not for every failed decryption
      if (Math.random() < 0.1) { // Only log 10% of failures to reduce spam
        encLogger.warn('Decryption returned null with valid inputs - possible key mismatch', {
          messageLength: encryptedMessage.length,
          nonceLength: nonce.length,
          senderKeyLength: senderPublicKey.length,
          recipientKeyLength: recipientSecretKey.length
        });
      }
      return null;
    }
    
    // Convert from Uint8Array to string
    const decryptedText = naclUtil.encodeUTF8(decryptedMessageUint8);
    encLogger.debug('Message decrypted successfully');
    return decryptedText;
  } catch (error) {
    // More detailed error logging
    if (error instanceof Error) {
      encLogger.error(`Error decrypting message: ${error.name} - ${error.message}`, error);
    } else {
      encLogger.error('Unknown error decrypting message', error);
    }
    return null;
  }
}

/**
 * Send the public key to the server
 * @param publicKeyBase64 The public key as a base64 string
 * @returns Promise that resolves when the public key is updated
 */
export async function sendPublicKeyToServer(publicKeyBase64: string): Promise<void> {
  const response = await fetch('/api/user/public-key', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ publicKey: publicKeyBase64 }),
  });
  
  if (!response.ok) {
    throw new Error('Failed to update public key on server');
  }
}

/**
 * Fetch a user's public key from the server with improved error handling and debug information
 * @param userId The ID of the user
 * @returns Promise that resolves to the user's public key as a base64 string or null if not found
 * @throws Error if there's a network issue or server error that should be retried
 */
export async function fetchUserPublicKey(userId: number): Promise<string | null> {
  if (!userId || userId <= 0) {
    encLogger.error(`Invalid user ID for public key fetch: ${userId}`);
    return null;
  }

  // Use AbortController to implement timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout (increased from 5s)

  let response = null;

  try {
    encLogger.debug(`Attempting to fetch public key for user ${userId}`);
    
    response = await fetch(`/api/users/${userId}/public-key`, {
      signal: controller.signal,
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      // Add more reliable retry logic with exponential backoff
      // This is handled at the application level in getUserPublicKey
    });
    
    // Clear the timeout as soon as we get a response
    clearTimeout(timeoutId);
    
    if (response.status === 404) {
      // User doesn't have a public key yet - this is an expected condition, not an error
      encLogger.info(`User ${userId} doesn't have a public key`);
      return null;
    }
    
    if (response.status >= 500) {
      // Server error - throw an error that can be caught and retried
      throw new Error(`Server error (${response.status}) when fetching public key for user ${userId}`);
    }
    
    if (!response.ok) {
      // Other HTTP errors - log but don't retry
      encLogger.error(`Error fetching public key for user ${userId}: ${response.status} ${response.statusText}`);
      return null;
    }
    
    const data = await response.json();
    
    if (!data.publicKey) {
      encLogger.warn(`Server returned successful response but no public key for user ${userId}`);
      return null;
    }
    
    encLogger.debug(`Successfully retrieved public key for user ${userId}`);
    return data.publicKey;
  } catch (error) {
    // Ensure timeout is cleared
    clearTimeout(timeoutId);
    
    if (error instanceof Error && error.name === 'AbortError') {
      encLogger.error(`Request timeout (10s) fetching public key for user ${userId}`);
      // Rethrow as a different error type that can be identified for retries
      throw new Error(`Timeout fetching public key for user ${userId}`);
    } else if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      // Network error - likely offline or server down
      encLogger.error(`Network error fetching public key for user ${userId}`, error);
      throw error; // Rethrow for retry
    } else {
      // Log the error with additional response details if available
      const statusInfo = response ? ` (HTTP ${response.status})` : '';
      encLogger.error(`Exception fetching public key for user ${userId}${statusInfo}`, error);
      
      // Rethrow server errors (already identified above) and network errors
      // but return null for other cases
      if (error instanceof Error && 
          (error.message.includes('Server error') || error.message.includes('Network error'))) {
        throw error; // Rethrow for retry
      }
      return null;
    }
  }
}

/**
 * Initialize E2EE for the current user (Legacy function)
 * @deprecated Use initializeSecureEncryption() with password instead
 * @returns The public key as a base64 string
 */
export async function initializeE2EE(): Promise<string> {
  encLogger.warn('initializeE2EE is deprecated - use initializeSecureEncryption() instead');
  
  // Check if we have an active secure session
  if (isEncryptionActive()) {
    const publicKey = getPublicKey();
    if (publicKey) {
      return publicKey;
    }
  }
  
  // Check for legacy localStorage key that needs migration
  const legacyKey = localStorage.getItem('e2ee_secret_key');
  if (legacyKey) {
    encLogger.warn('Found legacy localStorage key - migration required');
    throw new Error('Legacy encryption keys found. Please use secure authentication to migrate your keys.');
  }
  
  // No keys available - user needs to initialize with password
  throw new Error('No encryption keys available. Please initialize secure encryption with your password.');
}

// Add a typeguard for checking if an object has the expected decrypt properties
export function isValidEncryptedMessage(obj: any): obj is { encryptedMessage: string; nonce: string } {
  return obj && 
         typeof obj === 'object' && 
         typeof obj.encryptedMessage === 'string' && 
         typeof obj.nonce === 'string';
}

/**
 * Safely attempt to encrypt a message, with fallback for unencrypted
 * @param content The message content to encrypt
 * @param recipientPublicKey The recipient's public key
 * @param senderSecretKey The sender's secret key
 * @returns Object with the message content, nonce, and isEncrypted flag
 */
export function safeEncrypt(
  content: string,
  recipientPublicKey: string | null,
  senderSecretKey: string | null
): { content: string; nonce: string; isEncrypted: boolean } {
  // If content is empty, don't bother encrypting
  if (!content || content.trim() === '') {
    encLogger.debug('Empty content, skipping encryption');
    return {
      content,
      nonce: '',
      isEncrypted: false
    };
  }
  
  // If either key is missing, return unencrypted content with reason
  if (!recipientPublicKey) {
    encLogger.info('Encryption skipped: Missing recipient public key');
    return {
      content,
      nonce: '',
      isEncrypted: false
    };
  }
  
  if (!senderSecretKey) {
    encLogger.info('Encryption skipped: Missing sender secret key');
    return {
      content,
      nonce: '',
      isEncrypted: false
    };
  }
  
  try {
    // Attempt encryption
    encLogger.debug('Attempting to encrypt message');
    const encrypted = encryptMessage(content, recipientPublicKey, senderSecretKey);
    
    if (encrypted) {
      // Encryption successful
      encLogger.debug('Message encryption successful');
      return {
        content: encrypted.encryptedMessage,
        nonce: encrypted.nonce,
        isEncrypted: true
      };
    } else {
      // encryptMessage returned null despite having valid keys
      encLogger.error('Encryption failed in encryptMessage despite valid keys');
    }
  } catch (error) {
    // Log detailed error info
    if (error instanceof Error) {
      encLogger.error(`Encryption error: ${error.name} - ${error.message}`, error);
    } else {
      encLogger.error('Encryption failed with unknown error', error);
    }
  }
  
  // Fallback to unencrypted if encryption fails
  encLogger.warn('Falling back to unencrypted message due to encryption failure');
  return {
    content,
    nonce: '',
    isEncrypted: false
  };
}

/**
 * Safely attempt to decrypt a message, with fallback for when decryption fails
 * @param message A message object containing content, nonce, and isEncrypted flag
 * @param senderPublicKey The sender's public key
 * @param recipientSecretKey The recipient's secret key
 * @returns The decrypted message content or an error message
 */
export function safeDecrypt(
  message: { content: string; nonce?: string; isEncrypted?: boolean },
  senderPublicKey: string | null,
  recipientSecretKey: string | null
): string {
  // Validate message object
  if (!message || typeof message.content !== 'string') {
    encLogger.error('Invalid message object provided to safeDecrypt');
    return "[INVALID MESSAGE FORMAT]";
  }
  
  // If message is not encrypted, return as is
  if (!message.isEncrypted || !message.nonce) {
    encLogger.debug('Message is not encrypted, returning as-is');
    return message.content;
  }
  
  // If sender public key is missing, return specific error
  if (!senderPublicKey) {
    encLogger.warn('Cannot decrypt message: Missing sender public key');
    return "🔐 [Message is encrypted. Sender key unavailable.]";
  }
  
  // If recipient secret key is missing, check if we need to initialize secure encryption
  if (!recipientSecretKey) {
    // Check if there's a legacy key that needs migration
    const legacyKey = localStorage.getItem('e2ee_secret_key');
    if (legacyKey) {
      encLogger.info('Legacy encryption key found - migration needed for decryption');
      return "🔐 [Legacy encryption detected. Please set up secure encryption to access your messages.]";
    }
    
    // Check if secure encryption is available but session is inactive
    if (hasExistingEncryptedKeys()) {
      encLogger.info('Secure encryption keys available but session inactive');
      return "🔐 [Encryption session inactive. Please unlock your encryption to view this message.]";
    }
    
    encLogger.warn('Cannot decrypt message: No encryption keys available');
    return "🔐 [No encryption keys available. Please set up encryption to view secure messages.]";
  }
  
  try {
    // Validate keys and message format silently to avoid flooding console
    try {
      // Validate message content and nonce first
      if (!message.content || !message.nonce) {
        encLogger.warn('Encrypted message missing required components');
        return "🔐 [Encrypted message missing required components]";
      }
      
      // Check if message content and nonce are valid base64
      try {
        naclUtil.decodeBase64(message.content);
        naclUtil.decodeBase64(message.nonce);
      } catch (formatError) {
        encLogger.warn('Message content or nonce is not valid base64', formatError);
        return "🔐 [Message format error]";
      }
      
      // Check if keys are valid base64
      try {
        naclUtil.decodeBase64(senderPublicKey);
        naclUtil.decodeBase64(recipientSecretKey);
      } catch (keyError) {
        encLogger.warn('Public or private key is not valid base64', keyError);
        return "🔐 [Encryption key format error]";
      }
    } catch (validationError) {
      encLogger.error('Validation error before decryption', validationError);
      return "🔐 [Encryption validation error]";
    }
    
    // Attempt decryption
    encLogger.debug('Validation passed, attempting to decrypt message');
    const decrypted = decryptMessage(
      message.content,
      message.nonce,
      senderPublicKey,
      recipientSecretKey
    );
    
    if (decrypted !== null) {
      encLogger.debug('Message decryption successful');
      return decrypted;
    } else {
      // Primary decryption failed - this might be due to key mismatch or message corruption
      encLogger.warn('Decryption returned null with valid inputs - possible key mismatch', {
        messageLength: message.content.length,
        nonceLength: message.nonce.length,
        senderKeyLength: senderPublicKey.length,
        recipientKeyLength: recipientSecretKey.length
      });
      return "🔐 [Message could not be decrypted - key mismatch or corrupted data]";
    }
  } catch (error) {
    // Log detailed error info
    if (error instanceof Error) {
      encLogger.error(`Decryption error: ${error.name} - ${error.message}`, error);
      
      // Check for common error types and provide more specific messages
      if (error.message.includes('bad nonce size') || error.message.includes('incorrect length')) {
        return "🔐 [Message format error]";
      } else if (error.message.includes('invalid encoding')) {
        return "🔐 [Invalid data encoding]";
      } else if (error.message.includes('secret key length')) {
        return "🔐 [Invalid key format]";
      }
    } else {
      encLogger.error('Decryption failed with unknown error', error);
    }
    
    // Track decryption failures more explicitly for debugging
    try {
      const diagnostics = {
        messagePresent: !!message,
        contentPresent: !!message.content,
        noncePresent: !!message.nonce,
        senderKeyPresent: !!senderPublicKey,
        recipientKeyPresent: !!recipientSecretKey,
        contentLength: message.content.length,
        nonceLength: message.nonce?.length || 0,
        errorType: error instanceof Error ? error.name : typeof error,
        timestamp: new Date().toISOString()
      };
      encLogger.error('Decryption diagnostics', diagnostics);
    } catch (diagError) {
      // Don't let diagnostic logging cause additional errors
      encLogger.warn('Failed to log decryption diagnostics', diagError);
    }
  }
  
  // Generic fallback with a more user-friendly message
  return "🔐 [Encrypted message - unable to decrypt]";
}