
import { useState, useCallback } from 'react';
import { ContentFilterDialog } from '../components/ui/content-filter-dialog';

export function useContentFilter() {
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);
  const [filterMessage, setFilterMessage] = useState('');

  const handleContentFilterError = useCallback((error: any) => {
    // Extract the message from the error
    let message = "Your submission contains language that violates our community guidelines.";
    
    if (error.message) {
      message = error.message;
    } else if (typeof error === 'object' && error.error) {
      message = error.error;
    }
    
    // Show the filter dialog
    setFilterMessage(message);
    setIsFilterDialogOpen(true);
    
    // Return true to indicate we handled the error
    return true;
  }, []);

  const closeFilterDialog = useCallback(() => {
    setIsFilterDialogOpen(false);
  }, []);

  // A component to render the dialog
  const ContentFilterDialogComponent = useCallback(() => (
    <ContentFilterDialog 
      isOpen={isFilterDialogOpen}
      onClose={closeFilterDialog}
      message={filterMessage}
    />
  ), [isFilterDialogOpen, closeFilterDialog, filterMessage]);

  return {
    handleContentFilterError,
    closeFilterDialog,
    ContentFilterDialogComponent,
    isFilterDialogOpen
  };
}
