import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import ForumPage from "@/pages/forum-page";
import AuthPage from "@/pages/auth-page";
import MessagesPage from "@/pages/messages-page";
import ProfilePage from "@/pages/profile-page";
import ThreadPage from "@/pages/thread-page";
import ChatPage from "@/pages/chat";
import NewThreadPage from "@/pages/new-thread";
import AdminPage from "@/pages/admin-page";
import AdminUserDetailsPage from "@/pages/admin-user-details";
import { AuthProvider } from "./hooks/use-auth";
import { ProtectedRoute } from "./lib/protected-route";
import { BootSequence } from "@/components/loading/boot-sequence";
import { applyTerminalTheme } from "@/components/ui/terminal-theme-switcher";
import "./styles/crt.css";
import AIPage from "@/pages/ai-page";
import NewsPage from "@/pages/news-page";
import PublicProfilePage from "@/pages/public-profile-page";
import FriendsPage from "@/pages/friends-page";
import { useEffect, useState, Suspense } from "react";

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="space-y-4 text-center">
        <div className="text-primary font-mono animate-pulse">Loading...</div>
        <div className="w-48 h-2 bg-primary/20 rounded overflow-hidden">
          <div className="w-full h-full bg-primary animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}

function Router() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Switch>
        {/* The root "/" route redirects to /forum after login */}
        <Route path="/" component={AuthPage} />
        <Route path="/auth" component={AuthPage} />
        <ProtectedRoute path="/forum" component={ForumPage} />
        <ProtectedRoute path="/new-thread" component={NewThreadPage} />
        <ProtectedRoute path="/messages" component={MessagesPage} />
        <ProtectedRoute path="/chat" component={ChatPage} />
        <ProtectedRoute path="/news" component={NewsPage} />
        <ProtectedRoute path="/thread/:id" component={ThreadPage} />
        <ProtectedRoute path="/admin" component={AdminPage} />
        <ProtectedRoute path="/admin/users/:id" component={AdminUserDetailsPage} />
        <ProtectedRoute path="/profile" component={ProfilePage} />
        <ProtectedRoute path="/profile/:userId" component={PublicProfilePage} />
        <ProtectedRoute path="/friends" component={FriendsPage} />
        <Route path="/ai">
          <ProtectedRoute path="/ai" component={AIPage} />
        </Route>
        <Route component={NotFound} />
      </Switch>
    </Suspense>
  );
}

function App() {
  const [isBooted, setIsBooted] = useState(() => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem("system_booted") === "true";
    }
    return false;
  });

  useEffect(() => {
    // Load and apply saved theme
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem("terminal-theme") || "amber";
      applyTerminalTheme(savedTheme as "amber" | "green" | "blue");
    }
  }, []);

  const handleBootComplete = () => {
    setIsBooted(true);
    sessionStorage.setItem("system_booted", "true");
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <div className="crt min-h-screen bg-background text-text">
          {!isBooted ? (
            <BootSequence onComplete={handleBootComplete} />
          ) : (
            <Router />
          )}
          <Toaster />
        </div>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;