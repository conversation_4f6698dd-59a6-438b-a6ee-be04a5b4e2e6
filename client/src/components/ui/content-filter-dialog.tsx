import React, { useEffect, useState } from 'react';
import { useSoundEffects } from '@/lib/sounds';
import { clientLogger } from '@/lib/logger';

interface ContentFilterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  message?: string;
}

export function ContentFilterDialog({ 
  isOpen, 
  onClose, 
  message = "Message could not be sent." 
}: ContentFilterDialogProps) {
  const { playSound } = useSoundEffects();
  const [preventPropagation, setPreventPropagation] = useState(false);
  
  useEffect(() => {
    if (isOpen) {
      try {
        // Log the error to help with debugging
        if (typeof clientLogger !== 'undefined' && clientLogger.error) {
          clientLogger.error('Content filter dialog shown', { message });
        } else {
          console.error('Content filter dialog shown', { message });
        }
        
        // Play the error sound effect
        if (typeof playSound === 'function') {
          playSound('error');
        }

        // Set a flag to prevent error propagation to Vite overlay
        setPreventPropagation(true);
        
        // Prevent errors from propagating to window.onerror
        const originalErrorHandler = window.onerror;
        window.onerror = (message, source, lineno, colno, error) => {
          // Check for content filter related errors with broader criteria
          if (preventPropagation && (
              (error instanceof Error && error.name === 'ContentFilterError') ||
              (typeof message === 'string' && (
                message.includes('Content Moderation') ||
                message.includes('community guidelines') ||
                message.includes('inappropriate language')
              ))
            )) {
            // Prevent the error from propagating to Vite's error overlay
            return true;
          }
          return originalErrorHandler ? originalErrorHandler(message, source, lineno, colno, error) : false;
        };
        
        // Also prevent React's error boundary from catching these errors
        const originalConsoleError = console.error;
        // Create a custom property to store the original console.error
        const consoleErrorWithOriginal = (...args: any[]) => {
          const errorText = args.join(' ');
          if (errorText.includes('Content Moderation') || 
              errorText.includes('community guidelines') || 
              errorText.includes('inappropriate language') ||
              errorText.includes('ContentFilterError')) {
            // Don't log to console to prevent React's error boundary
            return;
          }
          originalConsoleError.apply(console, args);
        };
        
        // Store the original console.error so we can restore it later
        (consoleErrorWithOriginal as any).originalConsoleError = originalConsoleError;
        console.error = consoleErrorWithOriginal;
        
        // Auto-close on any key press
        const handleKeyDown = (e: KeyboardEvent) => {
          onClose();
          
          // Prevent the key event from propagating
          e.stopPropagation();
          e.preventDefault();
        };
        
        window.addEventListener('keydown', handleKeyDown);
        
        return () => {
          window.removeEventListener('keydown', handleKeyDown);
          window.onerror = originalErrorHandler;
          console.error = originalConsoleError;
          setPreventPropagation(false);
        };
      } catch (error) {
        // Fail silently - we don't want errors in our error handler
        console.log('Error in content filter dialog effect:', error);
      }
    }
  }, [isOpen, onClose, playSound, message, preventPropagation]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-[100] flex items-center justify-center bg-black/80"
      onClick={(e) => {
        e.stopPropagation();
        onClose();
      }}
    >
      <div 
        className="max-w-md w-full mx-auto flex flex-col items-center text-center space-y-8 p-8 relative bg-black border border-red-600"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="w-full">
          <div className="border-t border-b border-l border-r border-dashed border-red-500 mx-auto max-w-xs py-2">
            <p className="text-red-500 font-mono">[ CONTENT MODERATION ]</p>
          </div>
        </div>
        
        <p className="text-red-400 font-mono text-lg">
          TRANSMISSION BLOCKED: {message}_
        </p>
        
        <div className="text-blue-400 font-mono text-sm">
          Content moderation alert: Your submission contains language 
          that violates our community guidelines. Please revise your 
          content and try again.
        </div>
        
        <div className="w-full">
          <div className="border-t border-b border-l border-r border-dashed border-red-500 mx-auto max-w-xs py-2">
            <p className="text-red-500 font-mono">[ PRESS ANY KEY TO CLOSE ]</p>
          </div>
        </div>
      </div>
    </div>
  );
}