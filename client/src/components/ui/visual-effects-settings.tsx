import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Monitor, Zap } from "lucide-react";

interface VisualEffectsSettings {
  scanlineEnabled: boolean;
  scanlineIntensity: number;
  crtEnabled: boolean;
  crtIntensity: number;
  flickerEnabled: boolean;
  glowEnabled: boolean;
}

const DEFAULT_SETTINGS: VisualEffectsSettings = {
  scanlineEnabled: true,
  scanlineIntensity: 25,
  crtEnabled: true,
  crtIntensity: 10,
  flickerEnabled: false,
  glowEnabled: true,
};

export function VisualEffectsSettings() {
  const [settings, setSettings] = useState<VisualEffectsSettings>(DEFAULT_SETTINGS);

  // Load settings from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('visual-effects-settings');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      } catch {
        // If parsing fails, use defaults
      }
    }
  }, []);

  // Apply effects to the document
  useEffect(() => {
    const root = document.documentElement;
    const crtElements = document.querySelectorAll('.crt');
    
    // Update CSS custom properties
    root.style.setProperty('--scanline-opacity', settings.scanlineEnabled ? (settings.scanlineIntensity / 100).toString() : '0');
    root.style.setProperty('--crt-opacity', settings.crtEnabled ? (settings.crtIntensity / 100).toString() : '0');
    root.style.setProperty('--glow-enabled', settings.glowEnabled ? '1' : '0');

    // Handle flicker class toggle
    crtElements.forEach(element => {
      if (settings.flickerEnabled) {
        element.classList.add('flicker-enabled');
      } else {
        element.classList.remove('flicker-enabled');
      }
    });

    // Save to localStorage
    localStorage.setItem('visual-effects-settings', JSON.stringify(settings));
  }, [settings]);

  const updateSetting = <K extends keyof VisualEffectsSettings>(
    key: K,
    value: VisualEffectsSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Monitor className="h-5 w-5" />
          Visual Effects
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Scanlines */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="scanline-toggle" className="text-sm font-medium">
              Scanlines
            </Label>
            <Switch
              id="scanline-toggle"
              checked={settings.scanlineEnabled}
              onCheckedChange={(checked) => updateSetting('scanlineEnabled', checked)}
            />
          </div>
          {settings.scanlineEnabled && (
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">
                Intensity: {settings.scanlineIntensity}%
              </Label>
              <Slider
                value={[settings.scanlineIntensity]}
                onValueChange={(value) => updateSetting('scanlineIntensity', value[0])}
                max={100}
                min={0}
                step={5}
                className="w-full"
              />
            </div>
          )}
        </div>

        {/* CRT Curvature/Distortion */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="crt-toggle" className="text-sm font-medium">
              CRT Effect
            </Label>
            <Switch
              id="crt-toggle"
              checked={settings.crtEnabled}
              onCheckedChange={(checked) => updateSetting('crtEnabled', checked)}
            />
          </div>
          {settings.crtEnabled && (
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">
                Strength: {settings.crtIntensity}%
              </Label>
              <Slider
                value={[settings.crtIntensity]}
                onValueChange={(value) => updateSetting('crtIntensity', value[0])}
                max={50}
                min={0}
                step={5}
                className="w-full"
              />
            </div>
          )}
        </div>

        {/* Screen Flicker */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="flicker-toggle" className="text-sm font-medium flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Screen Flicker
            </Label>
            <p className="text-xs text-muted-foreground">
              Adds subtle screen flicker animation
            </p>
          </div>
          <Switch
            id="flicker-toggle"
            checked={settings.flickerEnabled}
            onCheckedChange={(checked) => updateSetting('flickerEnabled', checked)}
          />
        </div>

        {/* Text Glow */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="glow-toggle" className="text-sm font-medium">
              Text Glow
            </Label>
            <p className="text-xs text-muted-foreground">
              Adds retro glow effect to text
            </p>
          </div>
          <Switch
            id="glow-toggle"
            checked={settings.glowEnabled}
            onCheckedChange={(checked) => updateSetting('glowEnabled', checked)}
          />
        </div>

        {/* Reset Button */}
        <div className="pt-4 border-t">
          <button
            onClick={() => setSettings(DEFAULT_SETTINGS)}
            className="text-xs text-muted-foreground hover:text-foreground transition-colors"
          >
            Reset to defaults
          </button>
        </div>
      </CardContent>
    </Card>
  );
}