import * as React from "react";
import { cn } from "@/lib/utils";
import { useSoundEffects } from "@/lib/sounds";
import { useEffect, useState } from "react";

interface RetroErrorProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  error?: string | null;
  variant?: "default" | "destructive" | "warning";
  onClose?: () => void;
}

export function RetroError({
  title = "ERROR",
  error,
  variant = "default",
  onClose,
  className,
  ...props
}: RetroErrorProps) {
  const [visible, setVisible] = useState(false);
  const [displayText, setDisplayText] = useState("");
  const { playSound } = useSoundEffects();

  useEffect(() => {
    if (error) {
      setVisible(true);
      playSound("error");
      
      // Typewriter effect
      let index = 0;
      const text = error;
      const interval = setInterval(() => {
        if (index <= text.length) {
          setDisplayText(text.slice(0, index));
          playSound("typing");
          index++;
        } else {
          clearInterval(interval);
        }
      }, 50);

      return () => clearInterval(interval);
    }
  }, [error, playSound]);

  if (!visible || !error) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",
        className
      )}
      {...props}
    >
      <div className="relative max-w-lg w-full mx-4">
        <div className="border-text bg-background p-6 shadow-lg">
          {/* ASCII Art Border */}
          <div className="text-primary text-center mb-4">
            +------------------------+
            <br />
            |{" ".repeat(Math.max(0, 24 - title.length) / 2)}
            {title.toUpperCase()}
            {" ".repeat(Math.ceil(Math.max(0, 24 - title.length) / 2))}|
            <br />
            +------------------------+
          </div>

          {/* Error Message with Typewriter Effect */}
          <div className="text-primary font-mono my-4 min-h-[60px] whitespace-pre-wrap">
            {displayText}
            <span className="animate-blink">_</span>
          </div>

          {/* ASCII Art Footer */}
          <div className="text-primary text-center mt-4">
            +------------------------+
            <br />
            | PRESS ANY KEY TO CLOSE |
            <br />
            +------------------------+
          </div>

          {/* Invisible button that covers the whole area */}
          <button
            className="absolute inset-0 w-full h-full opacity-0"
            onClick={() => {
              setVisible(false);
              playSound("typing");
              if (onClose) onClose();
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
