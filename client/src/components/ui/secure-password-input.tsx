import { useState } from 'react';
import { Button } from './button';
import { Input } from './input';
import { Label } from './label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './dialog';
import { Alert, AlertDescription } from './alert';
import { Eye, EyeOff, Shield, Key } from 'lucide-react';

interface SecurePasswordInputProps {
  onPasswordSubmit: (password: string) => Promise<void>;
  title: string;
  description: string;
  isLoading?: boolean;
  error?: string;
  buttonText?: string;
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  children?: React.ReactNode;
}

export function SecurePasswordInput({
  onPasswordSubmit,
  title,
  description,
  isLoading = false,
  error,
  buttonText = "Setup Encryption",
  buttonVariant = "default",
  children
}: SecurePasswordInputProps) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [localError, setLocalError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalError('');

    if (!password) {
      setLocalError('Password is required');
      return;
    }

    if (password.length < 8) {
      setLocalError('Password must be at least 8 characters long');
      return;
    }

    if (confirmPassword && password !== confirmPassword) {
      setLocalError('Passwords do not match');
      return;
    }

    try {
      await onPasswordSubmit(password);
      setIsOpen(false);
      setPassword('');
      setConfirmPassword('');
    } catch (err) {
      setLocalError(err instanceof Error ? err.message : 'Failed to setup encryption');
    }
  };

  const displayError = error || localError;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant={buttonVariant} className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            {buttonText}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription className="text-left">
            {description}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                disabled={isLoading}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {title.toLowerCase().includes('setup') && (
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  disabled={isLoading}
                  className="pr-10"
                />
              </div>
            </div>
          )}

          {displayError && (
            <Alert variant="destructive">
              <AlertDescription>{displayError}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !password}
              className="flex-1"
            >
              {isLoading ? 'Processing...' : 'Continue'}
            </Button>
          </div>
        </form>

        <div className="text-xs text-muted-foreground mt-2">
          <p>• Your password is used to securely encrypt your keys</p>
          <p>• Keys are never stored in plain text</p>
          <p>• Use a strong, memorable password</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}