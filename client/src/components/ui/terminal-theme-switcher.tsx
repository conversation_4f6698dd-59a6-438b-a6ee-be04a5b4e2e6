import * as React from "react";
import { Button } from "@/components/ui/button";
import { useSoundEffects } from "@/lib/sounds";
import { useToast } from "@/hooks/use-toast";

type PhosphorTheme = "amber" | "green" | "blue";

interface ThemeColors {
  background: string;
  foreground: string;
  primary: string;
  border: string;
}

const PHOSPHOR_THEMES: Record<PhosphorTheme, ThemeColors> = {
  amber: {
    background: "0 0% 10%",
    foreground: "32.7 100% 49.6%",
    primary: "32.7 100% 49.6%",
    border: "32.7 100% 49.6%",
  },
  green: {
    background: "0 0% 10%",
    foreground: "120 100% 25%",
    primary: "120 100% 25%",
    border: "120 100% 25%",
  },
  blue: {
    background: "0 0% 10%",
    foreground: "210 100% 50%",
    primary: "210 100% 50%",
    border: "210 100% 50%",
  },
};

// Export the function to apply theme
export const applyTerminalTheme = (theme: PhosphorTheme) => {
  const colors = PHOSPHOR_THEMES[theme];
  const root = document.documentElement;

  // Apply colors to CSS variables
  Object.entries(colors).forEach(([key, value]) => {
    root.style.setProperty(`--${key}`, value);
  });

  // Update all derived colors
  root.style.setProperty("--card", colors.background);
  root.style.setProperty("--card-foreground", colors.foreground);
  root.style.setProperty("--popover", colors.background);
  root.style.setProperty("--popover-foreground", colors.foreground);
  root.style.setProperty("--primary-foreground", colors.background);
  root.style.setProperty("--secondary", colors.background);
  root.style.setProperty("--secondary-foreground", colors.foreground);
  root.style.setProperty("--muted", "0 0% 15%");
  root.style.setProperty("--muted-foreground", colors.foreground);
  root.style.setProperty("--accent", colors.primary);
  root.style.setProperty("--accent-foreground", colors.background);
  root.style.setProperty("--destructive", "0 100% 50%");
  root.style.setProperty("--destructive-foreground", "0 0% 100%");
  root.style.setProperty("--ring", colors.primary);
  root.style.setProperty("--input", colors.primary);
  root.style.setProperty("--text", colors.foreground);

  localStorage.setItem("terminal-theme", theme);
};

export function TerminalThemeSwitcher() {
  const [currentTheme, setCurrentTheme] = React.useState<PhosphorTheme>(() => {
    return (localStorage.getItem("terminal-theme") as PhosphorTheme) || "amber";
  });
  const { playSound } = useSoundEffects();
  const { toast } = useToast();

  const handleThemeChange = React.useCallback((theme: PhosphorTheme) => {
    applyTerminalTheme(theme);
    setCurrentTheme(theme);
    playSound('typing');
    toast({
      title: "Theme Updated",
      description: `Terminal theme switched to ${theme} phosphor`,
    });
  }, [playSound, toast]);

  return (
    <div className="grid gap-4">
      <h3 className="text-lg font-semibold">Terminal Theme</h3>
      <div className="flex gap-2">
        <Button
          variant={currentTheme === "amber" ? "default" : "outline"}
          onClick={() => handleThemeChange("amber")}
          className="w-20"
        >
          Amber
        </Button>
        <Button
          variant={currentTheme === "green" ? "default" : "outline"}
          onClick={() => handleThemeChange("green")}
          className="w-20"
        >
          Green
        </Button>
        <Button
          variant={currentTheme === "blue" ? "default" : "outline"}
          onClick={() => handleThemeChange("blue")}
          className="w-20"
        >
          Blue
        </Button>
      </div>
    </div>
  );
}