import { cn } from "@/lib/utils";

interface AvatarDisplayProps {
  avatar?: string | null;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  fallback?: string;
}

const sizeClasses = {
  sm: "w-6 h-6 text-xs",
  md: "w-8 h-8 text-sm", 
  lg: "w-12 h-12 text-base",
  xl: "w-16 h-16 text-lg"
};

export function AvatarDisplay({ 
  avatar, 
  size = "md", 
  className, 
  fallback = "?" 
}: AvatarDisplayProps) {
  const isImageAvatar = avatar && (avatar.startsWith('data:image/') || avatar.startsWith('http') || avatar.startsWith('/static/'));
  
  return (
    <div className={cn(
      "rounded-full bg-muted border border-primary/20 overflow-hidden flex items-center justify-center",
      sizeClasses[size],
      className
    )}>
      {isImageAvatar ? (
        <img 
          src={avatar} 
          alt="Avatar" 
          className="w-full h-full object-cover"
        />
      ) : (
        <span className="font-mono">
          {avatar || fallback}
        </span>
      )}
    </div>
  );
}