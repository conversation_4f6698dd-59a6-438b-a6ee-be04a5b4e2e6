import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  ShieldCheck, 
  ShieldAlert, 
  ShieldX, 
  Eye, 
  CheckCircle2, 
  AlertTriangle,
  Copy,
  Smartphone,
  MessageCircle,
  Users
} from 'lucide-react';
import { 
  createKey<PERSON>ingerprint, 
  verify<PERSON><PERSON>, 
  unverify<PERSON><PERSON>, 
  generateVerificationQRData,
  compareFingerprintsSafely,
  type KeyFingerprint 
} from '@/lib/keyVerification';
import { fetchUserPublicKey } from '@/lib/encryption';
import { useToast } from '@/hooks/use-toast';

interface KeyVerificationProps {
  userId: number;
  username: string;
  children?: React.ReactNode;
}

export function KeyVerification({ userId, username, children }: KeyVerificationProps) {
  const [keyFingerprint, setKeyFingerprint] = useState<KeyFingerprint | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [verificationMethod, setVerificationMethod] = useState<string>('manual');
  const [verificationNotes, setVerificationNotes] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && !keyFingerprint) {
      loadKeyFingerprint();
    }
  }, [isOpen, userId]);

  const loadKeyFingerprint = async () => {
    setIsLoading(true);
    setError('');

    try {
      const publicKey = await fetchUserPublicKey(userId);
      if (!publicKey) {
        setError('User does not have encryption enabled');
        return;
      }

      const fingerprint = await createKeyFingerprint(publicKey, userId, username);
      setKeyFingerprint(fingerprint);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load key information');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyKey = () => {
    if (!keyFingerprint) return;

    try {
      verifyKey(
        keyFingerprint.userId, 
        keyFingerprint.fingerprint, 
        verificationMethod, 
        verificationNotes
      );

      // Update local state
      setKeyFingerprint({
        ...keyFingerprint,
        verificationStatus: 'verified',
        verifiedAt: new Date(),
        verificationMethod: verificationMethod as any
      });

      toast({
        title: "Key verified",
        description: `${username}'s encryption key has been marked as verified.`,
      });

      setIsOpen(false);
    } catch (err) {
      toast({
        title: "Verification failed",
        description: err instanceof Error ? err.message : 'Failed to verify key',
        variant: "destructive",
      });
    }
  };

  const handleUnverifyKey = () => {
    if (!keyFingerprint) return;

    try {
      unverifyKey(keyFingerprint.userId);

      // Update local state
      setKeyFingerprint({
        ...keyFingerprint,
        verificationStatus: 'unverified',
        verifiedAt: undefined,
        verificationMethod: undefined
      });

      toast({
        title: "Verification removed",
        description: `${username}'s key verification has been removed.`,
      });
    } catch (err) {
      toast({
        title: "Failed to unverify",
        description: err instanceof Error ? err.message : 'Failed to remove verification',
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied",
        description: `${type} copied to clipboard`,
      });
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <ShieldCheck className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <ShieldAlert className="h-4 w-4 text-orange-600" />;
      case 'unverified':
      default:
        return <Shield className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <ShieldCheck className="h-3 w-3 mr-1" />
            Verified
          </Badge>
        );
      case 'warning':
        return (
          <Badge variant="destructive" className="bg-orange-100 text-orange-800">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Key Changed
          </Badge>
        );
      case 'unverified':
      default:
        return (
          <Badge variant="outline" className="text-gray-600">
            <Shield className="h-3 w-3 mr-1" />
            Unverified
          </Badge>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="ghost" size="sm" className="flex items-center gap-1">
            {keyFingerprint ? getStatusIcon(keyFingerprint.verificationStatus) : <Eye className="h-4 w-4" />}
            Verify Key
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Key Verification - {username}
          </DialogTitle>
          <DialogDescription>
            Verify this user's encryption key to ensure secure communication and prevent man-in-the-middle attacks.
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading key information...</p>
            </div>
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {keyFingerprint && (
          <div className="space-y-6">
            {/* Status Card */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Verification Status</CardTitle>
                  {getStatusBadge(keyFingerprint.verificationStatus)}
                </div>
                {keyFingerprint.verificationStatus === 'verified' && keyFingerprint.verifiedAt && (
                  <CardDescription>
                    Verified on {keyFingerprint.verifiedAt.toLocaleDateString()} 
                    {keyFingerprint.verificationMethod && ` via ${keyFingerprint.verificationMethod}`}
                  </CardDescription>
                )}
                {keyFingerprint.verificationStatus === 'warning' && (
                  <CardDescription className="text-orange-700">
                    This user's encryption key has changed since your last verification. 
                    Please re-verify to ensure security.
                  </CardDescription>
                )}
              </CardHeader>
            </Card>

            {/* Key Fingerprints */}
            <Tabs defaultValue="fingerprint" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="fingerprint">Fingerprint</TabsTrigger>
                <TabsTrigger value="emoji">Emoji</TabsTrigger>
                <TabsTrigger value="words">Words</TabsTrigger>
                <TabsTrigger value="short">Short</TabsTrigger>
              </TabsList>

              <TabsContent value="fingerprint" className="space-y-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Full Fingerprint</CardTitle>
                    <CardDescription>
                      Compare this fingerprint with {username} through a secure channel
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-3 bg-muted rounded text-sm font-mono break-all">
                        {keyFingerprint.fingerprint}
                      </code>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(keyFingerprint.fingerprint, 'Fingerprint')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="emoji" className="space-y-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Emoji Fingerprint</CardTitle>
                    <CardDescription>
                      Easy visual verification - compare these emojis with {username}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 p-4 bg-muted rounded text-center text-2xl">
                        {keyFingerprint.emoji}
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(keyFingerprint.emoji, 'Emoji fingerprint')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="words" className="space-y-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Word Fingerprint</CardTitle>
                    <CardDescription>
                      Perfect for voice verification over phone calls
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-3 bg-muted rounded text-sm break-all">
                        {keyFingerprint.words}
                      </code>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(keyFingerprint.words, 'Word fingerprint')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="short" className="space-y-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Short Fingerprint</CardTitle>
                    <CardDescription>
                      Compact version for quick verification
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-3 bg-muted rounded text-lg font-mono text-center">
                        {keyFingerprint.shortFingerprint}
                      </code>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(keyFingerprint.shortFingerprint, 'Short fingerprint')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Verification Actions */}
            {keyFingerprint.verificationStatus !== 'verified' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Verify This Key</CardTitle>
                  <CardDescription>
                    Once you've confirmed the fingerprint with {username}, mark this key as verified
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="method">Verification Method</Label>
                    <Select value={verificationMethod} onValueChange={setVerificationMethod}>
                      <SelectTrigger>
                        <SelectValue placeholder="How did you verify this key?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manual">Manual comparison</SelectItem>
                        <SelectItem value="voice-call">Voice call verification</SelectItem>
                        <SelectItem value="qr-code">QR code scan</SelectItem>
                        <SelectItem value="in-person">In-person verification</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes (optional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Add any notes about this verification..."
                      value={verificationNotes}
                      onChange={(e) => setVerificationNotes(e.target.value)}
                      rows={2}
                    />
                  </div>

                  <Button onClick={handleVerifyKey} className="w-full">
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    Mark as Verified
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Unverify Option */}
            {keyFingerprint.verificationStatus === 'verified' && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Remove Verification</p>
                      <p className="text-xs text-muted-foreground">
                        If you suspect this key is compromised
                      </p>
                    </div>
                    <Button variant="destructive" size="sm" onClick={handleUnverifyKey}>
                      <ShieldX className="h-4 w-4 mr-1" />
                      Unverify
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Verification Instructions */}
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <strong>How to verify:</strong> Contact {username} through a separate, secure channel 
                (phone, in person, etc.) and compare one of the fingerprints above. Only mark as verified 
                if they match exactly.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}