import { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SecurePasswordInput } from '@/components/ui/secure-password-input';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldCheck, Key, AlertTriangle, Unlock } from 'lucide-react';
import { 
  initializeSecureEncryption, 
  clearEncryptionSession, 
  hasExistingEncryptedKeys, 
  isEncryptionActive,
  migrateLegacyKeys,
  sendPublicKeyToServer 
} from '@/lib/encryption';
import { useToast } from '@/hooks/use-toast';

interface EncryptionSetupProps {
  onEncryptionReady?: (publicKey: string) => void;
  showSetupButton?: boolean;
}

export function EncryptionSetup({ onEncryptionReady, showSetupButton = true }: EncryptionSetupProps) {
  const [encryptionStatus, setEncryptionStatus] = useState<'inactive' | 'active' | 'loading'>('inactive');
  const [hasExistingKeys, setHasExistingKeys] = useState(false);
  const [hasLegacyKeys, setHasLegacyKeys] = useState(false);
  const [error, setError] = useState<string>('');
  const { toast } = useToast();

  useEffect(() => {
    checkEncryptionStatus();
  }, []);

  const checkEncryptionStatus = () => {
    const isActive = isEncryptionActive();
    setEncryptionStatus(isActive ? 'active' : 'inactive');
    setHasExistingKeys(hasExistingEncryptedKeys());
    setHasLegacyKeys(!!localStorage.getItem('e2ee_secret_key'));
  };

  const handlePasswordSetup = async (password: string) => {
    setEncryptionStatus('loading');
    setError('');

    try {
      // Check if we need to migrate legacy keys first
      if (hasLegacyKeys) {
        const migrationSuccess = await migrateLegacyKeys(password);
        if (migrationSuccess) {
          toast({
            title: "Keys migrated successfully",
            description: "Your encryption keys have been upgraded to secure storage.",
          });
        }
      }

      // Initialize secure encryption
      const { publicKey, keyId } = await initializeSecureEncryption(password);

      // Send public key to server
      await sendPublicKeyToServer(publicKey);

      setEncryptionStatus('active');
      toast({
        title: "Encryption activated",
        description: "Your messages are now end-to-end encrypted.",
      });

      onEncryptionReady?.(publicKey);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to setup encryption');
      setEncryptionStatus('inactive');
      toast({
        title: "Encryption setup failed",
        description: err instanceof Error ? err.message : 'Please try again',
        variant: "destructive",
      });
    }
  };

  const handlePasswordUnlock = async (password: string) => {
    setEncryptionStatus('loading');
    setError('');

    try {
      const { publicKey } = await initializeSecureEncryption(password);
      
      // Sync public key with server (in case it changed)
      await sendPublicKeyToServer(publicKey);

      setEncryptionStatus('active');
      toast({
        title: "Encryption unlocked",
        description: "Welcome back! Your encrypted messages are now accessible.",
      });

      onEncryptionReady?.(publicKey);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unlock encryption');
      setEncryptionStatus('inactive');
      toast({
        title: "Failed to unlock encryption",
        description: "Please check your password and try again",
        variant: "destructive",
      });
    }
  };

  const handleClearSession = () => {
    clearEncryptionSession();
    setEncryptionStatus('inactive');
    checkEncryptionStatus();
    toast({
      title: "Encryption session cleared",
      description: "You'll need to re-enter your password to access encrypted messages.",
    });
  };

  if (encryptionStatus === 'active') {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-green-800">
            <ShieldCheck className="h-5 w-5" />
            Encryption Active
          </CardTitle>
          <CardDescription className="text-green-700">
            Your messages are end-to-end encrypted and secure.
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Shield className="h-3 w-3 mr-1" />
              Protected
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleClearSession}
              className="text-green-700 border-green-300 hover:bg-green-100"
            >
              Clear Session
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!showSetupButton) {
    return null;
  }

  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-amber-800">
          <Shield className="h-5 w-5" />
          Encryption Setup
        </CardTitle>
        <CardDescription className="text-amber-700">
          Secure your messages with end-to-end encryption.
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        {hasLegacyKeys && (
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              Legacy encryption keys detected. They will be migrated to secure storage when you set up your password.
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          {hasExistingKeys ? (
            <SecurePasswordInput
              onPasswordSubmit={handlePasswordUnlock}
              title="Unlock Encryption"
              description="Enter your password to unlock your encrypted messages and continue secure conversations."
              isLoading={encryptionStatus === 'loading'}
              error={error}
              buttonText="Unlock Encryption"
              buttonVariant="default"
            >
              <Button className="flex items-center gap-2" disabled={encryptionStatus === 'loading'}>
                <Unlock className="h-4 w-4" />
                {encryptionStatus === 'loading' ? 'Unlocking...' : 'Unlock Encryption'}
              </Button>
            </SecurePasswordInput>
          ) : (
            <SecurePasswordInput
              onPasswordSubmit={handlePasswordSetup}
              title="Setup Encryption"
              description="Create a password to secure your encryption keys. This password will be required to access your encrypted messages."
              isLoading={encryptionStatus === 'loading'}
              error={error}
              buttonText="Setup Encryption"
              buttonVariant="default"
            >
              <Button className="flex items-center gap-2" disabled={encryptionStatus === 'loading'}>
                <Key className="h-4 w-4" />
                {encryptionStatus === 'loading' ? 'Setting up...' : 'Setup Encryption'}
              </Button>
            </SecurePasswordInput>
          )}
        </div>

        <div className="text-xs text-amber-700 space-y-1">
          <p>• End-to-end encryption protects your messages from unauthorized access</p>
          <p>• Your password is never stored and cannot be recovered</p>
          <p>• Keep your password safe - losing it means losing access to encrypted messages</p>
        </div>
      </CardContent>
    </Card>
  );
}