import { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldAlert, Key, AlertTriangle, RotateCcw } from 'lucide-react';
import { 
  isEncryptionActive, 
  hasExistingEncryptedKeys, 
  initializeSecureEncryption,
  sendPublicKeyToServer
} from '@/lib/encryption';
import { SecurePasswordInput } from '@/components/ui/secure-password-input';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { useQuery } from '@tanstack/react-query';
import { AutoEncryptionToggle } from '@/components/auto-encryption-toggle';

interface EncryptionStatusProps {
  onEncryptionReady?: () => void;
}

export function EncryptionStatus({ onEncryptionReady }: EncryptionStatusProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  // Get user's encryption status from the new automated system
  const { data: encryptionStatus, isLoading } = useQuery({
    queryKey: ['/api/user/encryption-status'],
    enabled: !!user,
  });

  useEffect(() => {
    // If encryption is enabled and we have the status, call onEncryptionReady
    if ((encryptionStatus as any)?.encryptionEnabled && onEncryptionReady) {
      onEncryptionReady();
    }
  }, [encryptionStatus, onEncryptionReady]);

  if (isLoading) {
    return (
      <Alert className="border-blue-200 bg-blue-50">
        <Shield className="h-4 w-4 text-blue-600 animate-pulse" />
        <AlertDescription className="text-blue-800">
          Checking encryption status...
        </AlertDescription>
      </Alert>
    );
  }

  if ((encryptionStatus as any)?.encryptionEnabled) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <Shield className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span>Auto-encryption is active and secure</span>
              <RotateCcw className="h-3 w-3 text-green-600" />
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Protected
            </Badge>
          </div>
          {(encryptionStatus as any)?.keyInfo && (
            <div className="mt-2 text-xs text-green-700">
              Keys auto-rotate in {(encryptionStatus as any).keyInfo.daysUntilExpiry} days for enhanced security
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  // Show the auto-encryption toggle for users to enable the automated system
  return (
    <div className="space-y-3">
      <Alert className="border-amber-200 bg-amber-50">
        <ShieldAlert className="h-4 w-4 text-amber-600" />
        <AlertDescription className="text-amber-800">
          <div className="flex items-center justify-between">
            <span>Enable auto-encryption for secure messaging</span>
            <Badge variant="outline" className="text-amber-700">
              Disabled
            </Badge>
          </div>
          <div className="mt-2 text-xs text-amber-700">
            Automatic encryption provides server-side key management with no passwords required
          </div>
        </AlertDescription>
      </Alert>
      
      <AutoEncryptionToggle 
        onEncryptionChange={(enabled) => {
          if (enabled && onEncryptionReady) {
            setTimeout(() => onEncryptionReady(), 1000); // Give time for keys to generate
          }
        }}
      />
    </div>
  );
}