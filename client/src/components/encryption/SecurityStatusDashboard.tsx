import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  ShieldCheck, 
  ShieldAlert, 
  Clock, 
  Key, 
  RotateCcw,
  AlertTriangle,
  CheckCircle2,
  Info,
  History,
  RefreshCw
} from 'lucide-react';
import { KeyRotationManager } from '@/lib/keyRotation';
import { isEncryptionActive, getPublicKey, clearEncryptionSession } from '@/lib/encryption';
import { SecurePasswordInput } from '@/components/ui/secure-password-input';
import { useToast } from '@/hooks/use-toast';

export function SecurityStatusDashboard() {
  const [encryptionActive, setEncryptionActive] = useState(false);
  const [rotationStatus, setRotationStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const { toast } = useToast();

  const rotationManager = KeyRotationManager.getInstance();

  useEffect(() => {
    loadSecurityStatus();
  }, []);

  const loadSecurityStatus = async () => {
    try {
      setIsLoading(true);
      setError('');

      const isActive = isEncryptionActive();
      setEncryptionActive(isActive);

      if (isActive) {
        const status = rotationManager.getRotationStatus();
        setRotationStatus(status);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load security status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRotateKeys = async (password: string) => {
    try {
      await rotationManager.rotateKeys(password);
      toast({
        title: "Keys rotated successfully",
        description: "Your encryption keys have been updated for enhanced security.",
      });
      loadSecurityStatus();
    } catch (err) {
      toast({
        title: "Key rotation failed",
        description: err instanceof Error ? err.message : 'Failed to rotate keys',
        variant: "destructive",
      });
    }
  };

  const handleForceRotation = async (password: string) => {
    try {
      await rotationManager.forceKeyRotation(password, 'User requested emergency rotation');
      toast({
        title: "Emergency rotation completed",
        description: "Your keys have been immediately rotated for security.",
      });
      loadSecurityStatus();
    } catch (err) {
      toast({
        title: "Emergency rotation failed",
        description: err instanceof Error ? err.message : 'Failed to rotate keys',
        variant: "destructive",
      });
    }
  };

  const getSecurityScore = (): { score: number; level: string; color: string } => {
    if (!encryptionActive) {
      return { score: 0, level: 'No Encryption', color: 'text-red-600' };
    }

    let score = 40; // Base score for having encryption

    if (rotationStatus?.isScheduled) {
      score += 30; // Scheduled rotation
    }

    if (rotationStatus?.daysUntilRotation > 30) {
      score += 20; // Recent rotation
    } else if (rotationStatus?.daysUntilRotation <= 0) {
      score -= 10; // Overdue rotation
    }

    if (rotationStatus?.activeKeys > 1) {
      score += 10; // Multiple keys for backward compatibility
    }

    if (score >= 80) {
      return { score, level: 'Excellent', color: 'text-green-600' };
    } else if (score >= 60) {
      return { score, level: 'Good', color: 'text-blue-600' };
    } else if (score >= 40) {
      return { score, level: 'Basic', color: 'text-yellow-600' };
    } else {
      return { score, level: 'Poor', color: 'text-red-600' };
    }
  };

  const securityScore = getSecurityScore();

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading security status...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!encryptionActive) {
    return (
      <Card className="border-amber-200 bg-amber-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-amber-800">
            <ShieldAlert className="h-5 w-5" />
            Encryption Not Active
          </CardTitle>
          <CardDescription className="text-amber-700">
            Your messages are not encrypted. Enable encryption to secure your communications.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Security Score Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Status
          </CardTitle>
          <CardDescription>
            Overall security assessment of your encryption setup
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <span className={`text-2xl font-bold ${securityScore.color}`}>
                  {securityScore.score}%
                </span>
                <Badge 
                  variant={securityScore.score >= 60 ? "default" : "secondary"}
                  className={securityScore.score >= 60 ? "bg-green-100 text-green-800" : ""}
                >
                  {securityScore.level}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">Security Score</p>
            </div>
            <ShieldCheck className="h-8 w-8 text-green-600" />
          </div>
          
          <Progress value={securityScore.score} className="w-full" />

          {securityScore.score < 60 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Consider setting up key rotation and regular security practices to improve your score.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Detailed Status */}
      <Tabs defaultValue="encryption" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="encryption">Encryption</TabsTrigger>
          <TabsTrigger value="rotation">Key Rotation</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="encryption" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle2 className="h-5 w-5" />
                Encryption Active
              </CardTitle>
              <CardDescription>
                Your messages are protected with end-to-end encryption
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium">Encryption Status</p>
                  <p className="text-green-600">✓ Active</p>
                </div>
                <div>
                  <p className="font-medium">Algorithm</p>
                  <p className="text-muted-foreground">NaCl Box (Curve25519)</p>
                </div>
                <div>
                  <p className="font-medium">Key Storage</p>
                  <p className="text-green-600">✓ Password Protected</p>
                </div>
                <div>
                  <p className="font-medium">Forward Secrecy</p>
                  <p className={rotationStatus?.isScheduled ? "text-green-600" : "text-amber-600"}>
                    {rotationStatus?.isScheduled ? "✓ Enabled" : "⚠ Manual Only"}
                  </p>
                </div>
              </div>

              <div className="pt-2 border-t">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    clearEncryptionSession();
                    setEncryptionActive(false);
                    toast({
                      title: "Session cleared",
                      description: "You'll need to re-enter your password to access encrypted messages.",
                    });
                  }}
                >
                  Clear Session
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rotation" className="space-y-4">
          {rotationStatus?.isScheduled ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Automatic Key Rotation
                </CardTitle>
                <CardDescription>
                  Keys are automatically rotated for enhanced security
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium">Next Rotation</p>
                    <p className={rotationStatus.daysUntilRotation <= 7 ? "text-amber-600" : "text-muted-foreground"}>
                      {rotationStatus.daysUntilRotation > 0 
                        ? `${rotationStatus.daysUntilRotation} days`
                        : 'Overdue'
                      }
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Active Keys</p>
                    <p className="text-muted-foreground">{rotationStatus.activeKeys}</p>
                  </div>
                  <div>
                    <p className="font-medium">Last Rotation</p>
                    <p className="text-muted-foreground">
                      {rotationStatus.lastRotation 
                        ? rotationStatus.lastRotation.toLocaleDateString()
                        : 'Never'
                      }
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Total Keys</p>
                    <p className="text-muted-foreground">{rotationStatus.totalKeys}</p>
                  </div>
                </div>

                {rotationStatus.daysUntilRotation <= 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Your keys are overdue for rotation. Please rotate them for optimal security.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex gap-2 pt-2 border-t">
                  <SecurePasswordInput
                    onPasswordSubmit={handleRotateKeys}
                    title="Rotate Encryption Keys"
                    description="Enter your password to rotate your encryption keys now. This will generate new keys while keeping old ones for decrypting previous messages."
                    buttonText="Rotate Keys"
                    buttonVariant="outline"
                  >
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      <RotateCcw className="h-4 w-4" />
                      Rotate Now
                    </Button>
                  </SecurePasswordInput>

                  <SecurePasswordInput
                    onPasswordSubmit={handleForceRotation}
                    title="Emergency Key Rotation"
                    description="Immediately rotate your keys if you suspect they may be compromised. This is an emergency security measure."
                    buttonText="Emergency Rotate"
                    buttonVariant="destructive"
                  >
                    <Button variant="destructive" size="sm" className="flex items-center gap-1">
                      <AlertTriangle className="h-4 w-4" />
                      Emergency
                    </Button>
                  </SecurePasswordInput>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="border-amber-200 bg-amber-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-amber-800">
                  <Clock className="h-5 w-5" />
                  Key Rotation Not Configured
                </CardTitle>
                <CardDescription className="text-amber-700">
                  Set up automatic key rotation for enhanced security
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={async () => {
                    try {
                      await rotationManager.initializeRotationSchedule();
                      loadSecurityStatus();
                      toast({
                        title: "Key rotation enabled",
                        description: "Automatic key rotation has been set up.",
                      });
                    } catch (err) {
                      toast({
                        title: "Setup failed",
                        description: err instanceof Error ? err.message : 'Failed to setup rotation',
                        variant: "destructive",
                      });
                    }
                  }}
                  className="w-full"
                >
                  <Key className="h-4 w-4 mr-2" />
                  Enable Auto Rotation
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Security History
              </CardTitle>
              <CardDescription>
                Track your encryption and security activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Encryption Activated</p>
                    <p className="text-xs text-muted-foreground">Secure messaging enabled</p>
                  </div>
                  <span className="text-xs text-muted-foreground">Today</span>
                </div>

                {rotationStatus?.lastRotation && (
                  <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                    <RotateCcw className="h-4 w-4 text-blue-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">Keys Rotated</p>
                      <p className="text-xs text-muted-foreground">Enhanced forward secrecy</p>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {rotationStatus.lastRotation.toLocaleDateString()}
                    </span>
                  </div>
                )}

                <div className="text-center py-4">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={loadSecurityStatus}
                    className="flex items-center gap-1"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Refresh Status
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}