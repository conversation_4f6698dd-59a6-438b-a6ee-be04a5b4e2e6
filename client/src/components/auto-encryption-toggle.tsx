import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Shield, ShieldOff, RotateCcw } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface AutoEncryptionToggleProps {
  onEncryptionChange?: (enabled: boolean) => void;
}

export function AutoEncryptionToggle({ onEncryptionChange }: AutoEncryptionToggleProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [encryptionEnabled, setEncryptionEnabled] = useState(user?.autoEncryptionEnabled ?? true);

  const toggleEncryptionMutation = useMutation({
    mutationFn: async (enabled: boolean) => {
      const response = await apiRequest('PATCH', '/api/user/auto-encryption', { enabled });
      return response.json();
    },
    onSuccess: (data, enabled) => {
      setEncryptionEnabled(enabled);
      onEncryptionChange?.(enabled);
      
      // Update user data in cache
      queryClient.setQueryData(['/api/user'], (oldData: any) => ({
        ...oldData,
        autoEncryptionEnabled: enabled
      }));
      
      toast({
        title: enabled ? "Auto-encryption enabled" : "Auto-encryption disabled",
        description: enabled 
          ? "Your messages will be automatically encrypted" 
          : "Your messages will be sent as plain text",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update encryption setting",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    }
  });

  return (
    <Card className="border-text">
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          {encryptionEnabled ? (
            <Shield className="h-4 w-4 text-green-500" />
          ) : (
            <ShieldOff className="h-4 w-4 text-red-500" />
          )}
          Auto-Encryption
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <Label htmlFor="auto-encryption" className="text-sm">
            Encrypt messages automatically
          </Label>
          <Switch
            id="auto-encryption"
            checked={encryptionEnabled}
            onCheckedChange={(checked) => {
              toggleEncryptionMutation.mutate(checked);
            }}
            disabled={toggleEncryptionMutation.isPending}
          />
        </div>
        
        <div className="space-y-2">
          {encryptionEnabled ? (
            <Badge className="bg-green-900/20 text-green-300 border-green-700">
              <Shield className="h-3 w-3 mr-1" />
              Messages encrypted
            </Badge>
          ) : (
            <Badge className="bg-red-900/20 text-red-300 border-red-700">
              <ShieldOff className="h-3 w-3 mr-1" />
              Messages not encrypted
            </Badge>
          )}
          
          <p className="text-xs text-muted-foreground">
            {encryptionEnabled 
              ? "Strong encryption keys are automatically generated and rotated every 15 days. No passwords required."
              : "Messages will be sent as plain text and stored unencrypted."}
          </p>
          
          {encryptionEnabled && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <RotateCcw className="h-3 w-3" />
              <span>Keys auto-rotate for security</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}