import { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, Camera, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { AvatarDisplay } from "@/components/ui/avatar-display";

interface ImageAvatarUploaderProps {
  currentAvatar?: string;
  onAvatarUpdate?: (avatar: string) => void;
}

export function ImageAvatarUploader({ currentAvatar, onAvatarUpdate }: ImageAvatarUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const TARGET_SIZE = 128; // 128x128 pixels

  const resizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      img.onload = () => {
        // Set canvas size to target dimensions
        canvas.width = TARGET_SIZE;
        canvas.height = TARGET_SIZE;

        // Calculate crop dimensions to maintain aspect ratio
        const size = Math.min(img.width, img.height);
        const startX = (img.width - size) / 2;
        const startY = (img.height - size) / 2;

        // Draw the cropped and resized image
        ctx.drawImage(
          img,
          startX, startY, size, size, // Source rectangle
          0, 0, TARGET_SIZE, TARGET_SIZE // Destination rectangle
        );

        // Convert to base64 with good quality
        const resizedDataUrl = canvas.toDataURL('image/jpeg', 0.85);
        resolve(resizedDataUrl);
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      toast({
        title: "Invalid File Type",
        description: "Please upload a JPEG, PNG, or WebP image.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast({
        title: "File Too Large",
        description: "Please upload an image smaller than 5MB.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      // Create preview from file
      const previewUrl = URL.createObjectURL(file);
      setPreviewUrl(previewUrl);

      // Upload to server
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await fetch('/api/user/avatar/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload avatar');
      }

      const result = await response.json();

      // Update with server-processed image URL
      if (onAvatarUpdate) {
        await onAvatarUpdate(result.avatarUrl);
      }

      toast({
        title: "Avatar Updated",
        description: "Your profile picture has been updated successfully!",
      });

      // Clear preview after successful upload
      setPreviewUrl(null);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload image. Please try again.",
        variant: "destructive",
      });
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveAvatar = async () => {
    if (onAvatarUpdate) {
      try {
        await onAvatarUpdate('');
        setPreviewUrl(null);
        toast({
          title: "Avatar Removed",
          description: "Your profile picture has been removed.",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to remove avatar.",
          variant: "destructive",
        });
      }
    }
  };

  const displayAvatar = previewUrl || currentAvatar;
  const isImageAvatar = displayAvatar && (displayAvatar.startsWith('data:image/') || displayAvatar.startsWith('http'));

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5" />
          Profile Picture
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Avatar Display */}
        <div className="flex items-center gap-4">
          <AvatarDisplay 
            avatar={displayAvatar} 
            size="xl"
            className="w-24 h-24"
            fallback="No Image"
          />
          <div className="flex-1">
            <p className="text-sm text-muted-foreground">
              Upload a profile picture that represents you. Images will be automatically 
              resized to 128x128 pixels and cropped to a square.
            </p>
          </div>
        </div>

        {/* Upload Controls */}
        <div className="space-y-3">
          <div>
            <Label htmlFor="avatar-upload">Choose Image</Label>
            <Input
              id="avatar-upload"
              ref={fileInputRef}
              type="file"
              accept={ALLOWED_TYPES.join(',')}
              onChange={handleFileSelect}
              disabled={isUploading}
              className="hidden"
            />
          </div>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="flex-1"
            >
              <Upload className="mr-2 h-4 w-4" />
              {isUploading ? "Processing..." : "Upload Image"}
            </Button>

            {displayAvatar && (
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={handleRemoveAvatar}
                disabled={isUploading}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="text-xs text-muted-foreground space-y-1">
            <p>• Supported formats: JPEG, PNG, WebP</p>
            <p>• Maximum file size: 5MB</p>
            <p>• Images will be cropped to square and resized to 128x128</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}