import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Edit3, Save, X } from "lucide-react";

interface BioEditorProps {
  currentBio?: string;
  userId: number;
}

export function BioEditor({ currentBio = "", userId }: BioEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [bio, setBio] = useState(currentBio);
  const { toast } = useToast();

  const updateBioMutation = useMutation({
    mutationFn: async (newBio: string) => {
      const response = await apiRequest("POST", "/api/user/bio", {
        bio: newBio
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update bio');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/stats'] });
      queryClient.invalidateQueries({ queryKey: ['/api/users', userId, 'profile'] });
      setIsEditing(false);
      toast({
        title: "Bio Updated",
        description: "Your bio has been saved successfully!",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    updateBioMutation.mutate(bio);
  };

  const handleCancel = () => {
    setBio(currentBio);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Edit3 className="h-5 w-5" />
              About Me
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Edit3 className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentBio ? (
            <p className="text-sm text-muted-foreground leading-relaxed whitespace-pre-wrap">
              {currentBio}
            </p>
          ) : (
            <p className="text-sm text-muted-foreground italic">
              No bio added yet. Click "Edit" to add a bio and tell others about yourself.
            </p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Edit3 className="h-5 w-5" />
          Edit Bio
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="bio">About Me</Label>
          <Textarea
            id="bio"
            placeholder="Tell others about yourself, your interests, or anything you'd like to share..."
            value={bio}
            onChange={(e) => setBio(e.target.value)}
            maxLength={500}
            rows={4}
            className="resize-none"
          />
          <p className="text-xs text-muted-foreground">
            {bio.length}/500 characters
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={updateBioMutation.isPending}
            size="sm"
          >
            <Save className="h-4 w-4 mr-2" />
            {updateBioMutation.isPending ? "Saving..." : "Save"}
          </Button>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={updateBioMutation.isPending}
            size="sm"
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}