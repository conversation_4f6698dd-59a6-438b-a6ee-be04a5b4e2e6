import { Link, useLocation } from 'wouter';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import type { threadCategoryEnum } from '@shared/schema';

const CATEGORY_LABELS: Record<typeof threadCategoryEnum.enumValues[number], string> = {
  GENERAL: "General Discussion",
  TECHNICAL_SUPPORT: "Technical Support",
  AI_NEWS: "AI News",
  GLOBAL_NEWS: "Global News",
  FUNNY: "Fun & Memes"
};

interface ForumThreadProps {
  id: number;
  title: string;
  authorName: string;
  authorId?: number;
  createdAt: string;
  category: typeof threadCategoryEnum.enumValues[number];
  replyCount: number;
  voteCount: number;
  className?: string;
}

export function ForumThread({
  id,
  title,
  authorName,
  authorId,
  createdAt,
  category,
  replyCount,
  voteCount,
  className
}: ForumThreadProps) {
  const [, setLocation] = useLocation();
  
  const handleThreadClick = (e: React.MouseEvent) => {
    // If clicking on a link inside the thread, don't navigate to thread
    if ((e.target as HTMLElement).closest('a[href^="/profile/"]')) {
      return;
    }
    setLocation(`/thread/${id}`);
  };

  return (
    <div 
      className={cn("border border-primary/40 p-3 hover:bg-primary/5 transition-colors cursor-pointer", className)}
      onClick={handleThreadClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-primary font-mono text-lg mb-1">{title}</h3>
          <div className="text-sm text-muted-foreground font-mono flex items-center gap-2">
            <span className="px-2 py-0.5 bg-primary/10 rounded text-xs">
              {CATEGORY_LABELS[category]}
            </span>
            <span>•</span>
            <span>By: 
              {authorId ? (
                <Link href={`/profile/${authorId}`} className="text-primary hover:underline ml-1">
                  {authorName}
                </Link>
              ) : (
                <span className="ml-1">{authorName}</span>
              )}
            </span>
            <span>•</span>
            <span>{replyCount} {replyCount === 1 ? 'reply' : 'replies'}</span>
            <span>•</span>
            <span>{format(new Date(createdAt), 'MMM d, yyyy')}</span>
          </div>
        </div>
      </div>
    </div>
  );
}