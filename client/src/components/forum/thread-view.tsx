import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertReplySchema, type Thread, type Reply, type InsertReply, threadCategoryEnum } from "@shared/schema";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ChevronLeft, Send, User, Clock } from "lucide-react";
import { Link } from "wouter";
import { format } from "date-fns";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useSoundEffects } from "@/lib/sounds";
import DOMPurify from 'dompurify';
import { ContentFilterDialog } from "@/components/ui/content-filter-dialog";

const CATEGORY_LABELS: Record<typeof threadCategoryEnum.enumValues[number], string> = {
  GENERAL: "General Discussion",
  TECHNICAL_SUPPORT: "Technical Support",
  AI_NEWS: "AI News",
  GLOBAL_NEWS: "Global News",
  FUNNY: "Fun & Memes"
};

interface ThreadWithDetails extends Thread {
  authorName: string;
}

interface ReplyWithDetails extends Reply {
  authorName: string;
}

type ThreadViewProps = {
  threadId: number;
  thread: ThreadWithDetails;
};

// Custom hook for content filter error handling
function useContentFilter() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogMessage, setDialogMessage] = useState('');

  const handleContentFilterError = (error: any) => {
    let message = "Your reply could not be sent due to a violation of community guidelines.";
    if (typeof error === 'object' && error.message) {
      message = error.message;
    } else if (typeof error === 'string'){
      message = error;
    }
    setDialogMessage(message);
    setIsDialogOpen(true);
    return true; // Signal that we've handled this error
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  // ContentFilterDialog is our existing component for showing content filter violations
  const ContentFilterDialogComponent = () => (
    <ContentFilterDialog 
      isOpen={isDialogOpen}
      onClose={closeDialog}
      message={dialogMessage}
    />
  );

  return { handleContentFilterError, ContentFilterDialogComponent };
}


export function ThreadView({ threadId, thread }: ThreadViewProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { playSound } = useSoundEffects();
  const { handleContentFilterError, ContentFilterDialogComponent } = useContentFilter();
  
  // Helper function to check if a response indicates a content filter error
  const isContentFilterError = (response: Response | any): boolean => {
    if (response && response.headers) {
      return response.headers.get('X-Content-Filter-Error') === 'true';
    }
    
    if (response && response.type === 'content_filter') {
      return true;
    }
    
    return false;
  };

  const deleteThread = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("DELETE", `/api/threads/${threadId}`);
      
      // Check for content filter errors
      if (isContentFilterError(res)) {
        const errorData = await res.json();
        handleContentFilterError(errorData.error || "Content moderation blocked this operation.");
        return null; // Return null to indicate content filter error
      }
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to delete thread");
      }
      
      return res;
    },
    onSuccess: (result) => {
      // Only proceed if not a content filter error
      if (result) {
        playSound("message-sent");
        toast({
          title: "Success",
          description: "Thread deleted successfully"
        });
        window.location.href = "/";
      }
    },
    onError: (error: Error) => {
      // Only handle non-content filter errors here
      if (!(error.name === 'ContentFilterError' || 
            error.message.includes("Content Moderation") || 
            error.message.includes("inappropriate language") || 
            error.message.includes("community guidelines"))) {
        console.error("Thread deletion error:", error.message);
        playSound("error");
        toast({
          title: "Error",
          description: error.message || "Failed to delete thread",
          variant: "destructive",
        });
      }
    }
  });

  const form = useForm<InsertReply>({
    resolver: zodResolver(insertReplySchema),
    defaultValues: {
      content: "",
    },
  });

  const { data: replies = [], isLoading: repliesLoading } = useQuery<ReplyWithDetails[]>({
    queryKey: [`/api/threads/${threadId}/replies`],
    retry: 1,
    staleTime: 30000
  });

  const createReply = useMutation({
    mutationFn: async (data: InsertReply) => {
      const res = await apiRequest("POST", `/api/threads/${threadId}/replies`, data);
      
      // Check for content filter errors
      if (isContentFilterError(res)) {
        const errorData = await res.json();
        handleContentFilterError(errorData.error || "Your reply contains content that violates our community guidelines.");
        return null; // Return null to prevent further processing
      }
      
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || errorData.error || "Failed to post reply");
      }
      
      const result = await res.json();
      return result;
    },
    onSuccess: (result) => {
      // Only reset form and invalidate queries if we got a valid result
      // If result is null, it means we had a content filter error
      if (result) {
        queryClient.invalidateQueries({ queryKey: [`/api/threads/${threadId}/replies`] });
        queryClient.invalidateQueries({ queryKey: ['/api/threads'] });
        form.reset();
        playSound("message-sent");
        toast({
          title: "Success",
          description: "Your reply has been posted",
        });
      }
    },
    onError: (error: Error) => {
      console.error('Reply creation error:', error.message);
      // Here we only handle non-content filter errors
      // Content filter errors are already handled in mutationFn
      let errorMessage = "Failed to post reply. Please try again.";
      if (error.message) {
        errorMessage = error.message;
      }
      playSound("error");
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  if (repliesLoading) {
    return <div className="text-text font-mono">Loading replies...</div>;
  }

  if (!thread || !thread.id) {
    return (
      <div className="space-y-4">
        <Link href="/">
          <Button variant="outline" size="sm">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Threads
          </Button>
        </Link>
        <div className="text-text font-mono">Thread not found</div>
      </div>
    );
  }

  const onSubmit = (data: InsertReply) => {
    createReply.mutate(data);
  };

  return (
    <div className="space-y-6">
      {/* Content Filter Dialog */}
      <ContentFilterDialogComponent />

      <div className="flex justify-between items-center">
        <Link href="/">
          <Button variant="outline" size="sm">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Threads
          </Button>
        </Link>
        {user && (user.id === thread.authorId || user.isAdmin) && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => deleteThread.mutate()}
            disabled={deleteThread.isPending}
          >
            {deleteThread.isPending ? "Deleting..." : (user.isAdmin && user.id !== thread.authorId ? "Delete Thread (Admin)" : "Delete Thread")}
          </Button>
        )}
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-3 mb-2">
            <span className="px-2 py-0.5 bg-primary/10 rounded text-xs font-mono">
              {CATEGORY_LABELS[thread.category || "GENERAL"]}
            </span>
          </div>
          <CardTitle className="font-mono">{thread.title}</CardTitle>
          <div className="text-sm text-muted-foreground font-mono flex items-center gap-2">
            <User className="h-4 w-4" />
            <span>{thread.authorName}</span>
            <Clock className="h-4 w-4 ml-2" />
            <span>{format(new Date(thread.createdAt), 'MMM d, yyyy')}</span>
            {replies.length > 0 && (
              <>
                <span>•</span>
                <span>{replies.length} {replies.length === 1 ? 'reply' : 'replies'}</span>
              </>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div
            className="font-mono whitespace-pre-wrap break-words overflow-wrap-anywhere"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(thread.content)
            }}
          />
        </CardContent>
      </Card>

      <div className="space-y-4">
        {replies.map((reply: ReplyWithDetails) => (
          <Card key={reply.id}>
            <CardContent className="pt-6">
              <div className="font-mono whitespace-pre-wrap break-words overflow-wrap-anywhere">{reply.content}</div>
              <div className="text-sm text-muted-foreground font-mono flex items-center gap-2 mt-4">
                <div className="flex-grow flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{reply.authorName}</span>
                  <Clock className="h-4 w-4 ml-2" />
                  <span>{format(new Date(reply.createdAt), 'MMM d, yyyy')}</span>
                </div>
                {user && user.id === reply.authorId && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      const deleteReply = async () => {
                        try {
                          const res = await apiRequest(
                            "DELETE",
                            `/api/threads/${threadId}/replies/${reply.id}`
                          );
                          
                          // Check for content filter errors
                          if (isContentFilterError(res)) {
                            const errorData = await res.json();
                            handleContentFilterError(errorData.error || "Content moderation blocked this operation.");
                            return;
                          }
                          
                          if (res.ok) {
                            queryClient.invalidateQueries({ queryKey: [`/api/threads/${threadId}/replies`] });
                            queryClient.invalidateQueries({ queryKey: ['/api/threads'] });
                            playSound("message-sent");
                            toast({
                              title: "Success",
                              description: "Reply deleted successfully",
                            });
                          } else {
                            const error = await res.json();
                            throw new Error(error.message || "Failed to delete reply");
                          }
                        } catch (error) {
                          // Only handle non-content filter errors
                          if (!(error instanceof Error && 
                              (error.name === 'ContentFilterError' || 
                               error.message.includes("Content Moderation") || 
                               error.message.includes("inappropriate language") || 
                               error.message.includes("community guidelines")))) {
                            console.error("Error deleting reply:", error);
                            playSound("error");
                            toast({
                              title: "Error",
                              description: error instanceof Error ? error.message : "Failed to delete reply",
                              variant: "destructive",
                            });
                          }
                        }
                      };
                      deleteReply();
                    }}
                  >
                    Delete
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {user && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="Write your reply..."
                      className="font-mono resize-none"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <Button type="submit" className="font-mono">
              <Send className="h-4 w-4 mr-2" />
              Post Reply
            </Button>
          </form>
        </Form>
      )}
    </div>
  );
}