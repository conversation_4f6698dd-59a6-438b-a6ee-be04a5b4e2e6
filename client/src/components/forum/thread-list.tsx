import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertThreadSchema, type Thread, type InsertThread, threadCategoryEnum } from "@shared/schema";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { Link } from "wouter";
import { ContentFilterDialog } from "@/components/ui/content-filter-dialog";
import { useSoundEffects } from "@/lib/sounds";

const CATEGORY_LABELS: Record<typeof threadCategoryEnum.enumValues[number], string> = {
  GENERAL: "General Discussion",
  TECHNICAL_SUPPORT: "Technical Support",
  AI_NEWS: "AI News",
  GLOBAL_NEWS: "Global News",
  FUNNY: "Fun & Memes"
};

interface ThreadWithDetails extends Thread {
  authorName: string;
  replyCount: number;
  voteCount: number;
}

export function ThreadList() {
  const { user } = useAuth();
  const { playSound } = useSoundEffects();
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);
  const [filterMessage, setFilterMessage] = useState("");
  
  const form = useForm<InsertThread>({
    resolver: zodResolver(insertThreadSchema),
    defaultValues: {
      title: "",
      content: "",
      category: "GENERAL"
    },
  });

  const { data: threads, isLoading } = useQuery<ThreadWithDetails[]>({
    queryKey: ["/api/threads"],
    refetchInterval: 6000, // Auto-refresh every 6 seconds
    refetchIntervalInBackground: true // Continue refreshing when tab is not active
  });

  // Check if a response indicates a content filter error
  const isContentFilterError = (response: Response | any): boolean => {
    if (response && response.headers) {
      return response.headers.get('X-Content-Filter-Error') === 'true';
    }
    
    if (response && response.type === 'content_filter') {
      return true;
    }
    
    return false;
  };

  const createThread = useMutation({
    mutationFn: async (data: InsertThread) => {
      const res = await apiRequest("POST", "/api/threads", data);
      
      // Check for content filter errors
      if (isContentFilterError(res)) {
        const errorData = await res.json();
        setFilterMessage(errorData.error || "Your thread contains content that violates our community guidelines.");
        setIsFilterDialogOpen(true);
        playSound("error");
        return null; // Return null to prevent further processing
      }
      
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to create thread");
      }
      
      const result = await res.json();
      return result;
    },
    onSuccess: (result) => {
      // Only reset form and invalidate queries if we got a valid result
      // If result is null, it means we had a content filter error
      if (result) {
        queryClient.invalidateQueries({ queryKey: ["/api/threads"] });
        form.reset();
      }
    },
    onError: (error: Error) => {
      console.error('Thread creation error:', error.message);
      // Handle non-content filter errors here
      // Content filter errors are already handled in the mutationFn
    }
  });

  if (isLoading) return <div className="text-text">Loading threads...</div>;

  return (
    <div className="space-y-6">
      {/* Content Filter Dialog */}
      <ContentFilterDialog 
        isOpen={isFilterDialogOpen}
        onClose={() => setIsFilterDialogOpen(false)}
        message={filterMessage}
      />
      
      <Card className="border-primary/40">
        <CardHeader>
          <CardTitle className="text-primary">Create New Thread</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit((data) => createThread.mutate(data))} className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-primary">Title</FormLabel>
                    <FormControl>
                      <Input {...field} className="border-primary/40" />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-primary">Category</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="border-primary/40">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-primary">Content</FormLabel>
                    <FormControl>
                      <Textarea {...field} className="border-primary/40 min-h-[100px]" />
                    </FormControl>
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={createThread.isPending} className="w-full">
                {createThread.isPending ? "Creating..." : "Create Thread"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      <div className="space-y-4">
        {threads?.map((thread) => (
          <Link key={thread.id} href={`/thread/${thread.id}`}>
            <div className="border border-primary/40 p-3 hover:bg-primary/5 transition-colors cursor-pointer">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-primary font-mono text-lg mb-1">{thread.title}</h3>
                  <div className="text-sm text-muted-foreground font-mono flex items-center gap-2">
                    <span className="px-2 py-0.5 bg-primary/10 rounded text-xs">
                      {CATEGORY_LABELS[thread.category || "GENERAL"]}
                    </span>
                    <span>•</span>
                    <span>By: 
                      {thread.authorId ? (
                        <Link href={`/profile/${thread.authorId}`} className="text-primary hover:underline ml-1" onClick={(e) => e.stopPropagation()}>
                          {thread.authorName}
                        </Link>
                      ) : (
                        <span className="ml-1">{thread.authorName}</span>
                      )}
                    </span>
                    <span>•</span>
                    <span>Last: {new Date(thread.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}