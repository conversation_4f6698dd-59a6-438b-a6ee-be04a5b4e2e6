import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { 
  Shield, 
  ShieldCheck, 
  RotateCcw, 
  Clock, 
  Users, 
  Key, 
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";

interface EncryptionStats {
  totalUsers: number;
  encryptionEnabled: number;
  encryptionDisabled: number;
  totalKeys: number;
  expiringSoon: number;
  messagesEncrypted: number;
  messagesPlaintext: number;
}

interface UserKeyInfo {
  userId: number;
  username: string;
  hasKeys: boolean;
  keyCreatedAt?: string;
  keyExpiresAt?: string;
  encryptionEnabled: boolean;
  daysUntilExpiry?: number;
}

export function EncryptionDashboard() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [autoRotationEnabled, setAutoRotationEnabled] = useState(true);

  // Fetch encryption statistics
  const { data: stats, isLoading: statsLoading } = useQuery<EncryptionStats>({
    queryKey: ['/api/admin/encryption/stats'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch user key information
  const { data: userKeys = [], isLoading: keysLoading } = useQuery<UserKeyInfo[]>({
    queryKey: ['/api/admin/encryption/users'],
    refetchInterval: 60000, // Refresh every minute
  });

  // Force key rotation for all users
  const rotateAllKeysMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/admin/encryption/rotate-all');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/encryption'] });
      toast({
        title: "Key Rotation Initiated",
        description: "All encryption keys are being rotated. This may take a few moments.",
      });
    },
    onError: (error) => {
      toast({
        title: "Rotation Failed",
        description: error instanceof Error ? error.message : "Failed to rotate keys",
        variant: "destructive"
      });
    }
  });

  // Force key rotation for specific user
  const rotateUserKeyMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest('POST', `/api/admin/encryption/rotate-user/${userId}`);
      return response.json();
    },
    onSuccess: (data, userId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/encryption'] });
      const user = userKeys.find(u => u.userId === userId);
      toast({
        title: "User Key Rotated",
        description: `New encryption keys generated for ${user?.username}`,
      });
    },
    onError: (error) => {
      toast({
        title: "User Key Rotation Failed",
        description: error instanceof Error ? error.message : "Failed to rotate user key",
        variant: "destructive"
      });
    }
  });

  // Toggle auto-rotation service
  const toggleAutoRotationMutation = useMutation({
    mutationFn: async (enabled: boolean) => {
      const response = await apiRequest('PATCH', '/api/admin/encryption/auto-rotation', { enabled });
      return response.json();
    },
    onSuccess: (data, enabled) => {
      setAutoRotationEnabled(enabled);
      toast({
        title: enabled ? "Auto-rotation Enabled" : "Auto-rotation Disabled",
        description: enabled 
          ? "Keys will automatically rotate every 15 days" 
          : "Manual key rotation required",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to Update Auto-rotation",
        description: error instanceof Error ? error.message : "Failed to update setting",
        variant: "destructive"
      });
    }
  });

  const getExpiryStatus = (daysUntilExpiry?: number) => {
    if (!daysUntilExpiry) return { color: "gray", text: "No keys" };
    if (daysUntilExpiry <= 1) return { color: "red", text: "Expires today" };
    if (daysUntilExpiry <= 3) return { color: "orange", text: "Expires soon" };
    if (daysUntilExpiry <= 7) return { color: "yellow", text: "Expires this week" };
    return { color: "green", text: "Healthy" };
  };

  const calculateProgress = (daysUntilExpiry?: number) => {
    if (!daysUntilExpiry) return 0;
    const totalDays = 15; // 15-day rotation cycle
    const daysUsed = totalDays - daysUntilExpiry;
    return Math.min(100, Math.max(0, (daysUsed / totalDays) * 100));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-glow">Encryption Key Management</h2>
          <p className="text-muted-foreground">
            Monitor and manage automatic encryption key rotation
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Switch
            checked={autoRotationEnabled}
            onCheckedChange={(checked) => toggleAutoRotationMutation.mutate(checked)}
            disabled={toggleAutoRotationMutation.isPending}
          />
          <span className="text-sm">Auto-rotation</span>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-text">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.encryptionEnabled || 0} with encryption enabled
            </p>
          </CardContent>
        </Card>

        <Card className="border-text">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Keys</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalKeys || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.expiringSoon || 0} expiring soon
            </p>
          </CardContent>
        </Card>

        <Card className="border-text">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Messages Encrypted</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.messagesEncrypted || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.messagesPlaintext || 0} plaintext messages
            </p>
          </CardContent>
        </Card>

        <Card className="border-text">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {autoRotationEnabled ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              )}
              <span className="text-sm font-medium">
                {autoRotationEnabled ? "Active" : "Manual"}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Auto-rotation {autoRotationEnabled ? "enabled" : "disabled"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card className="border-text">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            Key Rotation Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => rotateAllKeysMutation.mutate()}
              disabled={rotateAllKeysMutation.isPending}
              className="flex items-center gap-2"
            >
              {rotateAllKeysMutation.isPending ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4" />
              )}
              Rotate All Keys Now
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                queryClient.invalidateQueries({ queryKey: ['/api/admin/encryption'] });
              }}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh Data
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Manual key rotation will generate new encryption keys for all users immediately.
            This may temporarily interrupt encrypted messaging.
          </p>
        </CardContent>
      </Card>

      {/* User Key Status Table */}
      <Card className="border-text">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            User Key Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Encryption</TableHead>
                  <TableHead>Key Age</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {keysLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      Loading user key information...
                    </TableCell>
                  </TableRow>
                ) : userKeys.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No user key data available
                    </TableCell>
                  </TableRow>
                ) : (
                  userKeys.map((userKey) => {
                    const status = getExpiryStatus(userKey.daysUntilExpiry);
                    const progress = calculateProgress(userKey.daysUntilExpiry);
                    
                    return (
                      <TableRow key={userKey.userId}>
                        <TableCell className="font-medium">{userKey.username}</TableCell>
                        <TableCell>
                          {userKey.encryptionEnabled ? (
                            <Badge className="bg-green-900/20 text-green-300">
                              <Shield className="h-3 w-3 mr-1" />
                              Enabled
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-red-900/20 text-red-300">
                              Disabled
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {userKey.keyCreatedAt ? (
                            <div className="space-y-1">
                              <div className="text-sm">
                                {formatDistanceToNow(new Date(userKey.keyCreatedAt), { addSuffix: true })}
                              </div>
                              <Progress value={progress} className="h-1 w-20" />
                            </div>
                          ) : (
                            <span className="text-muted-foreground">No keys</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {userKey.daysUntilExpiry ? (
                            <span className="text-sm">
                              {userKey.daysUntilExpiry === 0 ? "Today" : `${userKey.daysUntilExpiry} days`}
                            </span>
                          ) : (
                            <span className="text-muted-foreground">N/A</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={`
                              ${status.color === 'red' ? 'bg-red-900/20 text-red-300 border-red-700' : ''}
                              ${status.color === 'orange' ? 'bg-orange-900/20 text-orange-300 border-orange-700' : ''}
                              ${status.color === 'yellow' ? 'bg-yellow-900/20 text-yellow-300 border-yellow-700' : ''}
                              ${status.color === 'green' ? 'bg-green-900/20 text-green-300 border-green-700' : ''}
                              ${status.color === 'gray' ? 'bg-gray-900/20 text-gray-300 border-gray-700' : ''}
                            `}
                          >
                            {status.text}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => rotateUserKeyMutation.mutate(userKey.userId)}
                            disabled={rotateUserKeyMutation.isPending || !userKey.hasKeys}
                            className="h-8 w-8 p-0"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}