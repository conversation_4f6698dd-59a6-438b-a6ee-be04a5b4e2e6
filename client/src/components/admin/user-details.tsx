import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import { UserTimeline } from "./user-timeline";

interface UserDetailsProps {
  userId: number;
}

interface DetailedUser {
  id: number;
  username: string;
  isAdmin: boolean;
  createdAt: string;
  lastPasswordChange: string;
  creationIp: string;
  lastLoginIp: string;
  lastLoginAt: string;
}

export function UserDetails({ userId }: UserDetailsProps) {
  const { data: user, isLoading } = useQuery({
    queryKey: [`/api/admin/users/${userId}`],
    queryFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}`, {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to fetch user details');
      }
      return response.json() as Promise<DetailedUser>;
    }
  });

  if (isLoading) {
    return <div>Loading user details...</div>;
  }

  if (!user) {
    return <div>User not found</div>;
  }

  const formatDate = (date: string) => {
    return date ? format(new Date(date), 'PPpp') : 'Never';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>User Details - {user.username}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm">User ID</h4>
                <p className="text-sm text-muted-foreground">{user.id}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Username</h4>
                <p className="text-sm text-muted-foreground">{user.username}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Admin Status</h4>
                <p className="text-sm text-muted-foreground">
                  {user.isAdmin ? 'Yes' : 'No'}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Account Created</h4>
                <p className="text-sm text-muted-foreground">
                  {formatDate(user.createdAt)}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Last Password Change</h4>
                <p className="text-sm text-muted-foreground">
                  {formatDate(user.lastPasswordChange)}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Creation IP</h4>
                <p className="text-sm text-muted-foreground">{user.creationIp || 'Not recorded'}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Last Login IP</h4>
                <p className="text-sm text-muted-foreground">{user.lastLoginIp || 'Never logged in'}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm">Last Login Time</h4>
                <p className="text-sm text-muted-foreground">
                  {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never logged in'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <UserTimeline userId={userId} />
    </div>
  );
}