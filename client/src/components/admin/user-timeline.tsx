import { useQuery } from "@tanstack/react-query";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import { Loader2, Shield, Lock, UserX, AlertTriangle, LogIn, UserPlus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface TimelineEvent {
  type: 'account_creation' | 'login' | 'password_change' | 'suspension' | 'ban' | 'unsuspension' | 'unban';
  timestamp: string | Date;
  details: string;
  ip?: string;
  reason?: string;
  until?: string;
}

interface UserTimelineProps {
  userId: number;
}

export function UserTimeline({ userId }: UserTimelineProps) {
  const { toast } = useToast();

  const { data: events, isLoading, error } = useQuery({
    queryKey: [`/api/admin/users/${userId}/timeline`],
    queryFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}/timeline`, {
        credentials: 'include'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Timeline API error: ${response.status} - ${errorText}`);
        throw new Error(`Failed to fetch timeline: ${response.status}`);
      }

      const data = await response.json();
      return data as TimelineEvent[];
    },
    retry: 1,
    onError: (error: Error) => {
      console.error('Timeline query error:', error);
      toast({
        title: "Error",
        description: "Failed to load timeline. Please try again.",
        variant: "destructive",
      });
    }
  });

  const getEventStyle = (type: string) => {
    switch (type) {
      case 'account_creation':
        return { icon: <UserPlus className="h-4 w-4" />, color: 'text-green-500' };
      case 'login':
        return { icon: <LogIn className="h-4 w-4" />, color: 'text-blue-500' };
      case 'password_change':
        return { icon: <Lock className="h-4 w-4" />, color: 'text-yellow-500' };
      case 'suspension':
        return { icon: <UserX className="h-4 w-4" />, color: 'text-orange-500' };
      case 'unsuspension':
        return { icon: <UserPlus className="h-4 w-4" />, color: 'text-green-500' };
      case 'ban':
        return { icon: <AlertTriangle className="h-4 w-4" />, color: 'text-red-500' };
      case 'unban':
        return { icon: <Shield className="h-4 w-4" />, color: 'text-green-500' };
      default:
        return { icon: <Shield className="h-4 w-4" />, color: 'text-gray-500' };
    }
  };

  const formatEventDetails = (event: TimelineEvent) => {
    let details = event.details;
    if (event.type === 'suspension' && event.until) {
      details += ` (until ${format(new Date(event.until), 'PPpp')})`;
    }
    return details;
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'account_creation':
        return 'text-green-500';
      case 'login':
        return 'text-blue-500';
      case 'password_change':
        return 'text-yellow-500';
      case 'suspension':
        return 'text-orange-500';
      case 'ban':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Timeline</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-32">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-destructive">
            Failed to load timeline. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Timeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {!events || events.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center">No activity recorded yet.</p>
          ) : (
            events.map((event, index) => (
              <div key={index} className="flex items-start gap-4 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                <div className={`mt-1 ${getEventStyle(event.type).color}`}>
                  {getEventStyle(event.type).icon}
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">{formatEventDetails(event)}</p>
                  <div className="flex flex-wrap items-center gap-2 text-xs text-muted-foreground">
                    <span>{format(new Date(event.timestamp), 'PPpp')}</span>
                    {event.ip && <span>• IP: {event.ip}</span>}
                    {event.reason && (
                      <span className="font-medium text-destructive">
                        • Reason: {event.reason}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}