import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { apiRequest } from "@/lib/queryClient";
import { Progress } from "@/components/ui/progress";
import { useState } from "react";

interface QueryMetric {
  operation: string;
  duration: number;
  timestamp: string;
  success: boolean;
  error?: string;
}

interface MetricsSummary {
  total: number;
  successful: number;
  failed: number;
  averageDuration: number;
  slowQueries: number;
}

interface DBMetrics {
  metrics: QueryMetric[];
  summary: MetricsSummary;
}

export function DBMetrics() {
  const [autoRefresh, setAutoRefresh] = useState(false);

  const { data: metrics, isLoading, refetch } = useQuery<DBMetrics>({
    queryKey: ['/api/system/db-metrics'],
    refetchInterval: 4000, // Auto-refresh every 4 seconds
    refetchIntervalInBackground: true // Continue refreshing when tab is not active
  });

  const clearMetrics = async () => {
    await apiRequest('POST', '/api/system/db-metrics/clear');
    refetch();
  };

  if (isLoading) {
    return <div className="text-text">Loading metrics...</div>;
  }

  const formatDuration = (ms: number) => {
    if (ms < 1) return `${(ms * 1000).toFixed(2)}μs`;
    if (ms < 1000) return `${ms.toFixed(2)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const successRate = metrics ? (metrics.summary.successful / metrics.summary.total) * 100 : 0;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-primary">Database Performance Metrics</h2>
        <div className="space-x-2">
          <Button 
            variant="outline" 
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? 'Disable Auto-Refresh' : 'Enable Auto-Refresh'}
          </Button>
          <Button variant="default" onClick={() => refetch()}>
            Refresh
          </Button>
          <Button variant="destructive" onClick={clearMetrics}>
            Clear Metrics
          </Button>
        </div>
      </div>

      {metrics && (
        <>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.summary.total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
                  <Progress value={successRate} className="h-2" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatDuration(metrics.summary.averageDuration)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Slow Queries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.summary.slowQueries}</div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Operations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs uppercase bg-background/50">
                    <tr>
                      <th className="px-4 py-2">Operation</th>
                      <th className="px-4 py-2">Duration</th>
                      <th className="px-4 py-2">Status</th>
                      <th className="px-4 py-2">Timestamp</th>
                      <th className="px-4 py-2">Error</th>
                    </tr>
                  </thead>
                  <tbody>
                    {metrics.metrics.slice(-10).reverse().map((metric, idx) => (
                      <tr key={idx} className="border-b border-primary/20">
                        <td className="px-4 py-2 font-mono">{metric.operation}</td>
                        <td className="px-4 py-2">{formatDuration(metric.duration)}</td>
                        <td className="px-4 py-2">
                          <span className={`px-2 py-1 rounded text-xs ${
                            metric.success 
                              ? 'bg-green-500/20 text-green-500' 
                              : 'bg-red-500/20 text-red-500'
                          }`}>
                            {metric.success ? 'SUCCESS' : 'FAILED'}
                          </span>
                        </td>
                        <td className="px-4 py-2">
                          {new Date(metric.timestamp).toLocaleString()}
                        </td>
                        <td className="px-4 py-2 text-red-500">
                          {metric.error || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
