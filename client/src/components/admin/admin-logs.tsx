import { useQuery } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface AdminLog {
  id: number;
  type: string;
  timestamp: string;
  details: string;
  reason?: string;
  metadata?: string;
  username: string;
  adminUsername: string;
}

export function AdminLogs() {
  const { data: logs, isLoading } = useQuery({
    queryKey: ['/api/admin/logs'],
    queryFn: async () => {
      const response = await fetch('/api/admin/logs', {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to fetch admin logs');
      }
      return response.json() as Promise<AdminLog[]>;
    },
    refetchInterval: 3000, // Auto-refresh every 3 seconds
    refetchIntervalInBackground: true // Continue refreshing when tab is not active
  });

  // Function to format action type for display
  const formatActionType = (type: string): JSX.Element => {
    const text = type.replace('admin_', '').replace(/_/g, ' ');
    let color = '';
    
    if (type.includes('ban')) {
      color = 'destructive';
    } else if (type.includes('suspend')) {
      color = 'warning';
    } else if (type.includes('toggle_admin')) {
      color = 'purple';
    } else {
      color = 'secondary';
    }
    
    return (
      <Badge variant={color as any} className="capitalize">
        {text}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Admin Action Logs</h3>
        <div className="rounded-md border p-4">
          <Skeleton className="h-6 w-full mb-2" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Admin Action Logs</h3>
      {logs && logs.length > 0 ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Admin</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Target User</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Reason</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="whitespace-nowrap">{format(new Date(log.timestamp), 'PPpp')}</TableCell>
                  <TableCell className="font-mono text-green-500">{log.adminUsername}</TableCell>
                  <TableCell>{formatActionType(log.type)}</TableCell>
                  <TableCell className="font-mono text-orange-500">{log.username}</TableCell>
                  <TableCell>{log.details}</TableCell>
                  <TableCell>{log.reason || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center p-4 border rounded-md">
          <p className="text-muted-foreground">No admin actions have been logged yet.</p>
        </div>
      )}
    </div>
  );
}