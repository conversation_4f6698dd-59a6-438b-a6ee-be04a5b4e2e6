import { useState, useEffect } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Loader2, Play, RefreshCw, CheckCircle, XCircle, CheckCircle2,
  Terminal, Cpu, Lock, Bug, BarChart, Microscope, Rocket,
  Activity, AlertTriangle
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

// Helper function to format duration in a human-readable format
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  const minutes = Math.floor(ms / 60000);
  const seconds = ((ms % 60000) / 1000).toFixed(0);
  return `${minutes}m ${seconds}s`;
}

// Types for test results and config
interface TestConfig {
  headless: boolean;
  baseUrl: string;
  retries: number;
  captureScreenshots: boolean;
  timeout: number;
  reporter: 'json' | 'console' | 'all';
  verbose: boolean;
}

interface TestSummary {
  passed: number;
  failed: number;
  total: number;
  passRate: number;
  duration?: number; // Optional duration field
}

interface TestProgress {
  total: number;
  completed: number;
  currentTest: string;
  currentSuite: string;
}

interface TestRunStatus {
  completed: boolean;
  code: number | null;
  output: string;
  errors?: string;
  testRunId?: string;
  status?: 'running' | 'success' | 'failed' | 'error';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  summary?: TestSummary;
  progress?: TestProgress;
}

export function TestRunner() {
  const { toast } = useToast();
  const [selectedTests, setSelectedTests] = useState<string[]>([]);
  const [selectedTestType, setSelectedTestType] = useState<string>("standard");
  const [activeTestRun, setActiveTestRun] = useState<string | null>(null);
  const [testOutput, setTestOutput] = useState<string>("");
  interface TestResults {
    progress?: TestProgress;
    summary?: TestSummary;
    status?: 'running' | 'success' | 'failed' | 'error';
    duration?: number;
    startedAt?: string;
    completedAt?: string;
    errors?: string;
    [key: string]: any; // Allow for other properties
  }
  
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [statusPolling, setStatusPolling] = useState<NodeJS.Timeout | null>(null);
  
  // Test configuration
  const [config, setConfig] = useState<TestConfig>({
    headless: true,
    baseUrl: "http://localhost:3000",
    retries: 2,
    captureScreenshots: true,
    timeout: 30000,
    reporter: 'all',
    verbose: false
  });

  // Fetch available tests
  const { data: availableTests, isLoading: testsLoading } = useQuery({
    queryKey: ["/api/admin/tests"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/admin/tests");
      if (!response.ok) {
        throw new Error("Failed to fetch available tests");
      }
      return response.json();
    },
  });

  // Poll for test status
  useEffect(() => {
    if (activeTestRun && !statusPolling) {
      // Keep track of consecutive failures
      let failureCount = 0;
      const MAX_FAILURES = 5; // Allow up to 5 consecutive failures before stopping
      
      const interval = setInterval(async () => {
        try {
          const response = await apiRequest("GET", `/api/admin/tests/status/${activeTestRun}`);
          
          // Reset failure count on success
          if (response.ok) {
            failureCount = 0;
            const status: TestRunStatus = await response.json();
            
            // Update output as it comes in
            if (status.output !== testOutput) {
              setTestOutput(status.output);
            }
            
            // If progress information is available, update results with it
            if (status.progress) {
              setTestResults((prev: TestResults | null) => ({
                ...(prev || {}),
                progress: status.progress
              }));
            }
            
            if (status.completed) {
              clearInterval(interval);
              setStatusPolling(null);
              setActiveTestRun(null);
              
              // If we have a summary directly in the status, use it
              if (status.summary) {
                setTestResults((prev: TestResults | null) => ({
                  ...(prev || {}),
                  summary: status.summary
                }));
              } 
              // Otherwise try to parse it from the output
              else {
                try {
                  // Try to extract JSON results from the output
                  const resultMatches = status.output.match(/\{[\s\S]*"summary"[\s\S]*\}/g);
                  if (resultMatches && resultMatches.length > 0) {
                    const resultJson = JSON.parse(resultMatches[0]);
                    setTestResults(resultJson);
                  }
                  // If no JSON results, look for standard test summary format
                  else if (status.output.includes("=== Test Summary ===")) {
                    const summaryMatch = status.output.match(/===\s*Test Summary\s*===[\s\S]*Pass rate:\s*(\d+)%/);
                    if (summaryMatch) {
                      const passRateStr = summaryMatch[1];
                      const passRate = parseInt(passRateStr, 10);
                      const totalMatch = status.output.match(/Total tests:\s*(\d+)/);
                      const passedMatch = status.output.match(/Passed:\s*(\d+)/);
                      const failedMatch = status.output.match(/Failed:\s*(\d+)/);
                      
                      setTestResults((prev: TestResults | null) => ({
                        ...(prev || {}),
                        summary: {
                          total: totalMatch ? parseInt(totalMatch[1], 10) : 0,
                          passed: passedMatch ? parseInt(passedMatch[1], 10) : 0,
                          failed: failedMatch ? parseInt(failedMatch[1], 10) : 0,
                          passRate: isNaN(passRate) ? 0 : passRate
                        }
                      }));
                    }
                  }
                } catch (err) {
                  console.error("Failed to parse test results:", err);
                }
              }
              
              // Determine status message based on status field or code
              const statusText = status.status || (status.code === 0 ? 'success' : 'failed');
              const isSuccess = statusText === 'success' || status.code === 0;
              
              // Show success/error toast with more detailed information
              toast({
                title: isSuccess ? "Tests completed successfully" : "Tests completed with issues",
                description: isSuccess 
                  ? `Tests passed! (${status.duration ? Math.round(status.duration / 1000) : '?'}s)` 
                  : `Test execution finished with ${status.errors ? 'errors' : `code ${status.code}`}`,
                variant: isSuccess ? "default" : "destructive",
              });
            }
          } else {
            // Increment failure count
            failureCount++;
            
            // If tests appear to still be running in the output, continue polling
            // even if the status endpoint is returning 404
            if (testOutput && (testOutput.includes("Running") || testOutput.includes("Test in progress")) &&
                !testOutput.includes("=== Test Summary ===")) {
              console.warn(`Status check failed (${failureCount}/${MAX_FAILURES}), but tests still appear to be running`);
            } 
            // If we've reached max failures, stop polling
            else if (failureCount >= MAX_FAILURES) {
              console.error("Too many consecutive status check failures, stopping poll");
              clearInterval(interval);
              setStatusPolling(null);
              
              // If we've already got output, assume tests completed
              if (testOutput && testOutput.includes("=== Test Summary ===")) {
                setActiveTestRun(null);
                
                // Try to extract test summary from the output
                try {
                  const summaryMatch = testOutput.match(/===\s*Test Summary\s*===[\s\S]*Pass rate:\s*(\d+)%/);
                  if (summaryMatch) {
                    const passRateStr = summaryMatch[1];
                    const passRate = parseInt(passRateStr, 10);
                    const totalMatch = testOutput.match(/Total tests:\s*(\d+)/);
                    const passedMatch = testOutput.match(/Passed:\s*(\d+)/);
                    const failedMatch = testOutput.match(/Failed:\s*(\d+)/);
                    
                    setTestResults({
                      summary: {
                        total: totalMatch ? parseInt(totalMatch[1], 10) : 0,
                        passed: passedMatch ? parseInt(passedMatch[1], 10) : 0,
                        failed: failedMatch ? parseInt(failedMatch[1], 10) : 0,
                        passRate: isNaN(passRate) ? 0 : passRate
                      }
                    });
                    
                    toast({
                      title: "Tests completed",
                      description: "Test results have been processed",
                      variant: "default",
                    });
                  }
                } catch (err) {
                  console.error("Failed to parse test results from output:", err);
                }
              } else {
                toast({
                  title: "Test status tracking lost",
                  description: "We lost track of the test status, but tests may still be running in the background",
                  variant: "destructive",
                });
              }
            }
          }
        } catch (error) {
          console.error("Error polling test status:", error);
          failureCount++;
          if (failureCount >= MAX_FAILURES) {
            console.error("Too many consecutive errors, stopping poll");
            clearInterval(interval);
            setStatusPolling(null);
            
            toast({
              title: "Test monitoring failed",
              description: "Failed to monitor test status, but tests may continue running in the background",
              variant: "destructive",
            });
          }
        }
      }, 1000);
      
      setStatusPolling(interval);
      
      return () => {
        clearInterval(interval);
        setStatusPolling(null);
      };
    }
  }, [activeTestRun, testOutput, toast]);

  // Run tests mutation
  const runTestsMutation = useMutation({
    mutationFn: async () => {
      console.log(`Running tests with type: ${selectedTestType} and tests: ${selectedTests}`);
      
      const response = await apiRequest("POST", "/api/admin/tests/run", {
        testType: selectedTestType,
        tests: selectedTests.length > 0 ? selectedTests : undefined,
        config: {
          headless: config.headless,
          baseUrl: config.baseUrl,
          retries: config.retries,
          captureScreenshots: config.captureScreenshots,
          timeout: config.timeout,
          reporter: config.reporter,
          verbose: config.verbose
        },
      });
      
      if (!response.ok) {
        // Get error message from response if possible
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to start test execution");
        } catch (e) {
          throw new Error(`Failed to start test execution (${response.status})`);
        }
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Test execution started",
        description: "Tests are running in the background",
      });
      
      // Reset previous test results
      setTestResults(null);
      
      // Set active test run ID for polling
      setActiveTestRun(data.testRunId);
      setTestOutput("Starting tests...\n\n" + data.command + "\n\n");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Toggle test selection
  const toggleTest = (test: string) => {
    setSelectedTests((prev) => {
      if (prev.includes(test)) {
        return prev.filter((t) => t !== test);
      } else {
        return [...prev, test];
      }
    });
  };

  // Select all tests of current type
  const selectAllTests = () => {
    if (availableTests && availableTests[selectedTestType]) {
      setSelectedTests(availableTests[selectedTestType]);
    }
  };

  // Clear test selection
  const clearTests = () => {
    setSelectedTests([]);
  };
  
  // Format test name for display
  const formatTestName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };
  
  // Get icon for test type
  const getTestIcon = (testType: string) => {
    switch (testType) {
      case 'standard':
        return <Terminal className="h-4 w-4 mr-2" />;
      case 'ai':
        return <Cpu className="h-4 w-4 mr-2" />;
      case 'encryption':
        return <Lock className="h-4 w-4 mr-2" />;
      case 'specialized':
        return <Microscope className="h-4 w-4 mr-2" />;
      case 'all':
        return <Rocket className="h-4 w-4 mr-2" />;
      default:
        return <Bug className="h-4 w-4 mr-2" />;
    }
  };

  // Update configuration
  const updateConfig = (key: keyof TestConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Test Runner</h3>
      <Card className="border border-zinc-700">
        <CardHeader className="pb-3">
          <CardTitle>Run Automated Tests</CardTitle>
          <CardDescription>
            Execute test suites to validate the application functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="standard" onValueChange={(value) => {
              setSelectedTestType(value);
              if (value !== 'config') {
                // Reset selected tests when changing test type
                setSelectedTests([]);
              }
            }}>
            <TabsList className="mb-4">
              <TabsTrigger value="standard">
                <Terminal className="h-4 w-4 mr-2" />
                Standard Tests
              </TabsTrigger>
              <TabsTrigger value="ai">
                <Cpu className="h-4 w-4 mr-2" />
                AI Tests
              </TabsTrigger>
              <TabsTrigger value="encryption">
                <Lock className="h-4 w-4 mr-2" />
                Encryption Tests
              </TabsTrigger>
              <TabsTrigger value="specialized">
                <Microscope className="h-4 w-4 mr-2" />
                Specialized
              </TabsTrigger>
              <TabsTrigger value="all">
                <Rocket className="h-4 w-4 mr-2" />
                All Tests
              </TabsTrigger>
              <TabsTrigger value="config">
                <BarChart className="h-4 w-4 mr-2" />
                Configuration
              </TabsTrigger>
            </TabsList>
            
            {/* Standard Tests */}
            <TabsContent value="standard" className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Select tests to run:</h4>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={selectAllTests}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearTests}>
                    Clear
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                {testsLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : availableTests?.standard?.length === 0 ? (
                  <div className="col-span-2 text-center py-4 text-muted-foreground">
                    No standard tests available
                  </div>
                ) : (
                  availableTests?.standard?.map((test: string) => (
                    <div key={test} className="flex items-center space-x-2">
                      <Checkbox
                        id={`test-${test}`}
                        checked={selectedTests.includes(test)}
                        onCheckedChange={() => toggleTest(test)}
                      />
                      <label
                        htmlFor={`test-${test}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {formatTestName(test)}
                      </label>
                    </div>
                  ))
                )}
              </div>
            </TabsContent>
            
            {/* AI Tests */}
            <TabsContent value="ai" className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">AI Integration Tests:</h4>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={selectAllTests}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearTests}>
                    Clear
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                {testsLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : availableTests?.ai?.length === 0 ? (
                  <div className="col-span-2 text-center py-4 text-muted-foreground">
                    No AI tests available
                  </div>
                ) : (
                  availableTests?.ai?.map((test: string) => (
                    <div key={test} className="flex items-center space-x-2">
                      <Checkbox
                        id={`test-${test}`}
                        checked={selectedTests.includes(test)}
                        onCheckedChange={() => toggleTest(test)}
                      />
                      <label
                        htmlFor={`test-${test}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {formatTestName(test)}
                      </label>
                    </div>
                  ))
                )}
              </div>
            </TabsContent>
            
            {/* Encryption Tests */}
            <TabsContent value="encryption" className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Encryption Tests:</h4>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={selectAllTests}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearTests}>
                    Clear
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                {testsLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : availableTests?.encryption?.length === 0 ? (
                  <div className="col-span-2 text-center py-4 text-muted-foreground">
                    No encryption tests available
                  </div>
                ) : (
                  availableTests?.encryption?.map((test: string) => (
                    <div key={test} className="flex items-center space-x-2">
                      <Checkbox
                        id={`test-${test}`}
                        checked={selectedTests.includes(test)}
                        onCheckedChange={() => toggleTest(test)}
                      />
                      <label
                        htmlFor={`test-${test}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {formatTestName(test)}
                      </label>
                    </div>
                  ))
                )}
              </div>
            </TabsContent>
            
            {/* Specialized Tests */}
            <TabsContent value="specialized" className="space-y-4">
              <Alert className="bg-violet-950/20 border-violet-600/30 mb-4">
                <AlertDescription className="text-violet-400">
                  Specialized tests are in-depth tests that focus on specific advanced functionality.
                  These tests might take longer to run but provide deeper validation.
                </AlertDescription>
              </Alert>
              
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Specialized Tests:</h4>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={selectAllTests}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearTests}>
                    Clear
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                {testsLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : availableTests?.specialized?.length === 0 ? (
                  <div className="col-span-2 text-center py-4 text-muted-foreground">
                    No specialized tests available
                  </div>
                ) : (
                  availableTests?.specialized?.map((test: string) => (
                    <div key={test} className="flex items-center space-x-2">
                      <Checkbox
                        id={`specialized-test-${test}`}
                        checked={selectedTests.includes(test)}
                        onCheckedChange={() => toggleTest(test)}
                      />
                      <label
                        htmlFor={`specialized-test-${test}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {formatTestName(test)}
                      </label>
                    </div>
                  ))
                )}
              </div>
            </TabsContent>
            
            {/* All Tests */}
            <TabsContent value="all" className="space-y-4">
              <Alert className="bg-yellow-950/20 border-yellow-600/30 mb-4">
                <AlertDescription className="text-yellow-400">
                  Running all tests will execute standard, AI, and encryption tests sequentially.
                  This may take some time to complete.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-4">
                <div className="grid gap-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center">
                      <Terminal className="h-4 w-4 mr-2" /> Standard Tests
                    </span>
                    <Badge variant="outline" className="ml-auto">
                      {availableTests?.standard?.length || 0} tests
                    </Badge>
                  </div>
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center">
                      <Cpu className="h-4 w-4 mr-2" /> AI Tests
                    </span>
                    <Badge variant="outline" className="ml-auto">
                      {availableTests?.ai?.length || 0} tests
                    </Badge>
                  </div>
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center">
                      <Lock className="h-4 w-4 mr-2" /> Encryption Tests
                    </span>
                    <Badge variant="outline" className="ml-auto">
                      {availableTests?.encryption?.length || 0} tests
                    </Badge>
                  </div>
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center">
                      <Microscope className="h-4 w-4 mr-2" /> Specialized Tests
                    </span>
                    <Badge variant="outline" className="ml-auto">
                      {availableTests?.specialized?.length || 0} tests
                    </Badge>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            {/* Configuration */}
            <TabsContent value="config" className="space-y-4">
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Headless Mode</span>
                  <Switch
                    checked={config.headless}
                    onCheckedChange={(checked) => updateConfig('headless', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Base URL</label>
                  <Input
                    value={config.baseUrl}
                    onChange={(e) => updateConfig('baseUrl', e.target.value)}
                    placeholder="http://localhost:3000"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Retries</label>
                  <Input
                    type="number"
                    min="0"
                    max="5"
                    value={config.retries}
                    onChange={(e) => updateConfig('retries', parseInt(e.target.value))}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Timeout (ms)</label>
                  <Input
                    type="number"
                    min="5000"
                    max="120000"
                    step="5000"
                    value={config.timeout}
                    onChange={(e) => updateConfig('timeout', parseInt(e.target.value))}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Reporter Type</label>
                  <Select
                    value={config.reporter}
                    onValueChange={(value) => updateConfig('reporter', value as 'json' | 'console' | 'all')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select reporter" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All (Console + JSON)</SelectItem>
                      <SelectItem value="console">Console Only</SelectItem>
                      <SelectItem value="json">JSON Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Capture Screenshots</span>
                  <Switch
                    checked={config.captureScreenshots}
                    onCheckedChange={(checked) => updateConfig('captureScreenshots', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Verbose Logging</span>
                  <Switch
                    checked={config.verbose}
                    onCheckedChange={(checked) => updateConfig('verbose', checked)}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="mt-6">
            <Button
              onClick={() => runTestsMutation.mutate()}
              disabled={runTestsMutation.isPending || !!activeTestRun}
              className="w-full"
            >
              {runTestsMutation.isPending || activeTestRun ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running {getTestIcon(selectedTestType)} Tests...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Run {getTestIcon(selectedTestType)} Tests
                </>
              )}
            </Button>
          </div>
          
          {/* Test Results/Output */}
          {testOutput && (
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">Test Output</h4>
                {activeTestRun && (
                  <div className="flex items-center text-yellow-500">
                    <Loader2 className="animate-spin mr-1 h-4 w-4" />
                    <span className="text-xs">Test in progress...</span>
                  </div>
                )}
              </div>
              
              {/* Test Progress if available */}
              {testResults && testResults.progress && (
                <div className="mb-3 p-3 border rounded-md bg-zinc-800/50" 
                     style={{ 
                       borderColor: activeTestRun ? 'rgba(202, 138, 4, 0.5)' : 
                                   (testResults.status === 'success' ? 'rgba(34, 197, 94, 0.5)' : 
                                    testResults.status === 'failed' ? 'rgba(239, 68, 68, 0.5)' : 
                                    testResults.status === 'error' ? 'rgba(239, 68, 68, 0.5)' : 'rgba(115, 115, 115, 0.5)')
                     }}>
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium flex items-center">
                      {activeTestRun ? (
                        <Activity className="h-4 w-4 mr-2 text-yellow-500" />
                      ) : testResults.status === 'success' ? (
                        <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      ) : testResults.status === 'failed' || testResults.status === 'error' ? (
                        <XCircle className="h-4 w-4 mr-2 text-red-500" />
                      ) : (
                        <Activity className="h-4 w-4 mr-2 text-yellow-500" />
                      )}
                      {activeTestRun ? "Progress" : "Test Summary"}
                    </h5>
                    <Badge variant={activeTestRun ? "outline" : 
                                    testResults.status === 'success' ? "default" : 
                                    testResults.status === 'failed' ? "destructive" : 
                                    testResults.status === 'error' ? "destructive" : "outline"} 
                           className={`ml-auto ${testResults.status === 'success' ? 'bg-green-900/50 hover:bg-green-900/30 text-green-300' : ''}`}>
                      {testResults.progress.completed} / {testResults.progress.total} 
                      {!activeTestRun && testResults.summary && ` (${testResults.summary.passRate}% pass rate)`}
                    </Badge>
                  </div>
                  
                  <Progress 
                    value={(testResults.progress.completed / testResults.progress.total) * 100} 
                    className={`h-2 mb-2 ${
                      !activeTestRun && testResults.status === 'success' ? 'bg-green-950/30' : 
                      !activeTestRun && (testResults.status === 'failed' || testResults.status === 'error') ? 'bg-red-950/30' : 
                      'bg-yellow-950/30'
                    }`}
                  />
                  
                  <div className="text-xs text-muted-foreground mt-2">
                    <p>
                      <span className="font-medium text-yellow-500">Current Test:</span> {testResults.progress.currentTest}
                    </p>
                    {testResults.progress.currentSuite && (
                      <p>
                        <span className="font-medium text-yellow-500">Suite:</span> {testResults.progress.currentSuite}
                      </p>
                    )}
                    
                    {!activeTestRun && testResults.duration && (
                      <p className="mt-1">
                        <span className="font-medium text-yellow-500">Duration:</span> {formatDuration(testResults.duration)}
                      </p>
                    )}
                    
                    {!activeTestRun && testResults.status && (
                      <div className="mt-2 pt-2 border-t border-zinc-700">
                        <span className="font-medium text-yellow-500">Status:</span>{' '}
                        <span className={
                          testResults.status === 'success' ? 'text-green-500' : 
                          testResults.status === 'failed' || testResults.status === 'error' ? 'text-red-500' : 
                          'text-yellow-500'
                        }>
                          {testResults.status.charAt(0).toUpperCase() + testResults.status.slice(1)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Test Summary if available */}
              {testResults && testResults.summary && (
                <div className="mb-3 p-3 border rounded-md bg-zinc-800/50">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium flex items-center">
                      {testResults.summary.failed > 0 ? (
                        <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                      ) : (
                        <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      )}
                      Test Summary
                    </h5>
                    <Badge 
                      variant={testResults.summary.failed > 0 ? "destructive" : "default"}
                      className="ml-auto"
                    >
                      {testResults.summary.passRate}% Pass Rate
                    </Badge>
                  </div>
                  <div className="grid grid-cols-4 gap-2 text-sm">
                    <div className="flex flex-col items-center p-2 bg-zinc-800 rounded">
                      <span className="text-xs text-zinc-400">Total</span>
                      <span className="font-mono text-lg">{testResults.summary.total}</span>
                    </div>
                    <div className="flex flex-col items-center p-2 bg-green-900/30 text-green-300 rounded">
                      <span className="text-xs opacity-80">Passed</span>
                      <span className="font-mono text-lg">{testResults.summary.passed}</span>
                    </div>
                    <div className="flex flex-col items-center p-2 bg-red-900/30 text-red-300 rounded">
                      <span className="text-xs opacity-80">Failed</span>
                      <span className="font-mono text-lg">{testResults.summary.failed}</span>
                    </div>
                    <div className="flex flex-col items-center p-2 bg-zinc-800 rounded">
                      <span className="text-xs text-zinc-400">Duration</span>
                      <span className="font-mono text-lg">
                        {testResults.duration 
                          ? `${Math.round(testResults.duration / 1000)}s`
                          : testResults.summary.duration 
                            ? `${Math.round(testResults.summary.duration / 1000)}s`
                            : '--'}
                      </span>
                    </div>
                  </div>
                  
                  {/* Show status information if available */}
                  {testResults.status && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      <div className={`flex items-center ${
                        testResults.status === 'success' ? 'text-green-500' : 
                        testResults.status === 'error' ? 'text-red-500' : 
                        testResults.status === 'failed' ? 'text-amber-500' : 'text-blue-500'
                      }`}>
                        <span className="font-semibold mr-1">Status:</span> 
                        {testResults.status.charAt(0).toUpperCase() + testResults.status.slice(1)}
                      </div>
                    </div>
                  )}
                  
                  {/* Show timestamps if available */}
                  {(testResults.startedAt || testResults.completedAt) && (
                    <div className="mt-2 text-xs grid grid-cols-2 gap-2 text-muted-foreground">
                      {testResults.startedAt && (
                        <div>
                          <span className="font-semibold">Started:</span> {new Date(testResults.startedAt).toLocaleTimeString()}
                        </div>
                      )}
                      {testResults.completedAt && (
                        <div>
                          <span className="font-semibold">Completed:</span> {new Date(testResults.completedAt).toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Show error information if available */}
                  {testResults.errors && (
                    <div className="mt-2 p-2 bg-red-900/20 border border-red-700 rounded text-xs text-red-300">
                      <span className="font-semibold">Error:</span> {testResults.errors}
                    </div>
                  )}
                </div>
              )}
              
              {/* Raw test output */}
              <ScrollArea className="h-[300px] w-full border rounded-md p-4 bg-zinc-950">
                <pre className="text-xs font-mono text-zinc-100 whitespace-pre-wrap">
                  {testOutput}
                </pre>
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}