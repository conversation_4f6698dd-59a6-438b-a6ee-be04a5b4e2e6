import { useAuth } from "@/hooks/use-auth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Terminal, LogOut } from "lucide-react";
import { cn } from "@/lib/utils";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useLocation } from "wouter";
import { NotificationDropdown } from "@/components/notifications/notification-dropdown";

interface TerminalLayoutProps {
  children: React.ReactNode;
}

export function TerminalLayout({ children }: TerminalLayoutProps) {
  const { user, logoutMutation } = useAuth();
  const [location, setLocation] = useLocation();

  // Get the current tab based on location
  const getCurrentTab = () => {
    if (location === '/forum') return 'forum';
    if (location === '/messages') return 'messages';
    if (location === '/ai') return 'ai';
    if (location === '/profile') return 'profile';
    if (location === '/friends') return 'friends';
    if (location === '/chat') return 'chat';
    if (location === '/news') return 'news';
    if (location === '/admin') return 'admin';
    // Check if viewing another user's profile - should show Friends tab as active
    if (location.startsWith('/profile/')) return 'friends';
    return 'forum';
  };

  return (
    <div className="min-h-screen">
      <header className="border-b border-text p-4">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Terminal className="h-6 w-6 text-text" />
            <span className="text-xl font-bold text-glow">Crow-AI</span>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-text">Logged in as: {user?.username}</span>
            <NotificationDropdown />
            <Button
              variant="outline"
              size="sm"
              onClick={() => logoutMutation.mutate()}
              disabled={logoutMutation.isPending}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto py-6 px-4">
        <div className="space-y-4">
          <Tabs defaultValue={getCurrentTab()} value={getCurrentTab()} className="w-full">
            <TabsList className={cn("grid w-full", user?.isAdmin ? "grid-cols-8" : "grid-cols-7")}>
              <TabsTrigger value="forum" onClick={() => setLocation("/forum")}>Forum</TabsTrigger>
              <TabsTrigger value="messages" onClick={() => setLocation("/messages")}>Messages</TabsTrigger>
              <TabsTrigger value="chat" onClick={() => setLocation("/chat")}>Chat Room</TabsTrigger>
              <TabsTrigger value="ai" onClick={() => setLocation("/ai")}>AI Chat</TabsTrigger>
              <TabsTrigger value="news" onClick={() => setLocation("/news")}>Fresh News</TabsTrigger>
              <TabsTrigger value="friends" onClick={() => setLocation("/friends")}>Friends</TabsTrigger>
              <TabsTrigger value="profile" onClick={() => setLocation("/profile")}>Profile</TabsTrigger>
              {user?.isAdmin && (
                <TabsTrigger value="admin" onClick={() => setLocation("/admin")}>Admin</TabsTrigger>
              )}
            </TabsList>
          </Tabs>
          <div className="page-fade-in">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}
