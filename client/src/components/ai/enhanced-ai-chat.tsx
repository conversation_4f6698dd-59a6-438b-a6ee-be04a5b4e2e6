import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, ChangeEvent } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Send, 
  RefreshCw, 
  Trash, 
  UploadCloud, 
  Plus, 
  Menu, 
  X, 
  Brain, 
  MessageSquare, 
  Clock,
  FileText,
  Save,
  Settings,
  Zap,
  Bot,
  User,
  Sparkles,
  Activity,
  ChevronDown,
  Cpu,
  Globe,
  BarChart3,
  TrendingUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSoundEffects } from '@/lib/sounds';
import { clientLogger } from '@/lib/logger';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Dialog, 
  DialogContent, 
  Di<PERSON>Header, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter 
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/hooks/use-auth';

interface Message {
  id: string;
  role: 'system' | 'user' | 'assistant';
  content: string;
  error?: boolean;
}

interface Attachment {
  fileName: string;
  fileType: string;
  content: string;
}

interface ChatSession {
  id: number;
  title: string;
  provider: string;
  model: string;
  totalTokens: number;
  maxTokens: number;
  createdAt: string;
  updatedAt: string;
}

interface ChatMessage {
  id: number;
  message: string;
  response: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

interface TokenUsage {
  totalTokens: number;
  maxTokens: number;
  remainingTokens: number;
  provider: string;
  usagePercentage: number;
}

type AIProvider = 'deepseek' | 'openrouter';

interface ProviderModel {
  value: string;
  label: string;
  description?: string;
}

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;
const MAX_TITLE_LENGTH = 50;

let messageCounter = 0;
const generateMessageId = () => `msg-${Date.now()}-${messageCounter++}`;

export function EnhancedAIChat() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeepThinking, setIsDeepThinking] = useState(false);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [isSessionsLoading, setIsSessionsLoading] = useState(false);
  const [isSessionDialogOpen, setIsSessionDialogOpen] = useState(false);
  const [newSessionTitle, setNewSessionTitle] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<AIProvider>('deepseek');
  const [selectedModel, setSelectedModel] = useState<string>('deepseek-chat');
  const [tokenUsage, setTokenUsage] = useState<TokenUsage | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  
  const providerModels: Record<AIProvider, ProviderModel[]> = {
    deepseek: [
      { value: 'deepseek-chat', label: 'DeepSeek Chat', description: 'General purpose model' },
      { value: 'deepseek-reasoner', label: 'DeepSeek Reasoner', description: 'Enhanced reasoning model' }
    ],
    openrouter: [
      { value: 'qwen/qwen3-235b-a22b:free', label: 'Qwen 3 235B (Free)', description: 'Large language model by Alibaba' },
      { value: 'tngtech/deepseek-r1t2-chimera:free', label: 'DeepSeek R1T2 Chimera (Free)', description: 'Advanced reasoning model' },
      { value: 'cognitivecomputations/dolphin-mistral-24b-venice-edition:free', label: 'Dolphin Mistral 24B Venice (Free)', description: 'Creative and uncensored model' },
      { value: 'microsoft/mai-ds-r1:free', label: 'Microsoft MAI DS R1 (Free)', description: 'Microsoft AI model' },
      { value: 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free', label: 'Llama 3.1 Nemotron Ultra 253B (Free)', description: 'Ultra-large NVIDIA model' }
    ]
  };
  
  const scrollRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { playSound } = useSoundEffects();
  const abortControllerRef = useRef<AbortController | null>(null);
  const readerRef = useRef<ReadableStreamDefaultReader<Uint8Array> | null>(null);

  // Load chat sessions on component mount
  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    if (!user) return;
    
    setIsSessionsLoading(true);
    try {
      const response = await fetch('/api/ai/sessions');
      if (!response.ok) {
        throw new Error(`Failed to load sessions: ${response.status}`);
      }
      
      const sessionsData: ChatSession[] = await response.json();
      setSessions(sessionsData || []);
      
      // If there are sessions and no current session, set the most recent one
      if (sessionsData.length > 0 && !currentSession) {
        const mostRecentSession = sessionsData[0];
        setCurrentSession(mostRecentSession);
        setSelectedProvider(mostRecentSession.provider as AIProvider);
        setSelectedModel(mostRecentSession.model);
        loadSessionMessages(mostRecentSession.id);
      }
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your chat sessions',
        variant: 'destructive'
      });
    } finally {
      setIsSessionsLoading(false);
    }
  };

  const loadSessionMessages = async (sessionId: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/ai/sessions/${sessionId}/messages`);
      if (!response.ok) {
        throw new Error(`Failed to load session messages: ${response.status}`);
      }
      
      const sessionMessages: ChatMessage[] = await response.json();
      
      if (sessionMessages.length) {
        // Convert to the message format expected by the chat component
        const formattedMessages = sessionMessages.map((msg: ChatMessage, index: number) => {
          const isUserMessage = index % 2 === 0;
          return {
            id: generateMessageId(),
            role: isUserMessage ? 'user' as const : 'assistant' as const,
            content: isUserMessage ? msg.message : msg.response
          };
        });
        
        setMessages(formattedMessages);
      } else {
        setMessages([]);
      }
      
      // Update token usage for the current session
      const session = sessions.find(s => s.id === sessionId) || currentSession;
      if (session) {
        setTokenUsage({
          totalTokens: session.totalTokens,
          maxTokens: session.maxTokens,
          remainingTokens: session.maxTokens - session.totalTokens,
          provider: session.provider,
          usagePercentage: (session.totalTokens / session.maxTokens) * 100
        });
      }
    } catch (error) {
      console.error('Failed to load session messages:', error);
      toast({
        title: 'Error',
        description: 'Failed to load chat messages for this session',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createNewSession = async () => {
    if (!user) return;
    
    try {
      const title = newSessionTitle.trim() || 'New Chat';
      
      const response = await fetch('/api/ai/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          title,
          provider: selectedProvider,
          model: selectedModel
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create session: ${response.status}`);
      }
      
      const newSession: ChatSession = await response.json();
      
      if (newSession) {
        setSessions(prev => [newSession, ...prev]);
        setCurrentSession(newSession);
        setMessages([]);
        setTokenUsage(null);
        setIsSessionDialogOpen(false);
        setNewSessionTitle('');
      }
    } catch (error) {
      console.error('Failed to create new chat session:', error);
      toast({
        title: 'Error',
        description: 'Failed to create a new chat session',
        variant: 'destructive'
      });
    }
  };

  const deleteSession = async (sessionId: number) => {
    if (!user) return;
    
    try {
      const response = await fetch(`/api/ai/sessions/${sessionId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete session: ${response.status}`);
      }
      
      // Update sessions list
      setSessions(prev => prev.filter(s => s.id !== sessionId));
      
      // If this was the current session, set a new current session
      if (currentSession?.id === sessionId) {
        const remaining = sessions.filter(s => s.id !== sessionId);
        if (remaining.length > 0) {
          setCurrentSession(remaining[0]);
          loadSessionMessages(remaining[0].id);
        } else {
          setCurrentSession(null);
          setMessages([]);
        }
      }
      
      toast({
        title: 'Success',
        description: 'Chat session deleted'
      });
    } catch (error) {
      console.error('Failed to delete chat session:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete chat session',
        variant: 'destructive'
      });
    }
  };

  const cleanup = useCallback(async () => {
    let cleanupSuccessful = true;
    
    // First handle the stream reader if it exists
    if (readerRef.current) {
      try {
        clientLogger.log('Canceling stream reader');
        await readerRef.current.cancel();
      } catch (error) {
        cleanupSuccessful = false;
        clientLogger.error('Error canceling reader', error);
      } finally {
        readerRef.current = null;
      }
    }

    // Then handle the abort controller
    if (abortControllerRef.current) {
      try {
        const controller = abortControllerRef.current;
        abortControllerRef.current = null; // Clear ref before aborting to prevent loops
        clientLogger.log('Aborting request');
        controller.abort('User cancelled request');
      } catch (error) {
        cleanupSuccessful = false;
        clientLogger.error('Error aborting request', error);
      }
    }
    
    setIsLoading(false);
    return cleanupSuccessful;
  }, []);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  const handleStopRequest = useCallback(() => {
    cleanup();
    setIsLoading(false);
    toast({
      title: 'Request stopped',
      description: 'The AI response generation was stopped.',
    });
  }, [cleanup, toast]);

  const clearChat = useCallback(() => {
    cleanup();
    setMessages([]);
  }, [cleanup]);

  const retryLastMessage = useCallback(
    (failedMsgId: string) => {
      const failedMsgIndex = messages.findIndex(m => m.id === failedMsgId);
      const lastUserMsg = messages
        .slice(0, failedMsgIndex)
        .reverse()
        .find(m => m.role === 'user');
      if (lastUserMsg) {
        sendMessage(lastUserMsg.content, failedMsgId);
      }
    },
    [messages]
  );

  const handleFileUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      Array.from(files).forEach(async (file) => {
        const reader = new FileReader();
        reader.onload = () => {
          const content = typeof reader.result === 'string' ? reader.result : '';
          const attachment: Attachment = {
            fileName: file.name,
            fileType: file.type,
            content
          };
          setAttachments(prev => [...prev, attachment]);
          
          // Show notification of file being attached
          toast({
            title: 'File Attached',
            description: `${file.name} ready to send with your message`
          });
        };
        if (file.type.startsWith('image/')) {
          reader.readAsDataURL(file);
        } else {
          reader.readAsText(file);
        }
      });
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const sendMessage = useCallback(
    async (messageText?: string, retryMsgId?: string, retryCount = 0) => {
      if (!user) {
        toast({
          title: 'Authentication Required',
          description: 'Please log in to use the AI chat feature.',
          variant: 'destructive',
        });
        return;
      }
      
      // Create a new session if none exists
      if (!currentSession) {
        try {
          const response = await fetch('/api/ai/sessions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              title: 'New Chat',
              provider: selectedProvider,
              model: selectedModel
            })
          });
          
          if (!response.ok) {
            throw new Error(`Failed to create session: ${response.status}`);
          }
          
          const newSession: ChatSession = await response.json();
          setCurrentSession(newSession);
          setSessions(prev => [newSession, ...prev]);
        } catch (error) {
          console.error('Failed to create new session:', error);
          toast({
            title: 'Error',
            description: 'Failed to create new chat session',
            variant: 'destructive',
          });
          return;
        }
      }
      
      await cleanup();
      if (retryCount >= MAX_RETRIES) {
        toast({
          title: 'Error',
          description: 'Maximum retry attempts reached. Please try again later.',
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        abortControllerRef.current = new AbortController();

        // Generate user message with input and attachments
        const userInput = messageText?.trim() || input.trim();
        const attachmentContent = attachments.map(att => (
          `Attachment: ${att.fileName} (${att.fileType})\n${att.content}`
        )).join('\n');
        const combinedContent = [userInput, attachmentContent].filter(Boolean).join('\n');

        // Create user message
        const userMessage: Message = {
          id: generateMessageId(),
          role: 'user',
          content: combinedContent
        };

        // Update messages state with user message
        setMessages(prev => [...prev, userMessage]);
        setInput('');
        setAttachments([]);

        // Prepare API request
        const payload = {
          messages: messages.concat(userMessage).filter(msg => !msg.error),
          stream: true,
          sessionId: currentSession?.id,
          isDeepThinking,
          provider: selectedProvider,
          model: selectedModel
        };

        clientLogger.log('Sending API Request', { payload });

        const response = await fetch('/api/ai/chat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
          throw new Error(errorData.error || `Server responded with ${response.status}`);
        }

        if (response.body) {
          clientLogger.log('Starting stream processing');
          const reader = response.body.getReader();
          readerRef.current = reader;
          const decoder = new TextDecoder();
          let partialLine = '';
          let fullContent = '';

          try {
            while (true) {
              const { value, done } = await reader.read();
              if (done) {
                clientLogger.log('Stream completed naturally');
                break;
              }

              const text = decoder.decode(value, { stream: true });
              partialLine += text;

              const lines = partialLine.split('\n');
              partialLine = lines.pop() || '';

              for (const line of lines) {
                // Skip empty lines
                if (!line.trim()) continue;
                
                if (line.startsWith('data: ')) {
                  const dataLine = line.slice(6);
                  if (dataLine === '[DONE]') {
                    clientLogger.log('Stream complete signal received');
                    continue;
                  }

                  try {
                    // Enhanced parsing with better error handling
                    const jsonData = JSON.parse(dataLine);
                    
                    // Support different formats between OpenAI and DeepSeek
                    let content = null;
                    if (jsonData.choices?.[0]?.delta?.content) {
                      // OpenAI style format
                      content = jsonData.choices[0].delta.content;
                    } else if (jsonData.choices?.[0]?.text) {
                      // Alternative format (some models)
                      content = jsonData.choices[0].text;
                    } else if (jsonData.data?.choices?.[0]?.delta?.content) {
                      // Nested data structure sometimes used
                      content = jsonData.data.choices[0].delta.content;
                    }
                    
                    if (content) {
                      fullContent += content;
                      const assistantMsg = { 
                        id: generateMessageId(),
                        role: 'assistant' as const,
                        content: fullContent
                      };
                      
                      setMessages(prev => {
                        const lastMsg = prev[prev.length - 1];
                        if (lastMsg?.role === 'assistant') {
                          return prev.map(msg => 
                            msg.id === lastMsg.id ? assistantMsg : msg
                          );
                        }
                        return [...prev, assistantMsg];
                      });
                    }
                  } catch (e) {
                    clientLogger.error('Error parsing SSE data', { error: e, line: dataLine });
                  }
                }
              }
            }
          } catch (error: any) {
            if (error.name === 'AbortError') {
              clientLogger.log('Stream reading aborted');
              return;
            }
            throw error;
          } finally {
            readerRef.current = null;
          }

          playSound('message-received');
          
          // If the current session doesn't have a proper title, update it with a summary of the first message
          if (currentSession && (currentSession.title === 'New Chat' || !currentSession.title) && userInput) {
            const truncatedTitle = userInput.length > MAX_TITLE_LENGTH 
              ? userInput.slice(0, MAX_TITLE_LENGTH) + '...' 
              : userInput;
            
            try {
              // Update session title based on first message
              await fetch(`/api/ai/sessions/${currentSession.id}`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ title: truncatedTitle })
              });
              
              // Update in local state
              setCurrentSession({
                ...currentSession,
                title: truncatedTitle
              });
              
              setSessions(prev => prev.map(s => 
                s.id === currentSession.id ? { ...s, title: truncatedTitle } : s
              ));
            } catch (error) {
              console.error('Failed to update session title:', error);
            }
          }
        }
      } catch (error: any) {
        // Improved error handling for aborted requests
        const isAbortError = 
          error.name === 'AbortError' || 
          error.message?.includes('aborted') || 
          error.message?.includes('canceled') ||
          error.message?.includes('cancelled');
        
        if (isAbortError) {
          clientLogger.log('Request aborted by user');
          // Don't show toast for user-initiated cancellations to reduce notification noise
          if (!abortControllerRef.current) {
            toast({
              title: 'Request Cancelled',
              description: 'The AI response was cancelled.',
            });
          }
          return; // Exit early for abort errors, no need for retries
        } else {
          const errorMessage = error.message || 'Failed to get AI response';
          clientLogger.error('Chat error', { error, errorMessage });
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
          playSound('error');

          // Add retry logic
          if (retryCount < MAX_RETRIES) {
            const delay = RETRY_DELAY * Math.pow(2, retryCount);
            toast({
              title: 'Retrying...',
              description: `Attempting retry in ${delay / 1000} seconds`,
            });
            setTimeout(() => {
              sendMessage(messageText, retryMsgId, retryCount + 1);
            }, delay);
          } else {
            const errorAssistantMessage: Message = {
              id: generateMessageId(),
              role: 'assistant',
              content: 'I encountered a temporary issue. Please try again or click the retry button.',
              error: true,
            };
            setMessages(prev => [...prev, errorAssistantMessage]);
          }
        }
      } finally {
        abortControllerRef.current = null;
        setIsLoading(false);
        setAttachments([]);
      }
    },
    [input, messages, attachments, playSound, toast, cleanup, currentSession, isDeepThinking, user]
  );

  const renderAttachmentList = () => {
    if (attachments.length === 0) return null;
    return (
      <div className="mb-4 space-y-2">
        <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
          <FileText className="h-3 w-3" />
          Attachments ({attachments.length})
        </div>
        <div className="flex flex-wrap gap-2">
          {attachments.map((attachment, index) => (
            <div key={index} className="flex items-center gap-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 p-3 rounded-xl text-sm transition-all hover:shadow-md">
              <div className="p-1 bg-primary/10 rounded-lg">
                <FileText className="h-3 w-3 text-primary" />
              </div>
              <span className="max-w-[180px] truncate font-medium">{attachment.fileName}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeAttachment(index)}
                className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive transition-colors"
                aria-label={`Remove ${attachment.fileName}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full min-h-[700px] gap-6 p-6 bg-gradient-to-br from-background via-background to-muted/30">
      {/* Enhanced Sidebar for session management */}
      <Card className="border-border/60 w-96 flex flex-col bg-gradient-to-br from-card to-card/80 backdrop-blur-sm shadow-xl">
        <CardHeader className="py-4 px-5 border-b border-border/60">
          <CardTitle className="text-glow flex items-center justify-between">
            <div className="flex items-center">
              <div className="p-2 bg-primary/10 rounded-xl mr-3">
                <Bot className="h-5 w-5 text-primary" />
              </div>
              <span className="font-bold">AI Sessions</span>
            </div>
            <Badge variant="secondary" className="font-medium">
              {sessions.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 min-h-0 p-4">
          <ScrollArea className="h-full pr-2">
            {isSessionsLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-20 bg-gradient-to-r from-muted to-muted/50 animate-pulse rounded-xl" />
                ))}
              </div>
            ) : sessions.length === 0 ? (
              <div className="text-center text-muted-foreground py-12">
                <div className="p-4 bg-primary/5 rounded-full w-fit mx-auto mb-4">
                  <MessageSquare className="h-8 w-8 text-primary/60" />
                </div>
                <h3 className="font-semibold mb-2">No conversations yet</h3>
                <p className="text-sm mb-4">Start your first AI conversation</p>
                <Button 
                  onClick={() => setIsSessionDialogOpen(true)}
                  className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Session
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {sessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-4 rounded-xl cursor-pointer transition-all duration-200 group border ${
                      currentSession?.id === session.id
                        ? 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-primary shadow-lg scale-[1.02]'
                        : 'bg-gradient-to-r from-card to-muted/20 hover:from-muted/40 hover:to-muted/20 border-border/40 hover:border-primary/20 hover:shadow-md'
                    }`}
                    onClick={() => {
                      setCurrentSession(session);
                      setSelectedProvider(session.provider as AIProvider);
                      setSelectedModel(session.model);
                      loadSessionMessages(session.id);
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold text-sm truncate">{session.title}</h4>
                          <Badge 
                            variant={currentSession?.id === session.id ? "secondary" : "outline"} 
                            className="text-xs px-2 py-0.5"
                          >
                            {session.provider === 'deepseek' ? (
                              <><Cpu className="h-3 w-3 mr-1" />DeepSeek</>
                            ) : (
                              <><Globe className="h-3 w-3 mr-1" />OpenRouter</>
                            )}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-xs opacity-80">
                          <span>{new Date(session.createdAt).toLocaleDateString()}</span>
                          <div className="flex items-center gap-1">
                            <Activity className="h-2 w-2" />
                            <span className="text-xs">{session.totalTokens.toLocaleString()}</span>
                          </div>
                        </div>
                        {session.totalTokens > 0 && (
                          <div className="mt-1">
                            <Progress 
                              value={(session.totalTokens / session.maxTokens) * 100} 
                              className="h-1" 
                            />
                          </div>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-background/20"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Menu className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteSession(session.id);
                            }}
                            className="text-destructive"
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Delete Session
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
        <CardFooter className="p-4 border-t border-border/60">
          <Button 
            variant="outline" 
            className="w-full bg-gradient-to-r from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/20 border-primary/20 transition-all duration-200"
            onClick={() => setIsSessionDialogOpen(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Conversation
          </Button>
        </CardFooter>
      </Card>

      {/* Enhanced Main chat area */}
      <Card className="border-border/60 flex-1 flex flex-col bg-gradient-to-br from-card to-card/80 backdrop-blur-sm shadow-xl">
        <CardHeader className="py-4 px-6 border-b border-border/60">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl">
                <Sparkles className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-glow text-lg font-bold">
                  {currentSession ? currentSession.title : 'AI Assistant'}
                </CardTitle>
                {currentSession && (
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="secondary" className="text-xs">
                      {currentSession.provider === 'deepseek' ? (
                        <><Cpu className="h-3 w-3 mr-1" />DeepSeek • {currentSession.model}</>
                      ) : (
                        <><Globe className="h-3 w-3 mr-1" />OpenRouter • {currentSession.model.split('/').pop()}</>
                      )}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setShowSettings(true)} 
                className="hover:bg-primary/10 transition-colors"
                aria-label="Settings"
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={clearChat} 
                className="hover:bg-destructive/10 hover:text-destructive transition-colors"
                aria-label="Clear Chat"
              >
                <Trash className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                asChild
                className="hover:bg-primary/10 transition-colors"
              >
                <label htmlFor="file-upload" className="cursor-pointer">
                  <UploadCloud className="h-4 w-4" />
                </label>
              </Button>
              <input
                id="file-upload"
                type="file"
                multiple
                className="hidden"
                onChange={handleFileUpload}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col min-h-0 p-6 pt-4">
          {/* Compact Token Usage Display */}
          {tokenUsage && (
            <div className="mb-3 p-2 bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-1">
                  <BarChart3 className="h-3 w-3 text-primary" />
                  <span className="font-medium text-xs">Tokens</span>
                </div>
                <span className="font-mono text-xs">
                  {tokenUsage.totalTokens.toLocaleString()} / {tokenUsage.maxTokens.toLocaleString()}
                </span>
              </div>
              <Progress 
                value={tokenUsage.usagePercentage} 
                className={`h-2 mb-1 ${
                  tokenUsage.usagePercentage > 90 ? 'bg-destructive/20' : 
                  tokenUsage.usagePercentage > 70 ? 'bg-yellow-500/20' : 'bg-primary/20'
                }`}
              />
              <div className="flex justify-between items-center text-xs">
                <Badge variant="outline" className="text-xs px-1 py-0">
                  {tokenUsage.provider === 'deepseek' ? (
                    <><Cpu className="h-2 w-2 mr-1" />DeepSeek</>
                  ) : (
                    <><Globe className="h-2 w-2 mr-1" />OpenRouter</>
                  )}
                </Badge>
                <span className={`text-xs ${
                  tokenUsage.usagePercentage > 90 ? 'text-destructive' : 
                  tokenUsage.usagePercentage > 70 ? 'text-yellow-600' : 'text-muted-foreground'
                }`}>
                  {tokenUsage.remainingTokens.toLocaleString()} left
                </span>
              </div>
            </div>
          )}
          <ScrollArea className="flex-1 min-h-0 pr-4">
            <div ref={scrollRef} className="space-y-6 pb-6">
              {messages.map((msg) => (
                msg.role !== 'system' && (
                  <div
                    key={msg.id}
                    className={`flex items-start gap-3 ${
                      msg.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                    }`}
                  >
                    {/* Avatar */}
                    <div className={`p-2 rounded-xl flex-shrink-0 ${
                      msg.role === 'user' 
                        ? 'bg-gradient-to-r from-primary to-primary/80' 
                        : 'bg-gradient-to-r from-secondary to-muted'
                    }`}>
                      {msg.role === 'user' ? (
                        <User className="h-4 w-4 text-primary-foreground" />
                      ) : (
                        <Bot className="h-4 w-4 text-foreground" />
                      )}
                    </div>
                    
                    {/* Message bubble */}
                    <div className={`max-w-[75%] break-words overflow-wrap-anywhere ${
                      msg.role === 'user'
                        ? 'bg-gradient-to-r from-primary to-primary/90 text-primary-foreground rounded-2xl rounded-tr-md'
                        : 'bg-gradient-to-r from-muted to-muted/60 border border-border/40 rounded-2xl rounded-tl-md'
                    } p-4 shadow-sm`}>
                      <div className="whitespace-pre-wrap leading-relaxed">{msg.content}</div>
                      {msg.error && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-3 bg-background/80 hover:bg-background"
                          onClick={() => retryLastMessage(msg.id)}
                          aria-label="Retry Message"
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Retry
                        </Button>
                      )}
                    </div>
                  </div>
                )
              ))}
              {isLoading && (
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-gradient-to-r from-secondary to-muted rounded-xl animate-pulse">
                    <Bot className="h-4 w-4 text-foreground" />
                  </div>
                  <div className="bg-gradient-to-r from-muted to-muted/60 border border-border/40 rounded-2xl rounded-tl-md p-4 shadow-sm max-w-[75%] flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <div className="animate-spin">
                        <Brain className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">
                        {isDeepThinking ? "Thinking deeply..." : "Analyzing..."}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-background/80 hover:bg-background"
                      onClick={handleStopRequest}
                      aria-label="Stop Generating"
                    >
                      Stop
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
          {renderAttachmentList()}
          <div className="mt-6">
            <Tabs defaultValue="regular" className="w-full">
              <TabsList className="grid grid-cols-2 mb-4 bg-muted/50 p-1 rounded-xl">
                <TabsTrigger value="regular" className="flex items-center data-[state=active]:bg-background data-[state=active]:shadow-sm rounded-lg transition-all">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Quick Chat
                </TabsTrigger>
                <TabsTrigger value="deep" className="flex items-center data-[state=active]:bg-background data-[state=active]:shadow-sm rounded-lg transition-all">
                  <Brain className="h-4 w-4 mr-2" />
                  Deep Analysis
                </TabsTrigger>
              </TabsList>
              <TabsContent value="regular" className="space-y-3">
                <div className="flex gap-3">
                  <div className="flex-1">
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="Type your message..."
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          setIsDeepThinking(false);
                          void sendMessage();
                        }
                      }}
                      disabled={isLoading}
                      className="bg-background/60 border-border/60 focus:border-primary/50 transition-colors rounded-xl h-12 px-4"
                      aria-label="Chat Input"
                    />
                  </div>
                  <Button 
                    onClick={() => {
                      setIsDeepThinking(false);
                      void sendMessage();
                    }} 
                    disabled={isLoading || !input.trim()} 
                    className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 h-12 px-6 rounded-xl shadow-lg"
                    aria-label="Send Message"
                  >
                    {isLoading ? (
                      <div className="animate-spin w-5 h-5 border-2 border-primary-foreground rounded-full border-t-transparent" />
                    ) : (
                      <Send className="h-5 w-5" />
                    )}
                  </Button>
                </div>
              </TabsContent>
              <TabsContent value="deep" className="space-y-3">
                <div className="flex gap-3">
                  <div className="flex-1">
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="Ask for detailed analysis..."
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey && e.ctrlKey) {
                          e.preventDefault();
                          setIsDeepThinking(true);
                          void sendMessage();
                        }
                      }}
                      disabled={isLoading}
                      className="bg-background/60 border-border/60 focus:border-primary/50 transition-colors rounded-xl h-12 px-4"
                      aria-label="Deep Thinking Input"
                    />
                  </div>
                  <Button 
                    onClick={() => {
                      setIsDeepThinking(true);
                      void sendMessage();
                    }} 
                    disabled={isLoading || !input.trim()} 
                    className="bg-gradient-to-r from-secondary to-muted hover:from-muted hover:to-secondary h-12 px-6 rounded-xl shadow-lg"
                    aria-label="Think Deeply"
                  >
                    {isLoading ? (
                      <div className="animate-spin w-5 h-5 border-2 border-foreground rounded-full border-t-transparent" />
                    ) : (
                      <Brain className="h-5 w-5" />
                    )}
                  </Button>
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted/30 p-3 rounded-lg">
                  <Clock className="h-3 w-3" />
                  <span>Deep analysis mode provides comprehensive, detailed responses (Ctrl+Enter to send)</span>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>
      
      {/* Enhanced New Session Dialog */}
      <Dialog open={isSessionDialogOpen} onOpenChange={setIsSessionDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Plus className="h-4 w-4 text-primary" />
              </div>
              Create New Conversation
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-5 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Session Title
              </label>
              <Input
                placeholder="Enter conversation title..."
                value={newSessionTitle}
                onChange={(e) => setNewSessionTitle(e.target.value)}
                className="bg-background/60 border-border/60 focus:border-primary/50 rounded-lg"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Cpu className="h-4 w-4" />
                AI Provider
              </label>
              <Select value={selectedProvider} onValueChange={(value) => {
                setSelectedProvider(value as AIProvider);
                const models = providerModels[value as AIProvider];
                if (models.length > 0) {
                  setSelectedModel(models[0].value);
                }
              }}>
                <SelectTrigger className="bg-background/60 border-border/60 focus:border-primary/50 rounded-lg">
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="deepseek">
                    <div className="flex items-center gap-2">
                      <Cpu className="h-4 w-4" />
                      <div>
                        <div className="font-medium">DeepSeek</div>
                        <div className="text-xs text-muted-foreground">Advanced reasoning models</div>
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="openrouter">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <div>
                        <div className="font-medium">OpenRouter</div>
                        <div className="text-xs text-muted-foreground">Gateway to multiple AI models</div>
                      </div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Model Selection
              </label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="bg-background/60 border-border/60 focus:border-primary/50 rounded-lg">
                  <SelectValue placeholder="Choose model" />
                </SelectTrigger>
                <SelectContent>
                  {providerModels[selectedProvider].map((model) => (
                    <SelectItem key={model.value} value={model.value}>
                      <div className="py-1">
                        <div className="font-medium">{model.label}</div>
                        {model.description && (
                          <div className="text-xs text-muted-foreground mt-0.5">{model.description}</div>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter className="gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsSessionDialogOpen(false)}
              className="border-border/60"
            >
              Cancel
            </Button>
            <Button 
              onClick={createNewSession}
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Session
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Enhanced Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Settings className="h-4 w-4 text-primary" />
              </div>
              AI Chat Settings
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  Provider
                </label>
                <Select value={selectedProvider} onValueChange={(value) => {
                  setSelectedProvider(value as AIProvider);
                  const models = providerModels[value as AIProvider];
                  if (models.length > 0) {
                    setSelectedModel(models[0].value);
                  }
                }}>
                  <SelectTrigger className="bg-background/60 border-border/60">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="deepseek">
                      <div className="flex items-center gap-2">
                        <Cpu className="h-4 w-4" />
                        DeepSeek
                      </div>
                    </SelectItem>
                    <SelectItem value="openrouter">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        OpenRouter
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  Model
                </label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger className="bg-background/60 border-border/60">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {providerModels[selectedProvider].map((model) => (
                      <SelectItem key={model.value} value={model.value}>
                        <div className="py-1">
                          <div className="font-medium text-sm">{model.label}</div>
                          {model.description && (
                            <div className="text-xs text-muted-foreground">{model.description}</div>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {currentSession && tokenUsage && (
              <div className="space-y-3">
                <label className="text-sm font-medium flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Session Statistics
                </label>
                <div className="p-3 bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-lg">
                  <div className="grid grid-cols-2 gap-3 mb-2">
                    <div className="text-center">
                      <div className="text-sm font-bold text-primary">
                        {tokenUsage.totalTokens.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">Used</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-bold text-muted-foreground">
                        {tokenUsage.remainingTokens.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">Left</div>
                    </div>
                  </div>
                  <Progress 
                    value={tokenUsage.usagePercentage} 
                    className={`h-2 ${
                      tokenUsage.usagePercentage > 90 ? 'bg-destructive/20' : 'bg-primary/20'
                    }`}
                  />
                  <div className="flex justify-between items-center mt-1 text-xs">
                    <span className="text-muted-foreground">0</span>
                    <span className={tokenUsage.usagePercentage > 90 ? 'text-destructive font-medium' : 'text-muted-foreground'}>
                      {Math.round(tokenUsage.usagePercentage)}%
                    </span>
                    <span className="text-muted-foreground">{tokenUsage.maxTokens.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowSettings(false)}
              className="border-border/60"
            >
              <X className="h-4 w-4 mr-2" />
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}