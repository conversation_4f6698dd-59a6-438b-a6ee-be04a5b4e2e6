import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSoundEffects } from "@/lib/sounds";
import { useDebounce } from "use-debounce";

const DEFAULT_ASCII_PATTERNS = [
  "( ͡° ͜ʖ ͡°)",
  "¯\\_(ツ)_/¯",
  "(╯°□°)╯",
  "ಠ_ಠ",
  "(⌐■_■)",
  "ʕ•ᴥ•ʔ",
  "(ง°ل͜°)ง",
  "ᕕ(ᐛ)ᕗ",
  "ಥ_ಥ",
  "¯\\(°_o)/¯",
];

const MAX_AVATAR_LENGTH = 50;

interface AsciiAvatarGeneratorProps {
  currentAvatar?: string;
  onAvatarSelect?: (avatar: string) => void;
}

export function AsciiAvatarGenerator({ currentAvatar, onAvatarSelect }: AsciiAvatarGeneratorProps) {
  const [customText, setCustomText] = useState("");
  const [selectedPattern, setSelectedPattern] = useState(currentAvatar || DEFAULT_ASCII_PATTERNS[0]);
  const debouncedCustomText = useDebounce(customText, 500);
  const { playSound } = useSoundEffects();

  // Sync with currentAvatar from profile page
  useEffect(() => {
    if (currentAvatar) {
      setSelectedPattern(currentAvatar);
      setCustomText(""); // Reset custom text when currentAvatar changes
    }
  }, [currentAvatar]);

  // Debounced API call for custom text
  useEffect(() => {
    const updateAvatar = async () => {
      if (debouncedCustomText[0] && onAvatarSelect) {
        await onAvatarSelect(debouncedCustomText[0]);
      }
    };

    updateAvatar();
  }, [debouncedCustomText, onAvatarSelect]);

  const handlePatternSelect = async (pattern: string) => {
    setSelectedPattern(pattern);
    setCustomText(""); // Clear custom text
    if (onAvatarSelect) {
      await onAvatarSelect(pattern);
    }
    playSound("typing");
  };

  const handleCustomTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newText = e.target.value.slice(0, MAX_AVATAR_LENGTH);
    setCustomText(newText);
    setSelectedPattern(newText);
    // API call deferred to debounced effect
  };

  const displayedAvatar = selectedPattern;

  return (
    <Card className="border-text">
      <CardHeader>
        <CardTitle className="text-glow">ASCII Avatar Generator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4">
          <div className="space-y-2">
            <Label>Predefined Patterns</Label>
            <div className="grid grid-cols-2 gap-2">
              {DEFAULT_ASCII_PATTERNS.map((pattern) => (
                <Button
                  key={pattern}
                  variant={selectedPattern === pattern ? "default" : "outline"}
                  onClick={() => handlePatternSelect(pattern)}
                  className="h-auto py-4 font-mono"
                >
                  {pattern}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Custom ASCII Art</Label>
            <Input
              value={customText}
              onChange={handleCustomTextChange}
              placeholder="Enter your custom ASCII art..."
              className="font-mono"
              maxLength={MAX_AVATAR_LENGTH}
            />
            <p className="text-sm text-muted-foreground">
              {customText.length}/{MAX_AVATAR_LENGTH} characters
            </p>
          </div>

          <div className="border rounded p-4 bg-muted">
            <Label>Preview</Label>
            <div className="mt-2 text-center font-mono text-xl whitespace-pre overflow-x-auto">
              {displayedAvatar}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}