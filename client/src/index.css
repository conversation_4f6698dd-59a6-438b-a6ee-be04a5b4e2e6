@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 10%;
    --foreground: 32.7 100% 49.6%;

    --card: 0 0% 10%;
    --card-foreground: 32.7 100% 49.6%;

    --popover: 0 0% 10%;
    --popover-foreground: 32.7 100% 49.6%;

    --primary: 32.7 100% 49.6%;
    --primary-foreground: 0 0% 10%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 32.7 100% 49.6%;

    --muted: 0 0% 15%;
    --muted-foreground: 32.7 100% 40%;

    --accent: 32.7 100% 49.6%;
    --accent-foreground: 0 0% 10%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 32.7 100% 49.6%;
    --input: 32.7 100% 49.6%;
    --ring: 32.7 100% 49.6%;

    --radius: 0rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Retro Error Animation Styles */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.animate-blink {
  animation: blink 1s step-end infinite;
}

.text-glow {
  text-shadow: 0 0 10px hsl(var(--primary));
}

/* Retro error message styles */
.retro-error {
  font-family: monospace;
  letter-spacing: 0.1em;
  line-height: 1.5;
}

.retro-border {
  border: 2px solid hsl(var(--primary));
  box-shadow: 0 0 10px hsl(var(--primary));
}

/* Smooth page transitions */
.page-transition {
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
}

.page-fade-in {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.2s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, 
    hsl(var(--primary) / 0.1) 25%, 
    hsl(var(--primary) / 0.2) 50%, 
    hsl(var(--primary) / 0.1) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}