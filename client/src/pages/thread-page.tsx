import { useRoute } from "wouter";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import { ThreadView } from "@/components/forum/thread-view";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { Thread } from "@shared/schema";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { Link } from "wouter";

interface ThreadWithDetails extends Thread {
  authorName: string;
}

export default function ThreadPage() {
  const [, params] = useRoute("/thread/:id");
  const threadId = params?.id ? parseInt(params.id) : undefined;
  const { user } = useAuth();

  const { data: thread, isLoading, error, isError } = useQuery<ThreadWithDetails>({
    queryKey: [`/api/threads/${threadId}`],
    enabled: !!threadId,
    retry: 1,
    staleTime: 30000,
    refetchOnWindowFocus: false,
    refetchOnMount: 'always'
  });

  if (!threadId) {
    return (
      <TerminalLayout>
        <div className="p-6">
          <div className="text-text font-mono">Thread not found</div>
        </div>
      </TerminalLayout>
    );
  }

  if (isLoading) {
    return (
      <TerminalLayout>
        <div className="p-6">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <Link href="/">
                <Button variant="outline" size="sm">
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Back to Threads
                </Button>
              </Link>
            </div>
            
            {/* Thread skeleton */}
            <div className="border border-primary/40 p-6 space-y-4">
              <div className="space-y-2">
                <div className="h-4 bg-primary/20 rounded w-24 animate-pulse"></div>
                <div className="h-8 bg-primary/20 rounded w-3/4 animate-pulse"></div>
                <div className="flex items-center gap-2">
                  <div className="h-4 bg-primary/20 rounded w-16 animate-pulse"></div>
                  <div className="h-4 bg-primary/20 rounded w-20 animate-pulse"></div>
                  <div className="h-4 bg-primary/20 rounded w-24 animate-pulse"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-4 bg-primary/20 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-primary/20 rounded w-5/6 animate-pulse"></div>
                <div className="h-4 bg-primary/20 rounded w-4/6 animate-pulse"></div>
              </div>
            </div>
            
            <div className="text-text font-mono text-center text-sm">Loading thread...</div>
          </div>
        </div>
      </TerminalLayout>
    );
  }

  if (isError) {
    return (
      <TerminalLayout>
        <div className="p-6">
          <div className="space-y-4">
            <Link href="/">
              <Button variant="outline" size="sm">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back to Threads
              </Button>
            </Link>
            <div className="text-text font-mono border border-destructive p-4">
              Error loading thread (ID: {threadId})
              <pre className="mt-2 text-sm text-destructive/80">
                {error instanceof Error ? error.message : 'Unknown error occurred'}
              </pre>
            </div>
          </div>
        </div>
      </TerminalLayout>
    );
  }

  if (!thread) {
    return (
      <TerminalLayout>
        <div className="p-6">
          <div className="space-y-4">
            <Link href="/">
              <Button variant="outline" size="sm">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back to Threads
              </Button>
            </Link>
            <div className="text-text font-mono border border-text p-4">
              Thread not found (ID: {threadId})
            </div>
          </div>
        </div>
      </TerminalLayout>
    );
  }

  return (
    <TerminalLayout>
      <div className="p-6">
        <ThreadView threadId={threadId} thread={thread} />
      </div>
    </TerminalLayout>
  );
}