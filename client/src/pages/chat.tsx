import React, { useState, useEffect, useCallback } from "react";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import { useAuth } from "@/hooks/use-auth";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useSoundEffects } from "@/lib/sounds";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Smile } from "lucide-react";
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

interface ChatMessage {
  id: number;
  userId: number;
  roomId: string;
  content: string;
  createdAt: Date;
  username: string;
}

interface Room {
  id: string;
  name: string;
  description: string;
}

const DEFAULT_ROOMS = [
  { 
    id: 'general', 
    name: 'General', 
    description: 'General discussion'
  },
  { 
    id: 'tech', 
    name: 'Tech Talk', 
    description: 'Technology discussions'
  },
  { 
    id: 'retro', 
    name: 'Retro Computing', 
    description: 'Vintage computer chat'
  },
  { 
    id: 'gaming', 
    name: 'Gaming', 
    description: 'Gaming discussions'
  }
];

export default function ChatPage() {
  const { user } = useAuth();
  const { playSound } = useSoundEffects();
  const queryClient = useQueryClient();

  const [newMessage, setNewMessage] = useState("");
  const [selectedRoom, setSelectedRoom] = useState<string>('general');
  const [rooms] = useState<Room[]>(DEFAULT_ROOMS);

  // Query for fetching messages
  const { data: messages = [] } = useQuery<(ChatMessage & { username: string })[]>({
    queryKey: ['/api/chat/messages', selectedRoom],
    refetchInterval: 3000, // Refetch every 3 seconds
    queryFn: async () => {
      const response = await fetch(`/api/chat/messages/${selectedRoom}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }
      return response.json();
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
    if (e.target.value.length % 2 === 0) {
      playSound("typing");
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      await apiRequest('POST', '/api/chat/messages', {
        content: newMessage.trim(),
        roomId: selectedRoom
      });

      setNewMessage("");
      playSound("message-sent");
      queryClient.invalidateQueries({ queryKey: ['/api/chat/messages', selectedRoom] });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const addEmoji = (emoji: any) => {
    setNewMessage(prev => prev + emoji.native);
    playSound("typing");
  };

  return (
    <TerminalLayout>
      <div className="grid grid-cols-4 gap-4">
        <Card className="col-span-1 border-text">
          <CardHeader>
            <CardTitle className="text-glow">Rooms</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {rooms.map((room) => (
              <Button
                key={room.id}
                variant={selectedRoom === room.id ? "default" : "outline"}
                className="w-full"
                onClick={() => {
                  setSelectedRoom(room.id);
                  playSound("typing");
                }}
              >
                {room.name}
              </Button>
            ))}
          </CardContent>
        </Card>

        <div className="col-span-3 space-y-4">
          <Card className="border-text h-[500px] flex flex-col">
            <CardHeader>
              <CardTitle className="text-glow">
                {rooms.find(r => r.id === selectedRoom)?.name || 'Chat Room'}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto space-y-2">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "p-2 rounded max-w-[80%] break-words",
                    message.userId === user?.id
                      ? "ml-auto bg-primary text-primary-foreground"
                      : "bg-muted"
                  )}
                >
                  <div className="flex justify-between items-start">
                    <p className="text-sm font-semibold">{message.username}</p>
                    <span className="text-xs font-medium text-text opacity-80">
                      {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                    </span>
                  </div>
                  <p>{message.content}</p>
                </div>
              ))}
            </CardContent>
            <div className="p-4 flex gap-2">
              <div className="flex-1 flex gap-2">
                <Input
                  value={newMessage}
                  onChange={handleInputChange}
                  placeholder="Type a message..."
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                />
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="icon">
                      <Smile className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0">
                    <Picker data={data} onEmojiSelect={addEmoji} theme="dark" />
                  </PopoverContent>
                </Popover>
              </div>
              <Button onClick={sendMessage}>Send</Button>
            </div>
          </Card>
        </div>
      </div>
    </TerminalLayout>
  );
}