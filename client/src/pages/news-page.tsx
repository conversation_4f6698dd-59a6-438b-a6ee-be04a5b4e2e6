import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { PlusCircle, Calendar, User, Edit, Trash2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useState } from "react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON>it<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";

interface News {
  id: number;
  title: string;
  content: string;
  authorId: number;
  authorName: string;
  isPublished: boolean;
  createdAt: string;
  publishedAt?: string;
}

export default function NewsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingNews, setEditingNews] = useState<News | null>(null);

  // Form state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isPublished, setIsPublished] = useState(false);

  // Fetch news articles
  const { data: newsArticles = [], isLoading } = useQuery<News[]>({
    queryKey: ['/api/news'],
  });

  // Create news mutation
  const createNewsMutation = useMutation({
    mutationFn: async (newsData: { title: string; content: string; isPublished: boolean }) => {
      const response = await apiRequest("POST", "/api/news", newsData);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.error || 'Failed to create news article');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/news'] });
      resetForm();
      setShowCreateForm(false);
      toast({
        title: "Success",
        description: "News article created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create news article",
        variant: "destructive",
      });
    },
  });

  // Update news mutation
  const updateNewsMutation = useMutation({
    mutationFn: async ({ id, ...newsData }: { id: number; title: string; content: string; isPublished: boolean }) => {
      const response = await apiRequest("PUT", `/api/news/${id}`, newsData);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.error || 'Failed to update news article');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/news'] });
      resetForm();
      setEditingNews(null);
      setShowCreateForm(false);
      toast({
        title: "Success",
        description: "News article updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update news article",
        variant: "destructive",
      });
    },
  });

  // Delete news mutation
  const deleteNewsMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/news/${id}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.error || 'Failed to delete news article');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/news'] });
      toast({
        title: "Success",
        description: "News article deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete news article",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setTitle("");
    setContent("");
    setIsPublished(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    const newsData = { title: title.trim(), content: content.trim(), isPublished };

    if (editingNews) {
      updateNewsMutation.mutate({ id: editingNews.id, ...newsData });
    } else {
      createNewsMutation.mutate(newsData);
    }
  };

  const startEdit = (news: News) => {
    setEditingNews(news);
    setTitle(news.title);
    setContent(news.content);
    setIsPublished(news.isPublished);
  };

  const cancelEdit = () => {
    setEditingNews(null);
    resetForm();
  };

  if (!user) {
    return (
      <TerminalLayout>
        <div className="p-6">
          <div className="text-center">
            <div className="text-text font-mono">Access denied</div>
          </div>
        </div>
      </TerminalLayout>
    );
  }

  return (
    <TerminalLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-glow">Fresh News</h1>
            <p className="text-muted-foreground mt-2">
              Latest updates and announcements from the website
            </p>
          </div>
          {user.isAdmin && (
            <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
              <DialogTrigger asChild>
                <Button onClick={() => { resetForm(); setEditingNews(null); }}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create News
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {editingNews ? 'Edit News Article' : 'Create News Article'}
                  </DialogTitle>
                  <p className="text-sm text-muted-foreground">
                    Admin-only feature: Create and manage official news announcements for all users.
                  </p>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Enter news title..."
                      maxLength={200}
                    />
                  </div>
                  <div>
                    <Label htmlFor="content">Content</Label>
                    <Textarea
                      id="content"
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="Enter news content..."
                      className="min-h-[200px]"
                      maxLength={5000}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="publish"
                      checked={isPublished}
                      onCheckedChange={setIsPublished}
                    />
                    <Label htmlFor="publish">Publish immediately and notify all users</Label>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        if (editingNews) {
                          cancelEdit();
                        } else {
                          setShowCreateForm(false);
                        }
                        resetForm();
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={createNewsMutation.isPending || updateNewsMutation.isPending}
                    >
                      {editingNews ? 'Update' : 'Create'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {isLoading ? (
          <div className="text-center py-8">Loading news...</div>
        ) : newsArticles.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-muted-foreground">No news articles yet.</p>
              {user.isAdmin && (
                <p className="text-sm text-muted-foreground mt-2">
                  Be the first to share some news!
                </p>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {newsArticles.map((article) => (
              <Card key={article.id} className="border border-border">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2">{article.title}</CardTitle>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {article.authorName}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDistanceToNow(new Date(article.createdAt), { addSuffix: true })}
                        </div>
                        <Badge variant={article.isPublished ? "default" : "secondary"}>
                          {article.isPublished ? "Published" : "Draft"}
                        </Badge>
                      </div>
                    </div>
                    {user.isAdmin && (
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            startEdit(article);
                            setShowCreateForm(true);
                          }}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteNewsMutation.mutate(article.id)}
                          disabled={deleteNewsMutation.isPending}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <p className="whitespace-pre-wrap text-foreground">{article.content}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </TerminalLayout>
  );
}