import { TerminalLayout } from "@/components/layout/terminal-layout";
import { DBMetrics } from "@/components/admin/db-metrics";
import { TestRunner } from "@/components/admin/test-runner";
import { useAuth } from "@/hooks/use-auth";
import { RetroError } from "@/components/ui/retro-error";
import { useLocation } from "wouter";
import { useEffect, useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AdminLogs } from "@/components/admin/admin-logs";

interface User {
  id: number;
  username: string;
  isAdmin: boolean;
  suspendedUntil: string | null;
  bannedAt: string | null;
}

export default function AdminPage() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [suspensionDate, setSuspensionDate] = useState("");
  const [actionReason, setActionReason] = useState("");

  useEffect(() => {
    if (user && !user.isAdmin) {
      setLocation('/');
    }
  }, [user, setLocation]);

  const { data: users, refetch: refetchUsers } = useQuery({
    queryKey: ['/api/admin/users'],
    queryFn: async () => {
      const response = await fetch('/api/admin/users', {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      return response.json() as Promise<User[]>;
    },
    enabled: !!user?.isAdmin,
    refetchInterval: 5000, // Auto-refresh every 5 seconds
    refetchIntervalInBackground: true // Continue refreshing when tab is not active
  });

  const toggleAdminMutation = useMutation({
    mutationFn: async ({ userId, isAdmin }: { userId: number; isAdmin: boolean }) => {
      const response = await apiRequest('POST', '/api/admin/users/toggle-admin', {
        userId,
        isAdmin,
        adminId: user?.id // Add adminId
      });
      if (!response.ok) {
        throw new Error('Failed to update user admin status');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUsers();
      toast({
        title: "Success",
        description: "User admin status updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const suspendUserMutation = useMutation({
    mutationFn: async ({ userId, until, reason }: { userId: number; until: string; reason: string }) => {
      if (!until || new Date(until) <= new Date()) {
        throw new Error('Please select a future date for suspension');
      }

      const response = await apiRequest('POST', `/api/admin/users/${userId}/suspend`, {
        until: new Date(until).toISOString(),
        reason,
        adminId: user?.id // Add adminId
      });
      if (!response.ok) {
        throw new Error('Failed to suspend user');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUsers();
      toast({
        title: "Success",
        description: "User suspended successfully",
      });
      setSelectedUser(null);
      setActionReason("");
      setSuspensionDate("");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const banUserMutation = useMutation({
    mutationFn: async ({ userId, reason }: { userId: number; reason: string }) => {
      const response = await apiRequest('POST', `/api/admin/users/${userId}/ban`, {
        reason,
        adminId: user?.id // Add adminId
      });
      if (!response.ok) {
        throw new Error('Failed to ban user');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUsers();
      toast({
        title: "Success",
        description: "User banned successfully",
      });
      setSelectedUser(null);
      setActionReason("");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const unsuspendUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest('POST', `/api/admin/users/${userId}/unsuspend`, {
        adminId: user?.id // Add adminId
      });
      if (!response.ok) {
        throw new Error('Failed to unsuspend user');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUsers();
      toast({
        title: "Success",
        description: "User unsuspended successfully",
      });
    }
  });

  const unbanUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest('POST', `/api/admin/users/${userId}/unban`, {
        adminId: user?.id // Add adminId
      });
      if (!response.ok) {
        throw new Error('Failed to unban user');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUsers();
      toast({
        title: "Success",
        description: "User unbanned successfully",
      });
    }
  });

  if (!user?.isAdmin) {
    return (
      <TerminalLayout>
        <RetroError
          error="ACCESS DENIED: Administrator privileges required."
          variant="destructive"
        />
      </TerminalLayout>
    );
  }

  return (
    <TerminalLayout>
      <div className="space-y-6 p-6">
        <div className="grid gap-4">
          <h2 className="text-2xl font-bold tracking-tight">Admin Dashboard</h2>

          <div className="grid gap-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Database Metrics</h3>
              <DBMetrics />
            </div>

            <div>
              <AdminLogs />
            </div>
            
            <div>
              <TestRunner />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">User Management</h3>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Username</TableHead>
                      <TableHead>Admin Status</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users?.map((u) => (
                      <TableRow key={u.id}>
                        <TableCell>{u.id}</TableCell>
                        <TableCell>{u.username}</TableCell>
                        <TableCell>
                          <Switch
                            checked={u.isAdmin}
                            onCheckedChange={(checked) => {
                              toggleAdminMutation.mutate({
                                userId: u.id,
                                isAdmin: checked
                              });
                            }}
                            disabled={toggleAdminMutation.isPending || u.id === user.id}
                          />
                        </TableCell>
                        <TableCell>
                          {u.bannedAt ? (
                            <span className="text-red-500">Banned</span>
                          ) : u.suspendedUntil && new Date(u.suspendedUntil) > new Date() ? (
                            <span className="text-yellow-500">Suspended</span>
                          ) : (
                            <span className="text-green-500">Active</span>
                          )}
                        </TableCell>
                        <TableCell className="space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setLocation(`/admin/users/${u.id}`)}
                          >
                            View Details
                          </Button>

                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedUser(u)}
                                disabled={u.bannedAt !== null || u.id === user.id}
                              >
                                Suspend
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Suspend User</DialogTitle>
                                <DialogDescription>
                                  Set a suspension duration and reason for {u.username}
                                </DialogDescription>
                              </DialogHeader>
                              <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                  <label>Suspend Until</label>
                                  <Input
                                    type="datetime-local"
                                    value={suspensionDate}
                                    onChange={(e) => setSuspensionDate(e.target.value)}
                                  />
                                </div>
                                <div className="grid gap-2">
                                  <label>Reason</label>
                                  <Textarea
                                    value={actionReason}
                                    onChange={(e) => setActionReason(e.target.value)}
                                  />
                                </div>
                              </div>
                              <DialogFooter>
                                <Button
                                  onClick={() => {
                                    if (selectedUser) {
                                      suspendUserMutation.mutate({
                                        userId: selectedUser.id,
                                        until: suspensionDate,
                                        reason: actionReason
                                      });
                                    }
                                  }}
                                >
                                  Suspend User
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => setSelectedUser(u)}
                                disabled={u.id === user.id}
                              >
                                Ban
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Ban User</DialogTitle>
                                <DialogDescription>
                                  Permanently ban {u.username} from the platform
                                </DialogDescription>
                              </DialogHeader>
                              <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                  <label>Reason</label>
                                  <Textarea
                                    value={actionReason}
                                    onChange={(e) => setActionReason(e.target.value)}
                                  />
                                </div>
                              </div>
                              <DialogFooter>
                                <Button
                                  variant="destructive"
                                  onClick={() => {
                                    if (selectedUser) {
                                      banUserMutation.mutate({
                                        userId: selectedUser.id,
                                        reason: actionReason
                                      });
                                    }
                                  }}
                                >
                                  Ban User
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          {u.suspendedUntil && new Date(u.suspendedUntil) > new Date() && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => unsuspendUserMutation.mutate(u.id)}
                            >
                              Unsuspend
                            </Button>
                          )}

                          {u.bannedAt && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => unbanUserMutation.mutate(u.id)}
                            >
                              Unban
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TerminalLayout>
  );
}