import { TerminalLayout } from "@/components/layout/terminal-layout";
import { useAuth } from "@/hooks/use-auth";
import { RetroError } from "@/components/ui/retro-error";
import { useLocation, useParams } from "wouter";
import { useEffect } from "react";
import { UserDetails } from "@/components/admin/user-details";

export default function AdminUserDetailsPage() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const params = useParams<{ id: string }>();
  const userId = parseInt(params.id);

  useEffect(() => {
    if (user && !user.isAdmin) {
      setLocation('/');
    }
  }, [user, setLocation]);

  if (!user?.isAdmin) {
    return (
      <TerminalLayout>
        <RetroError
          error="ACCESS DENIED: Administrator privileges required."
          variant="destructive"
        />
      </TerminalLayout>
    );
  }

  if (isNaN(userId)) {
    return (
      <TerminalLayout>
        <RetroError
          error="Invalid user ID"
          variant="destructive"
        />
      </TerminalLayout>
    );
  }

  return (
    <TerminalLayout>
      <div className="space-y-6 p-6">
        <UserDetails userId={userId} />
      </div>
    </TerminalLayout>
  );
}
