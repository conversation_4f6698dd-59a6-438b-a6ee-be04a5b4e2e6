import React, { useState, useEffect, useCallback, useRef } from "react";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import { useAuth } from "@/hooks/use-auth";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Shield, Lock, Unlock, Loader2, Users, MessageCircle } from "lucide-react";
import { useSoundEffects } from "@/lib/sounds";
import { useToast } from "@/hooks/use-toast";
import { RetroError } from "@/components/ui/retro-error";
import { useQuery, useMutation, useQueryClient, UseQueryOptions } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { EncryptionStatus } from "@/components/encryption/EncryptionStatus";

interface Message {
  id: number;
  senderId: number;
  recipientId: number;
  content: string;
  createdAt: string;
  senderName: string;
  nonce?: string;
  isEncrypted?: boolean;
}

interface User {
  id: number;
  username: string;
  autoEncryptionEnabled?: boolean;
}

export default function MessagesPage() {
  const { user } = useAuth();
  const { playSound } = useSoundEffects();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [newMessage, setNewMessage] = useState("");
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastNotifiedMessageIds, setLastNotifiedMessageIds] = useState<Set<number>>(new Set());
  const [encryptionEnabled, setEncryptionEnabled] = useState(true);
  const [lastMessageTimestamp, setLastMessageTimestamp] = useState(0);

  // Fetch friends only (not all users)
  const { data: users = [] } = useQuery<User[]>({
    queryKey: ['/api/friends'],
    onError: () => {
      setError("SYSTEM FAILURE: Unable to establish connection to friends database.");
      playSound("error");
    }
  } as UseQueryOptions<User[]>);

  // Fetch all messages for notification
  const { data: allMessages = [] } = useQuery<Message[]>({
    queryKey: ['/api/messages'],
    refetchInterval: 10000,
    enabled: !!user,
    onSuccess: (messages: Message[]) => {
      if (!user) return;

      const unreadMessages = messages.filter(
        (msg: Message) => msg.recipientId === user.id && msg.senderId !== user.id
      );

      const newUnreadMessages = unreadMessages.filter(
        (msg: Message) => !lastNotifiedMessageIds.has(msg.id)
      );

      if (newUnreadMessages.length > 0) {
        playSound("message-received");
        setLastNotifiedMessageIds((prev) => {
          const newSet = new Set(prev);
          newUnreadMessages.forEach((msg: Message) => newSet.add(msg.id));
          return newSet;
        });
      }
    },
    onError: () => {
      setError("COMMUNICATION ERROR: Message retrieval protocol failed.");
      playSound("error");
    }
  } as UseQueryOptions<Message[]>);

  // Track the most recent message ID to detect new messages
  const [lastMessageId, setLastMessageId] = useState<number>(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);
  
  // Fetch selected user messages with real-time updates
  const { data: messages = [], isLoading: isLoadingMessages } = useQuery<Message[]>({
    queryKey: ['/api/messages', selectedUser],
    queryFn: async () => {
      if (!selectedUser) return [];
      const res = await apiRequest('GET', `/api/messages/${selectedUser}`);
      const data = await res.json();
      
      // Sort messages by timestamp
      const sortedMessages = data.sort((a: Message, b: Message) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
      
      // Check for new messages
      const newestMessageId = sortedMessages.length > 0 
        ? Math.max(...sortedMessages.map((msg: Message) => msg.id)) 
        : 0;
      
      // If there's a new message and it's not from us, play a sound
      if (newestMessageId > lastMessageId && lastMessageId !== 0) {
        const newestMessage = sortedMessages.find((msg: Message) => msg.id === newestMessageId);
        if (newestMessage && newestMessage.senderId !== user?.id) {
          playSound("message-received");
          setTimeout(scrollToBottom, 100);
        }
      }
      
      // Update the last message ID
      if (newestMessageId > 0) {
        setLastMessageId(newestMessageId);
      }
      
      return sortedMessages;
    },
    refetchInterval: 2000,
    staleTime: 0,
    enabled: !!selectedUser && !!user,
    onSuccess: () => {
      if (lastMessageId === 0) {
        setTimeout(scrollToBottom, 100);
      }
    },
    onError: () => {
      setError("TRANSMISSION ERROR: Unable to receive messages from selected user.");
      playSound("error");
    }
  } as UseQueryOptions<Message[]>);

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async () => {
      if (!selectedUser || !newMessage.trim()) return;

      const messageData = {
        recipientId: selectedUser,
        content: newMessage.trim(),
        isEncrypted: encryptionEnabled && user?.autoEncryptionEnabled
      };

      const response = await apiRequest('POST', '/api/messages', messageData);
      return response.json();
    },
    onSuccess: () => {
      setNewMessage("");
      playSound("message-sent");
      
      queryClient.invalidateQueries({ queryKey: ['/api/messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      
      setTimeout(scrollToBottom, 100);
    },
    onError: (error: any) => {
      console.error('Send message error:', error);
      setError("TRANSMISSION FAILED: Message could not be delivered. Please try again.");
      playSound("error");
    }
  });

  // Auto-scroll effect
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  if (error) {
    return (
      <TerminalLayout>
        <RetroError 
          error={error} 
          onClose={() => setError(null)}
        />
      </TerminalLayout>
    );
  }

  return (
    <TerminalLayout>
      <div className="grid grid-cols-4 gap-4 h-full">
        <Card className="border-text">
          <CardHeader>
            <CardTitle className="text-glow">Friends</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {users.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Users className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">No friends yet</p>
                <p className="text-xs">Add friends to start messaging</p>
              </div>
            ) : (
              (users as User[]).map((u: User) => (
                <Button
                  key={u.id}
                  variant={selectedUser === u.id ? "default" : "outline"}
                  className="w-full"
                  onClick={() => {
                    setLastMessageId(0);
                    setSelectedUser(u.id);
                    playSound("typing");
                  }}
                >
                  {u.username}
                </Button>
              ))
            )}
          </CardContent>
        </Card>

        <div className="col-span-3 space-y-4">
          <EncryptionStatus />
          
          <Card className="border-text h-[500px] flex flex-col">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-glow">Messages</CardTitle>
              {user?.autoEncryptionEnabled && (
                <div className="flex items-center space-x-2">
                  <TooltipProvider>
                    <Tooltip delayDuration={300}>
                      <TooltipTrigger asChild>
                        <Switch
                          id="encryption-toggle"
                          checked={encryptionEnabled}
                          onCheckedChange={setEncryptionEnabled}
                          className="cursor-help"
                        />
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <p>Toggle end-to-end encryption. When disabled, messages will be sent as plaintext.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <div className="flex items-center">
                    {encryptionEnabled && (
                      <TooltipProvider>
                        <Tooltip delayDuration={300}>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="flex items-center gap-1 bg-green-900/20 cursor-help">
                              <Lock className="h-3 w-3" />
                              Encrypted
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent side="bottom" className="max-w-xs">
                            <p>Messages are encrypted end-to-end. Only you and the recipient can read them.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    {!encryptionEnabled && (
                      <TooltipProvider>
                        <Tooltip delayDuration={300}>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="flex items-center gap-1 bg-red-900/20 cursor-help">
                              <Unlock className="h-3 w-3" />
                              Unencrypted
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent side="bottom" className="max-w-xs">
                            <p>Messages are not encrypted. Anyone with access to the database can read them.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>
              )}
            </CardHeader>
            <CardContent className="flex-1 overflow-hidden flex flex-col">
              {selectedUser ? (
                <>
                  <div className="flex-1 overflow-y-auto space-y-2 mb-4">
                    {isLoadingMessages ? (
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading messages...
                      </div>
                    ) : messages.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        No messages yet. Start a conversation!
                      </div>
                    ) : (
                      messages.map((msg: Message) => (
                        <div
                          key={msg.id}
                          className={cn(
                            "p-3 rounded max-w-[80%]",
                            msg.senderId === user?.id
                              ? "bg-primary text-primary-foreground ml-auto"
                              : "bg-muted"
                          )}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs font-medium">
                              {msg.senderId === user?.id ? "You" : msg.senderName}
                            </span>
                            <div className="flex items-center gap-2">
                              {msg.isEncrypted && (
                                <Badge variant="outline" className="text-xs bg-green-900/20">
                                  <Shield className="h-2 w-2 mr-1" />
                                  E2EE
                                </Badge>
                              )}
                              <span className="text-xs opacity-70">
                                {formatDistanceToNow(new Date(msg.createdAt), { addSuffix: true })}
                              </span>
                            </div>
                          </div>
                          <div className="text-sm">{msg.content}</div>
                        </div>
                      ))
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                  
                  <div className="flex gap-2">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message..."
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          const now = Date.now();
                          if (now - lastMessageTimestamp < 1000) {
                            return;
                          }
                          
                          setLastMessageTimestamp(now);
                          sendMessageMutation.mutate();
                        }
                      }}
                      disabled={sendMessageMutation.isPending}
                    />
                    <Button 
                      onClick={() => {
                        const now = Date.now();
                        if (now - lastMessageTimestamp < 1000) {
                          return;
                        }
                        
                        setLastMessageTimestamp(now);
                        sendMessageMutation.mutate();
                      }} 
                      disabled={sendMessageMutation.isPending}
                    >
                      {sendMessageMutation.isPending ? "Sending..." : "Send"}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-muted-foreground text-center p-6">
                  <MessageCircle className="h-12 w-12 mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Select a Friend to Message</h3>
                  <p className="text-sm">You can only send messages to your friends. Add friends from the Friends page to start chatting.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </TerminalLayout>
  );
}