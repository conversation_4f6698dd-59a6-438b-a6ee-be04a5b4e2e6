import { useQuery, useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { AvatarDisplay } from "@/components/ui/avatar-display";
import { useLocation } from "wouter";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import {
  Users,
  UserPlus,
  UserCheck,
  UserX,
  Check,
  X,
  MessageCircle,
  Clock,
  User,
  ChevronLeft
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface FriendRequest {
  id: number;
  requesterId: number;
  receiverId: number;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
  requesterName: string;
  receiverName: string;
}

interface Friend {
  id: number;
  username: string;
  avatar?: string;
  isAdmin: boolean;
}

interface AllUser {
  id: number;
  username: string;
  avatar?: string;
  isAdmin: boolean;
  bio?: string;
  joinedAt?: string;
  threadsCreated?: number;
  repliesPosted?: number;
}

export default function FriendsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("friends");

  // Fetch friend requests
  const { data: requests = [], isLoading: requestsLoading } = useQuery<FriendRequest[]>({
    queryKey: ['/api/friends/requests'],
    enabled: !!user,
  });

  // Fetch friends
  const { data: friends = [], isLoading: friendsLoading } = useQuery<Friend[]>({
    queryKey: ['/api/friends'],
    enabled: !!user,
  });

  // Fetch all users 
  const { data: allUsers = [], isLoading: allUsersLoading } = useQuery<AllUser[]>({
    queryKey: ['/api/users'],
    enabled: !!user,
  });

  // Send friend request mutation
  const sendFriendRequest = useMutation({
    mutationFn: async (receiverId: number) => {
      const response = await apiRequest("POST", "/api/friends/request", {
        receiverId
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send friend request');
      }
      return response.json();
    },
    onSuccess: (_, receiverId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/friends/requests'] });
      queryClient.invalidateQueries({ queryKey: ['/api/friends'] });
      
      const targetUser = allUsers.find(u => u.id === receiverId);
      toast({
        title: "Friend Request Sent",
        description: `Friend request sent to ${targetUser?.username}`,
      });
    },
    onError: (error: Error) => {
      const isAlreadyExists = error.message.includes("already exists");
      toast({
        title: isAlreadyExists ? "Request Already Sent" : "Error",
        description: isAlreadyExists 
          ? "You've already sent a friend request to this user"
          : error.message,
        variant: isAlreadyExists ? "default" : "destructive",
      });
    },
  });

  // Respond to friend request mutation
  const respondToRequest = useMutation({
    mutationFn: async ({ requestId, accept }: { requestId: number; accept: boolean }) => {
      const response = await apiRequest("POST", "/api/friends/respond", {
        requestId,
        accept
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to respond to friend request');
      }
      return response.json();
    },
    onSuccess: (_, { accept, requestId }) => {
      queryClient.invalidateQueries({ queryKey: ['/api/friends/requests'] });
      queryClient.invalidateQueries({ queryKey: ['/api/friends'] });
      
      const request = requests.find(r => r.id === requestId);
      toast({
        title: accept ? "Friend Request Accepted" : "Friend Request Declined",
        description: accept 
          ? `You are now friends with ${request?.requesterName}`
          : `Friend request from ${request?.requesterName} declined`,
      });
    },
    onError: (error: Error) => {
      const isAlreadyExists = error.message.includes("already exists");
      toast({
        title: isAlreadyExists ? "Request Already Sent" : "Error",
        description: isAlreadyExists 
          ? "You've already sent a friend request to this user"
          : error.message,
        variant: isAlreadyExists ? "default" : "destructive",
      });
    },
  });

  const pendingRequests = requests.filter(r => 
    r.status === 'pending' && r.receiverId === user?.id
  );

  const sentRequests = requests.filter(r => 
    r.status === 'pending' && r.requesterId === user?.id
  );

  if (!user) {
    return (
      <TerminalLayout>
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6 text-center">
              <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-xl font-semibold mb-2">Please Log In</h2>
              <p className="text-muted-foreground">
                You need to be logged in to view your friends.
              </p>
            </CardContent>
          </Card>
        </div>
      </TerminalLayout>
    );
  }

  return (
    <TerminalLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Friends
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="friends" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Friends ({friends.length})
            </TabsTrigger>
            <TabsTrigger value="all-users" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              All Users ({allUsers.length})
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pending ({pendingRequests.length})
            </TabsTrigger>
            <TabsTrigger value="sent" className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Sent ({sentRequests.length})
            </TabsTrigger>
          </TabsList>

          {/* Friends List */}
          <TabsContent value="friends" className="space-y-4">
            {friendsLoading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-muted rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
                          <div className="h-3 bg-muted rounded w-1/3"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : friends.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Friends Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Start exploring the forum and send friend requests to connect with other users.
                  </p>
                  <Button onClick={() => setLocation("/")}>
                    Explore Forum
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 space-y-4">
                  {friends.map((friend, index) => (
                    <div key={friend.id}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <AvatarDisplay 
                            avatar={friend.avatar} 
                            size="md"
                            fallback={friend.username.slice(0, 2).toUpperCase()}
                          />
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{friend.username}</h4>
                              {friend.isAdmin && (
                                <Badge variant="destructive" className="text-xs">
                                  Admin
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">Friend</p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setLocation(`/profile/${friend.id}`)}
                          >
                            <User className="h-4 w-4 mr-2" />
                            Profile
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setLocation(`/messages?user=${friend.id}`)}
                          >
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Message
                          </Button>
                        </div>
                      </div>
                      {index < friends.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* All Users */}
          <TabsContent value="all-users" className="space-y-4">
            {allUsersLoading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-muted rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
                          <div className="h-3 bg-muted rounded w-1/2 mb-1"></div>
                          <div className="h-3 bg-muted rounded w-1/3"></div>
                        </div>
                        <div className="flex gap-2">
                          <div className="h-8 w-20 bg-muted rounded"></div>
                          <div className="h-8 w-20 bg-muted rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : allUsers.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Users Found</h3>
                  <p className="text-muted-foreground">
                    No other users are registered yet.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 space-y-4">
                  {allUsers.map((user, index) => {
                    const isFriend = friends.some(f => f.id === user.id);
                    const hasPendingRequest = [...pendingRequests, ...sentRequests].some(
                      r => r.requesterId === user.id || r.receiverId === user.id
                    );
                    
                    return (
                      <div key={user.id}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <AvatarDisplay 
                              avatar={user.avatar} 
                              size="md"
                              fallback={user.username.slice(0, 2).toUpperCase()}
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{user.username}</h4>
                                {user.isAdmin && (
                                  <Badge variant="destructive" className="text-xs">
                                    Admin
                                  </Badge>
                                )}
                                {isFriend && (
                                  <Badge variant="secondary" className="text-xs">
                                    Friend
                                  </Badge>
                                )}
                                {hasPendingRequest && (
                                  <Badge variant="outline" className="text-xs">
                                    Request Pending
                                  </Badge>
                                )}
                              </div>
                              {user.bio && (
                                <p className="text-sm text-muted-foreground mt-1 truncate max-w-md">
                                  {user.bio}
                                </p>
                              )}
                              <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                                {user.threadsCreated !== undefined && (
                                  <span>{user.threadsCreated} threads</span>
                                )}
                                {user.repliesPosted !== undefined && (
                                  <span>{user.repliesPosted} replies</span>
                                )}
                                {user.joinedAt && (
                                  <span>Joined {formatDistanceToNow(new Date(user.joinedAt), { addSuffix: true })}</span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setLocation(`/profile/${user.id}`)}
                            >
                              <User className="h-4 w-4 mr-2" />
                              Profile
                            </Button>
                            {!isFriend && !hasPendingRequest && (
                              <Button
                                size="sm"
                                onClick={() => sendFriendRequest.mutate(user.id)}
                                disabled={sendFriendRequest.isPending}
                              >
                                <UserPlus className="h-4 w-4 mr-2" />
                                Add Friend
                              </Button>
                            )}
                            {isFriend && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setLocation(`/messages?user=${user.id}`)}
                              >
                                <MessageCircle className="h-4 w-4 mr-2" />
                                Message
                              </Button>
                            )}
                          </div>
                        </div>
                        {index < allUsers.length - 1 && <Separator className="mt-4" />}
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Pending Requests */}
          <TabsContent value="pending" className="space-y-4">
            {requestsLoading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    {[...Array(2)].map((_, i) => (
                      <div key={i} className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-muted rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
                          <div className="h-3 bg-muted rounded w-1/3"></div>
                        </div>
                        <div className="flex gap-2">
                          <div className="h-8 w-16 bg-muted rounded"></div>
                          <div className="h-8 w-16 bg-muted rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : pendingRequests.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Pending Requests</h3>
                  <p className="text-muted-foreground">
                    You don't have any pending friend requests at the moment.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 space-y-4">
                  {pendingRequests.map((request, index) => (
                    <div key={request.id}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <AvatarDisplay 
                            avatar=""
                            size="md"
                            fallback={request.requesterName.slice(0, 2).toUpperCase()}
                          />
                          <div>
                            <h4 className="font-medium">{request.requesterName}</h4>
                            <p className="text-sm text-muted-foreground">
                              Sent {formatDistanceToNow(new Date(request.createdAt), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => respondToRequest.mutate({ requestId: request.id, accept: true })}
                            disabled={respondToRequest.isPending}
                          >
                            <Check className="h-4 w-4 mr-2" />
                            Accept
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => respondToRequest.mutate({ requestId: request.id, accept: false })}
                            disabled={respondToRequest.isPending}
                          >
                            <X className="h-4 w-4 mr-2" />
                            Decline
                          </Button>
                        </div>
                      </div>
                      {index < pendingRequests.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Sent Requests */}
          <TabsContent value="sent" className="space-y-4">
            {requestsLoading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    {[...Array(2)].map((_, i) => (
                      <div key={i} className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-muted rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
                          <div className="h-3 bg-muted rounded w-1/3"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : sentRequests.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <UserPlus className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Sent Requests</h3>
                  <p className="text-muted-foreground">
                    You haven't sent any friend requests yet.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 space-y-4">
                  {sentRequests.map((request, index) => (
                    <div key={request.id}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <AvatarDisplay 
                            avatar=""
                            size="md"
                            fallback={request.receiverName.slice(0, 2).toUpperCase()}
                          />
                          <div>
                            <h4 className="font-medium">{request.receiverName}</h4>
                            <p className="text-sm text-muted-foreground">
                              Sent {formatDistanceToNow(new Date(request.createdAt), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Pending
                        </Badge>
                      </div>
                      {index < sentRequests.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </TerminalLayout>
  );
}