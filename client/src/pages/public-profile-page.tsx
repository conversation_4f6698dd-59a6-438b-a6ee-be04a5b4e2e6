import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { AvatarDisplay } from "@/components/ui/avatar-display";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import { formatDistanceToNow } from "date-fns";
import { 
  User, 
  UserPlus, 
  UserCheck, 
  UserX, 
  Shield, 
  Calendar, 
  MessageCircle,
  FileText,
  Heart,
  ArrowLeft
} from "lucide-react";

interface PublicProfile {
  id: number;
  username: string;
  bio: string;
  avatar: string;
  joinedAt: string;
  threadsCreated: number;
  repliesPosted: number;
  isAdmin: boolean;
}

interface FriendshipStatus {
  isFriend: boolean;
  requestStatus?: string;
}

export default function PublicProfilePage() {
  const { userId } = useParams();
  const [, setLocation] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();

  const userIdNum = parseInt(userId || "0");
  const isOwnProfile = user?.id === userIdNum;

  // Fetch public profile
  const { data: profile, isLoading: profileLoading } = useQuery<PublicProfile>({
    queryKey: [`/api/users/${userIdNum}/profile`],
    enabled: !!userIdNum,
  });

  // Check friendship status
  const { data: friendship, isLoading: friendshipLoading } = useQuery<FriendshipStatus>({
    queryKey: [`/api/friends/check/${userIdNum}`],
    enabled: !!userIdNum && !isOwnProfile,
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/friends/check/${userIdNum}`);
      if (!response.ok) {
        throw new Error('Failed to check friendship status');
      }
      return response.json();
    },
  });

  // Send friend request mutation
  const sendFriendRequest = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/friends/request", {
        receiverId: userIdNum
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send friend request');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/friends/check/${userIdNum}`] });
      toast({
        title: "Friend Request Sent",
        description: `Friend request sent to ${profile?.username}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  if (profileLoading) {
    return (
      <TerminalLayout>
        <div className="space-y-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  <div className="w-20 h-20 bg-muted rounded-full"></div>
                  <div className="flex-1 space-y-4">
                    <div className="h-6 bg-muted rounded w-1/4"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-4 bg-muted rounded w-1/2"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </TerminalLayout>
    );
  }

  if (!profile) {
    return (
      <TerminalLayout>
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6 text-center">
              <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-xl font-semibold mb-2">User Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The user you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={() => setLocation("/friends")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Friends
              </Button>
            </CardContent>
          </Card>
        </div>
      </TerminalLayout>
    );
  }

  const getFriendshipButton = () => {
    if (isOwnProfile) return null;

    if (friendshipLoading) {
      return (
        <Button disabled>
          <User className="h-4 w-4 mr-2" />
          Loading...
        </Button>
      );
    }

    if (friendship?.isFriend) {
      return (
        <Button variant="outline" disabled>
          <UserCheck className="h-4 w-4 mr-2" />
          Friends
        </Button>
      );
    }

    if (friendship?.requestStatus === 'pending') {
      return (
        <Button variant="outline" disabled>
          <UserX className="h-4 w-4 mr-2" />
          Request Pending
        </Button>
      );
    }

    return (
      <Button 
        onClick={() => sendFriendRequest.mutate()}
        disabled={sendFriendRequest.isPending}
      >
        <UserPlus className="h-4 w-4 mr-2" />
        {sendFriendRequest.isPending ? "Sending..." : "Add Friend"}
      </Button>
    );
  };

  return (
    <TerminalLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setLocation("/friends")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Friends
          </Button>
        </div>

        {/* Profile Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-6">
              <AvatarDisplay 
                avatar={profile.avatar} 
                size="xl"
                className="w-20 h-20"
                fallback={profile.username?.slice(0, 2).toUpperCase() || "??"}
              />
              
              <div className="flex-1 space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <h1 className="text-2xl font-bold">{profile.username}</h1>
                      {profile.isAdmin && (
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <Shield className="h-3 w-3" />
                          Admin
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 mr-2" />
                      Joined {profile.joinedAt ? formatDistanceToNow(new Date(profile.joinedAt), { addSuffix: true }) : "unknown"}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {getFriendshipButton()}
                    {!isOwnProfile && (
                      <Button 
                        variant="outline"
                        onClick={() => setLocation(`/messages?user=${profile.id}`)}
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Message
                      </Button>
                    )}
                  </div>
                </div>

                {/* Bio */}
                {profile.bio && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">About</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {profile.bio}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{profile.threadsCreated ?? 0}</div>
              <div className="text-sm text-muted-foreground">Threads Created</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <MessageCircle className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{profile.repliesPosted ?? 0}</div>
              <div className="text-sm text-muted-foreground">Replies Posted</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <Heart className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">
                {(profile.threadsCreated ?? 0) + (profile.repliesPosted ?? 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Contributions</div>
            </CardContent>
          </Card>
        </div>

        {isOwnProfile && (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-muted-foreground mb-4">
                This is how your profile appears to other users.
              </p>
              <Button onClick={() => setLocation("/profile")}>
                Edit Profile
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </TerminalLayout>
  );
}