import { TerminalLayout } from "@/components/layout/terminal-layout";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { ImageAvatarUploader } from "@/components/profile/image-avatar-uploader";
import { TerminalThemeSwitcher } from "@/components/ui/terminal-theme-switcher";
import { VisualEffectsSettings } from "@/components/ui/visual-effects-settings";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useLocation } from "wouter";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  User, 
  Calendar, 
  MessageSquare, 
  FileText, 
  Activity, 
  Shield,
  Clock,
  Key
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { AvatarDisplay } from "@/components/ui/avatar-display";
import { BioEditor } from "@/components/profile/bio-editor";

// Password change schema
const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type PasswordChangeForm = z.infer<typeof passwordChangeSchema>;

// Account deletion schema
const deleteAccountSchema = z.object({
  password: z.string().min(1, "Password is required"),
});

type DeleteAccountForm = z.infer<typeof deleteAccountSchema>;

// Define interfaces for profile data
interface UserStats {
  threadsCreated: number;
  repliesPosted: number;
  messagesReceived: number;
  messagesSent: number;
  lastLoginAt: string | null;
  joinedAt: string;
}

interface ActivityItem {
  id: number;
  type: string;
  description: string;
  timestamp: string;
  ip?: string;
}

interface SecurityInfo {
  lastPasswordChange: string;
  publicKey: string;
  autoEncryptionEnabled: boolean;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [currentAvatar, setCurrentAvatar] = useState<string>("");
  const [isUpdatingAvatar, setIsUpdatingAvatar] = useState(false);

  // Fetch current avatar
  const { data: avatarData, refetch: refetchAvatar } = useQuery({
    queryKey: ['/api/user/avatar'],
    queryFn: async () => {
      const response = await fetch('/api/user/avatar', {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch avatar: ${response.status}`);
      }
      return response.json();
    },
    retry: 1,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true
  });

  // Fetch user statistics
  const { data: userStats, isLoading: statsLoading } = useQuery<UserStats>({
    queryKey: ['/api/user/stats'],
    enabled: !!user,
  });

  // Fetch activity timeline
  const { data: activityData, isLoading: activityLoading } = useQuery<ActivityItem[]>({
    queryKey: ['/api/user/activity'],
    enabled: !!user,
  });

  // Fetch security information
  const { data: securityInfo, isLoading: securityLoading } = useQuery<SecurityInfo>({
    queryKey: ['/api/user/security'],
    enabled: !!user,
  });

  useEffect(() => {
    if (avatarData?.avatar) {
      setCurrentAvatar(avatarData.avatar);
    }
  }, [avatarData]);

  // Handle avatar update with loading state
  const handleAvatarUpdate = async (newAvatar: string) => {
    setIsUpdatingAvatar(true);
    try {
      const response = await fetch('/api/user/avatar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ avatar: newAvatar })
      });

      if (!response.ok) {
        throw new Error("Failed to update avatar");
      }

      setCurrentAvatar(newAvatar);
      await refetchAvatar();
      toast({
        title: "Avatar Updated",
        description: "Your avatar has been saved successfully!",
      });
    } catch (error: any) {
      toast({
        title: "Update Failed",
        description: "Could not update your avatar. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingAvatar(false);
    }
  };

  // Password change form
  const passwordForm = useForm<PasswordChangeForm>({
    resolver: zodResolver(passwordChangeSchema),
  });

  // Delete account form
  const deleteForm = useForm<DeleteAccountForm>({
    resolver: zodResolver(deleteAccountSchema),
  });

  // Password change mutation
  const passwordChangeMutation = useMutation({
    mutationFn: async (data: PasswordChangeForm) => {
      const res = await apiRequest("POST", "/api/user/change-password", {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Password Changed",
        description: "Your password has been successfully updated.",
      });
      passwordForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete account mutation
  const deleteAccountMutation = useMutation({
    mutationFn: async (data: DeleteAccountForm) => {
      const res = await apiRequest("POST", "/api/user/delete", {
        password: data.password,
      });
      if (!res.ok) {
        const errorText = await res.text();
        try {
          const errorJson = JSON.parse(errorText);
          throw new Error(errorJson.message || "Failed to delete account");
        } catch {
          throw new Error("Failed to delete account. Please try again.");
        }
      }
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Account Deleted",
        description: "Your account has been successfully deleted.",
      });
      setLocation("/auth");
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onPasswordChange = async (data: PasswordChangeForm) => {
    await passwordChangeMutation.mutate(data);
  };

  const onDeleteAccount = async (data: DeleteAccountForm) => {
    await deleteAccountMutation.mutate(data);
  };

  return (
    <TerminalLayout>
      <div className="space-y-6">
        {/* Profile Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-6">
              <div className="text-center space-y-2">
                <AvatarDisplay 
                  avatar={currentAvatar} 
                  size="xl"
                  className="w-20 h-20"
                  fallback="?_?"
                />
                {isUpdatingAvatar && <span className="text-xs text-muted-foreground">Updating...</span>}
              </div>
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-3">
                  <h2 className="text-2xl font-bold">{user?.username}</h2>
                  {user?.isAdmin && (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      Admin
                    </Badge>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                  <div>User ID: #{user?.id}</div>
                  <div>
                    Joined: {userStats?.joinedAt ? 
                      formatDistanceToNow(new Date(userStats.joinedAt), { addSuffix: true }) 
                      : 'Unknown'}
                  </div>
                  <div>
                    Last Login: {userStats?.lastLoginAt ? 
                      formatDistanceToNow(new Date(userStats.lastLoginAt), { addSuffix: true }) 
                      : 'Never'}
                  </div>
                  <div className="flex items-center gap-1">
                    <Key className="h-3 w-3" />
                    E2E Encryption: {securityInfo?.autoEncryptionEnabled ? 'Enabled' : 'Disabled'}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Overview */}
        {!statsLoading && userStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-primary" />
                  <div>
                    <div className="text-2xl font-bold">{userStats.threadsCreated}</div>
                    <div className="text-xs text-muted-foreground">Threads Created</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-primary" />
                  <div>
                    <div className="text-2xl font-bold">{userStats.repliesPosted}</div>
                    <div className="text-xs text-muted-foreground">Replies Posted</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="text-2xl font-bold">{userStats.messagesSent}</div>
                    <div className="text-xs text-muted-foreground">Messages Sent</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="text-2xl font-bold">{userStats.messagesReceived}</div>
                    <div className="text-xs text-muted-foreground">Messages Received</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Tabbed Content */}
        <Tabs defaultValue="settings" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="customization">Customization</TabsTrigger>
          </TabsList>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <BioEditor currentBio={user?.bio ?? ""} userId={user?.id || 0} />

            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>Update your account password</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={passwordForm.handleSubmit(onPasswordChange)} className="space-y-4">
                  <div className="space-y-2">
                    <Label>Current Password</Label>
                    <Input type="password" {...passwordForm.register("currentPassword")} />
                    {passwordForm.formState.errors.currentPassword && (
                      <p className="text-red-500 text-sm">
                        {passwordForm.formState.errors.currentPassword.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>New Password</Label>
                    <Input type="password" {...passwordForm.register("newPassword")} />
                    {passwordForm.formState.errors.newPassword && (
                      <p className="text-red-500 text-sm">
                        {passwordForm.formState.errors.newPassword.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Confirm New Password</Label>
                    <Input type="password" {...passwordForm.register("confirmPassword")} />
                    {passwordForm.formState.errors.confirmPassword && (
                      <p className="text-red-500 text-sm">
                        {passwordForm.formState.errors.confirmPassword.message}
                      </p>
                    )}
                  </div>

                  <Button type="submit" disabled={passwordChangeMutation.isPending}>
                    {passwordChangeMutation.isPending ? "Changing..." : "Change Password"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card className="border-destructive">
              <CardHeader>
                <CardTitle className="text-destructive">Delete Account</CardTitle>
                <CardDescription>
                  Permanently delete your account and all associated data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive">Delete Account</Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete your account
                        and remove all of your data from our servers.
                      </AlertDialogDescription>
                    </AlertDialogHeader>

                    <form onSubmit={deleteForm.handleSubmit(onDeleteAccount)} className="space-y-4 mt-4">
                      <div className="space-y-2">
                        <Label>Confirm your password</Label>
                        <Input type="password" {...deleteForm.register("password")} />
                        {deleteForm.formState.errors.password && (
                          <p className="text-red-500 text-sm">
                            {deleteForm.formState.errors.password.message}
                          </p>
                        )}
                      </div>

                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          type="submit"
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          disabled={deleteAccountMutation.isPending}
                        >
                          {deleteAccountMutation.isPending ? "Deleting..." : "Delete Account"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </form>
                  </AlertDialogContent>
                </AlertDialog>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Information
                </CardTitle>
                <CardDescription>
                  Your account security details and encryption settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {securityLoading ? (
                  <div className="space-y-2">
                    <div className="h-4 bg-muted animate-pulse rounded" />
                    <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                  </div>
                ) : securityInfo ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span className="text-sm">Last Password Change</span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {formatDistanceToNow(new Date(securityInfo.lastPasswordChange), { addSuffix: true })}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        <span className="text-sm">Auto Encryption</span>
                      </div>
                      <Badge variant={securityInfo.autoEncryptionEnabled ? "default" : "secondary"}>
                        {securityInfo.autoEncryptionEnabled ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">Public Key (E2E Encryption)</Label>
                      <div className="p-3 bg-muted/30 rounded-lg font-mono text-xs break-all">
                        {securityInfo.publicKey}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">Unable to load security information</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Your recent actions and account events
                </CardDescription>
              </CardHeader>
              <CardContent>
                {activityLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-muted animate-pulse rounded-full" />
                        <div className="space-y-1 flex-1">
                          <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                          <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : activityData && activityData.length > 0 ? (
                  <ScrollArea className="h-96">
                    <div className="space-y-3">
                      {activityData.map((activity, index) => (
                        <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-muted/20">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                          <div className="flex-1 space-y-1">
                            <div className="text-sm">{activity.description}</div>
                            <div className="text-xs text-muted-foreground flex items-center gap-2">
                              <span>{formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}</span>
                              {activity.ip && <span>• IP: {activity.ip}</span>}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent activity found
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Customization Tab */}
          <TabsContent value="customization" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Terminal Theme</CardTitle>
                <CardDescription>Customize your retro terminal experience</CardDescription>
              </CardHeader>
              <CardContent>
                <TerminalThemeSwitcher />
              </CardContent>
            </Card>

            <VisualEffectsSettings />

            <ImageAvatarUploader currentAvatar={currentAvatar} onAvatarUpdate={handleAvatarUpdate} />
          </TabsContent>
        </Tabs>
      </div>
    </TerminalLayout>
  );
}