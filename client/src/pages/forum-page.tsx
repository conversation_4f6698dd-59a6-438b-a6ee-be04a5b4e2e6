import { useQuery } from '@tanstack/react-query';
import { TerminalLayout } from '@/components/layout/terminal-layout';
import { ForumThread } from '@/components/forum/forum-thread';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Link } from 'wouter';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { Thread, threadCategoryEnum } from '@shared/schema';
import { useState } from 'react';

const CATEGORY_LABELS: Record<typeof threadCategoryEnum.enumValues[number], string> = {
  GENERAL: "General Discussion",
  TECHNICAL_SUPPORT: "Technical Support",
  AI_NEWS: "AI News",
  GLOBAL_NEWS: "Global News",
  FUNNY: "Fun & Memes"
};

interface ThreadWithDetails extends Omit<Thread, 'createdAt'> {
  authorName: string;
  replyCount: number;
  voteCount: number;
  createdAt: string;
  category: typeof threadCategoryEnum.enumValues[number]; // Make category non-nullable
}

export default function ForumPage() {
  const [selectedCategory, setSelectedCategory] = useState<typeof threadCategoryEnum.enumValues[number] | 'ALL'>('ALL');

  const { data: threads, isLoading } = useQuery<ThreadWithDetails[]>({
    queryKey: ['/api/threads'],
    refetchInterval: 6000, // Auto-refresh every 6 seconds
    refetchIntervalInBackground: true // Continue refreshing when tab is not active
  });

  const filteredThreads = threads?.filter(thread => 
    selectedCategory === 'ALL' || thread.category === selectedCategory
  );

  return (
    <TerminalLayout>
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-mono text-primary">Active Threads</h1>
            <Select 
              value={selectedCategory} 
              onValueChange={(value) => setSelectedCategory(value as typeof threadCategoryEnum.enumValues[number] | 'ALL')}
            >
              <SelectTrigger className="w-[200px] font-mono">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Categories</SelectItem>
                {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Link href="/new-thread">
            <Button size="sm" className="font-mono">
              <Plus className="h-4 w-4 mr-2" />
              New Thread
            </Button>
          </Link>
        </div>

        <ScrollArea className="h-[calc(100vh-12rem)]">
          <div className="space-y-4">
            {isLoading ? (
              <div className="text-muted-foreground font-mono animate-pulse p-4 border border-primary/40">
                Loading threads...
              </div>
            ) : !filteredThreads?.length ? (
              <div className="text-muted-foreground font-mono p-4 border border-primary/40">
                {selectedCategory === 'ALL' 
                  ? 'No threads yet. Be the first to create one!'
                  : `No threads in ${CATEGORY_LABELS[selectedCategory]} category yet.`}
              </div>
            ) : (
              filteredThreads.map((thread) => (
                <ForumThread
                  key={thread.id}
                  id={thread.id}
                  title={thread.title}
                  authorName={thread.authorName}
                  authorId={thread.authorId}
                  createdAt={thread.createdAt}
                  category={thread.category}
                  replyCount={thread.replyCount}
                  voteCount={thread.voteCount}
                />
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </TerminalLayout>
  );
}