import React, { useEffect, useState, useCallback } from "react";
import { useLocation } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TerminalLayout } from "@/components/layout/terminal-layout";
import { useSoundEffects } from "@/lib/sounds";
import { ContentFilterDialog } from "@/components/ui/content-filter-dialog";
import { insertThreadSchema, type InsertThread } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { ArrowLeft, Bold, Italic, List, Save, Trash2, SmilePlus } from "lucide-react";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

const CATEGORIES = [
  { value: "GENERAL", label: "General Discussion" },
  { value: "TECHNICAL_SUPPORT", label: "Technical Support" },
  { value: "AI_NEWS", label: "AI News" },
  { value: "GLOBAL_NEWS", label: "Global News" },
  { value: "FUNNY", label: "Fun & Memes" }
] as const;

// Custom hook for content filter error handling
const useContentFilter = () => {
  const [isContentFilterError, setIsContentFilterError] = useState(false);
  const [contentFilterErrorMessage, setContentFilterErrorMessage] = useState('');

  // Function to detect if an error is related to content moderation
  const isContentModerationError = (error: any): boolean => {
    if (!error) return false;
    
    // Check error message content
    const errorMsg = error.message || error.error || (typeof error === 'string' ? error : '');
    
    return (
      errorMsg.includes('community guidelines') || 
      errorMsg.includes('content moderation') || 
      errorMsg.includes('Content Moderation') ||
      errorMsg.includes('inappropriate') ||
      errorMsg.toLowerCase().includes('offensive')
    );
  };

  const handleContentFilterError = (errorData: any) => {
    setIsContentFilterError(true);
    
    // Extract the appropriate message
    let message = "Content violates community guidelines.";
    
    if (errorData) {
      if (typeof errorData === 'string') {
        message = errorData;
      } else if (errorData.message) {
        message = errorData.message;
      } else if (errorData.error) {
        message = errorData.error;
      }
    }
    
    setContentFilterErrorMessage(message);
    
    // Prevent the error from propagating to the global error handler
    if (window.onerror) {
      const originalOnError = window.onerror;
      window.onerror = (msg, src, line, col, err) => {
        if (isContentModerationError(err)) {
          return true; // Prevents the error from propagating
        }
        return originalOnError(msg, src, line, col, err);
      };
      
      // Restore after a short delay
      setTimeout(() => {
        window.onerror = originalOnError;
      }, 1000);
    }
  };

  const ContentFilterDialogComponent = () => {
    return (
      <ContentFilterDialog 
        isOpen={isContentFilterError} 
        onClose={() => setIsContentFilterError(false)}
        message={contentFilterErrorMessage}
      />
    );
  };

  return { handleContentFilterError, isContentModerationError, ContentFilterDialogComponent };
};


export default function NewThreadPage() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const { playSound } = useSoundEffects();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isDraft, setIsDraft] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { handleContentFilterError, ContentFilterDialogComponent } = useContentFilter();

  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'Write your thread content here...',
      }),
    ],
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      form.setValue('content', content, {
        shouldValidate: true,
        shouldDirty: true
      });
    },
  });

  const form = useForm<InsertThread>({
    resolver: zodResolver(insertThreadSchema),
    defaultValues: {
      title: "",
      content: "",
      category: "GENERAL"
    },
  });

  // Load draft on mount
  useEffect(() => {
    const draft = localStorage.getItem('thread-draft');
    if (draft) {
      const parsedDraft = JSON.parse(draft);
      form.setValue('title', parsedDraft.title);
      form.setValue('category', parsedDraft.category);
      editor?.commands.setContent(parsedDraft.content);
      form.setValue('content', parsedDraft.content);
      setIsDraft(true);
    }
  }, [editor]);

  // Handle page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (form.formState.isDirty) {
        e.preventDefault();
        e.returnValue = '';
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [form.formState.isDirty]);

  const saveDraft = () => {
    const draft = {
      title: form.getValues('title'),
      category: form.getValues('category'),
      content: editor?.getHTML() || '',
    };
    localStorage.setItem('thread-draft', JSON.stringify(draft));
    setIsDraft(true);
    playSound("message-sent");
    toast({
      title: "Draft Saved",
      description: "Your thread has been saved as a draft",
    });
  };

  const clearDraft = () => {
    localStorage.removeItem('thread-draft');
    form.reset();
    editor?.commands.clearContent();
    setIsDraft(false);
    playSound("typing");
    toast({
      title: "Draft Cleared",
      description: "Your draft has been cleared",
    });
  };

  const onSubmit = async (formData: InsertThread) => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      const content = editor?.getHTML() || '';

      if (!content.trim()) {
        toast({
          title: "Error",
          description: "Content cannot be empty",
          variant: "destructive",
        });
        return;
      }

      // Ensure all required fields are present
      const threadData = {
        title: formData.title.trim(),
        category: formData.category,
        content: content
      };

      // Log submission attempt (clean format)
      console.log(`Creating thread: "${threadData.title}" in ${threadData.category}`);

      const response = await apiRequest("POST", "/api/threads", threadData);
      const data = await response.json();

      if (!response.ok) {
        if (response.status === 422 && data.type === "content_filter") {
          handleContentFilterError(data);
          return; // Exit early to prevent further processing
        } else {
          throw new Error(data.message || 'Failed to create thread');
        }
      }

      clearDraft();
      playSound("message-sent");
      toast({
        title: "Success",
        description: "Thread created successfully",
      });
      setLocation("/forum");
    } catch (error) {
      // Clean error logging - just the message, not the full object
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error("Thread creation failed:", errorMessage);
      playSound("error");
      
      // Handle rate limiting and other API errors properly
      if (error instanceof Error) {
        if (error.message.includes("wait") && error.message.includes("seconds")) {
          // This is a rate limiting error
          toast({
            title: "Please wait",
            description: error.message,
            variant: "destructive",
          });
        } else if (error.message.includes("content") && error.message.includes("filter")) {
          // This is a content filter error
          handleContentFilterError(error);
        } else {
          // Generic error
          toast({
            title: "Error",
            description: error.message || "Failed to create thread",
            variant: "destructive",
          });
        }
      } else {
        // Fallback for unknown error types
        toast({
          title: "Error",
          description: "Failed to create thread",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <TerminalLayout>
      <ContentFilterDialogComponent />

      <div className="max-w-2xl mx-auto">
        <Button
          variant="outline"
          className="mb-4"
          onClick={() => {
            playSound("typing");
            setLocation("/forum");
          }}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Forum
        </Button>

        <Card className="border-text">
          <CardHeader>
            <CardTitle className="text-glow">Create New Thread</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input {...field} className="terminal-input" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {CATEGORIES.map((category) => (
                              <SelectItem
                                key={category.value}
                                value={category.value}
                              >
                                {category.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => editor?.chain().focus().toggleBold().run()}
                      className={editor?.isActive('bold') ? 'bg-primary text-primary-foreground' : ''}
                    >
                      <Bold className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => editor?.chain().focus().toggleItalic().run()}
                      className={editor?.isActive('italic') ? 'bg-primary text-primary-foreground' : ''}
                    >
                      <Italic className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => editor?.chain().focus().toggleBulletList().run()}
                      className={editor?.isActive('bulletList') ? 'bg-primary text-primary-foreground' : ''}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                    <div className="relative">
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                      >
                        <SmilePlus className="h-4 w-4" />
                      </Button>
                      {showEmojiPicker && (
                        <div className="absolute right-0 mt-2 z-50">
                          <Picker
                            data={data}
                            onEmojiSelect={(emoji: any) => {
                              editor?.commands.insertContent(emoji.native);
                              setShowEmojiPicker(false);
                            }}
                            theme="dark"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Content</FormLabel>
                      <FormControl>
                        <div className="terminal-input min-h-[300px] p-3">
                          <EditorContent
                            editor={editor}
                            onBlur={() => {
                              // Ensure form field is updated when editor loses focus
                              const content = editor?.getHTML() || '';
                              field.onChange(content);
                            }}
                          />
                          {/* Hidden input to help with form validation and testing */}
                          <input
                            type="hidden"
                            name="content"
                            value={field.value || ''}
                            onChange={field.onChange}
                            data-testid="content-field"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2">
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Creating..." : "Create Thread"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={saveDraft}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Draft
                  </Button>
                  {isDraft && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={clearDraft}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Draft
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </TerminalLayout>
  );
}